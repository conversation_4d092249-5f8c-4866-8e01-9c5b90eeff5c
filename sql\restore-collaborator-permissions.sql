-- Function to restore collaborator permissions when status changes from 'inactive' to active
CREATE OR REPLACE FUNCTION restore_collaborator_permissions(p_club_id INTEGER, p_collaborator_id INTEGER)
RETURNS VOID AS $$
DECLARE
  v_user_id UUID;
  v_collaborator_status TEXT;
  v_default_permissions JSONB;
BEGIN
  -- Log the operation
  RAISE NOTICE 'Restaurando permissões do colaborador % no clube %', p_collaborator_id, p_club_id;

  -- 1. Get collaborator's user_id and current status
  SELECT user_id, status INTO v_user_id, v_collaborator_status
  FROM collaborators
  WHERE id = p_collaborator_id AND club_id = p_club_id;

  IF v_user_id IS NULL THEN
    RAISE NOTICE 'Colaborador % não encontrado ou sem usuário associado', p_collaborator_id;
    RETURN;
  END IF;

  -- 2. Define default permissions for collaborators
  v_default_permissions := '{
    "collaborators.view": true,
    "collaborators.create": false,
    "collaborators.edit": false,
    "collaborators.delete": false,
    "collaborator_finances.view": true,
    "collaborator_finances.create": false,
    "collaborator_finances.edit": false,
    "collaborator_finances.delete": false
  }'::jsonb;

  -- 3. Check if user already has permissions in club_members
  IF EXISTS (
    SELECT 1 FROM club_members 
    WHERE club_id = p_club_id AND user_id = v_user_id
  ) THEN
    -- Update existing permissions, preserving any existing ones and adding collaborator permissions
    UPDATE club_members
    SET 
      permissions = COALESCE(permissions, '{}'::jsonb) || v_default_permissions,
      updated_at = NOW()
    WHERE club_id = p_club_id AND user_id = v_user_id;
  ELSE
    -- Insert new permissions record
    INSERT INTO club_members (
      club_id, 
      user_id, 
      permissions, 
      created_at, 
      updated_at
    ) VALUES (
      p_club_id,
      v_user_id,
      v_default_permissions,
      NOW(),
      NOW()
    );
  END IF;

  -- 4. Reactivate financial data if it was deactivated
  UPDATE collaborators
  SET 
    financial_data = jsonb_set(
      COALESCE(financial_data, '{}'::jsonb),
      '{status}',
      '"active"',
      true
    ),
    updated_at = NOW()
  WHERE id = p_collaborator_id AND club_id = p_club_id
  AND (financial_data->>'status' = 'inactive' OR financial_data->>'status' IS NULL);

  -- 5. Log completion
  RAISE NOTICE 'Permissões do colaborador % restauradas com sucesso', p_collaborator_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
