import { supabase } from "@/integrations/supabase/client";
import { withPermission, withAuditLog } from "./middleware";

export type MedicalAvailability = {
  id: number;
  club_id: number;
  professional_id: number;
  date: string;
  start_time: string;
  end_time: string;
  capacity: number;
  booked: number;
  created_at: string;
  updated_at: string;
};

export const MEDICAL_AVAILABILITY_PERMISSIONS = {
  VIEW: "medical.availability.view",
  CREATE: "medical.availability.create",
  EDIT: "medical.availability.edit",
  DELETE: "medical.availability.delete",
};

export async function getMedicalAvailability(
  clubId: number,
  professionalId: number,
  startDate?: string,
  endDate?: string
): Promise<MedicalAvailability[]> {
  let query = supabase
    .from("medical_availability")
    .select("*")
    .eq("club_id", clubId)
    .eq("professional_id", professionalId);

  if (startDate) query = query.gte("date", startDate);
  if (endDate) query = query.lte("date", endDate);

  const { data, error } = await query.order("date").order("start_time");
  if (error) {
    throw new Error(`Error fetching availability: ${error.message}`);
  }
  return data || [];
}

export async function createMedicalAvailability(
  clubId: number,
  userId: string,
  availability: Omit<MedicalAvailability, "id" | "club_id" | "booked" | "created_at" | "updated_at">
): Promise<MedicalAvailability> {
  return withPermission(clubId, userId, MEDICAL_AVAILABILITY_PERMISSIONS.CREATE, () =>
    withAuditLog(clubId, userId, "medical.availability.create", availability, async () => {
      const { data, error } = await supabase
        .from("medical_availability")
        .insert({ club_id: clubId, ...availability })
        .select()
        .single();
      if (error) {
        throw new Error(`Error creating availability: ${error.message}`);
      }
      return data as MedicalAvailability;
    })
  );
}

export async function updateMedicalAvailability(
  clubId: number,
  userId: string,
  id: number,
  availability: Partial<Omit<MedicalAvailability, "id" | "club_id" | "created_at" | "updated_at" | "booked">>
): Promise<MedicalAvailability> {
  return withPermission(clubId, userId, MEDICAL_AVAILABILITY_PERMISSIONS.EDIT, () =>
    withAuditLog(clubId, userId, "medical.availability.update", { id, ...availability }, async () => {
      const { data, error } = await supabase
        .from("medical_availability")
        .update({ ...availability, updated_at: new Date().toISOString() })
        .eq("club_id", clubId)
        .eq("id", id)
        .select()
        .single();
      if (error) {
        throw new Error(`Error updating availability: ${error.message}`);
      }
      return data as MedicalAvailability;
    })
  );
}

export async function deleteMedicalAvailability(
  clubId: number,
  userId: string,
  id: number
): Promise<boolean> {
  return withPermission(clubId, userId, MEDICAL_AVAILABILITY_PERMISSIONS.DELETE, () =>
    withAuditLog(clubId, userId, "medical.availability.delete", { id }, async () => {
      const { error } = await supabase
        .from("medical_availability")
        .delete()
        .eq("club_id", clubId)
        .eq("id", id);
      if (error) {
        throw new Error(`Error deleting availability: ${error.message}`);
      }
      return true;
    })
  );
}
