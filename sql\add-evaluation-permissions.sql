-- SQL script to insert new evaluation permissions
INSERT INTO permissions (name, description, category) VALUES
  ('evaluation.tabs.players', 'Acessar aba Atletas em Pré Cadastro', 'evaluation'),
  ('evaluation.tabs.invitations', 'Acessar aba Convites', 'evaluation'),
  ('evaluation.tabs.new', 'Acessar aba Novo Convite', 'evaluation'),
  ('evaluation.tabs.dashboard', 'Acessar aba Dashboard', 'evaluation'),
  ('evaluation.invitations.create', 'Criar convites de pré cadastro', 'evaluation'),
  ('evaluation.invitations.copy', 'Copiar link de convite', 'evaluation'),
  ('evaluation.invitations.resend', 'Reenviar convite de pré cadastro', 'evaluation'),
  ('evaluation.invitations.delete', 'Excluir convites de pré cadastro', 'evaluation'),
  ('evaluation.players.edit', 'Editar atletas em pré cadastro', 'evaluation'),
  ('evaluation.players.delete', 'Excluir atletas em pré cadastro', 'evaluation'),
  ('evaluation.players.schedule', 'Agendar pré cadastro de atleta', 'evaluation'),
  ('evaluation.players.update_status', 'Atualizar status do pré cadastro', 'evaluation'),
  ('evaluation.players.verify_documents', 'Verificar documentos do pré cadastro', 'evaluation')
ON CONFLICT (name) DO NOTHING;
