/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-nocheck
import { supabase } from "@/integrations/supabase/client";
import { v4 as uuidv4 } from "uuid";
import { sendPlayerInvitation, sendWelcomeWithCredentials } from "./email";
import { signUp } from "./auth";
import { generateRandomPassword } from "@/services/brevoEmailService";
import { createUserDirectly } from "./directAuth";
import { deleteSeason } from "./seasonApi";

// Obter a URL do site a partir das variáveis de ambiente
const SITE_URL = import.meta.env.VITE_SITE_URL || "http://localhost:3000";

// Importações de módulos internos
import { Player, getPlayers, getPlayerById, createPlayer, updatePlayer, deletePlayer } from './players';
import { UpcomingMatch, MatchHistory, Gol, <PERSON>tao, getUpcomingMatches, createUpcomingMatch, deleteUpcomingMatch, getMatchHistory, createMatchHistory, updateMatchHistory, deleteMatchHistory, updateMatchEscalacao } from './matches';
import { Opponent, getOpponents, getOpponentById, createOpponent, updateOpponent, deleteOpponent } from './opponents';
import { Competition, getCompetitions, getCompetitionById, createCompetition, updateCompetition, deleteCompetition } from './competitions';
import { MatchEvent, GoalEvent, CardEvent, SubstitutionEvent, NoteEvent, getMatchEvents, createGoalEvent, createCardEvent, createSubstitutionEvent, createNoteEvent, deleteMatchEvent } from './matchEvents';
import { Accommodation, PlayerAccommodation, getAccommodations, getAccommodationById, createAccommodation, updateAccommodation, deleteAccommodation, getPlayerAccommodations, getAccommodationPlayers, assignPlayerToAccommodation, updatePlayerAccommodation, removePlayerFromAccommodation } from './accommodations';
import { MealLocation, getMealLocations, createMealLocation, updateMealLocation, deleteMealLocation } from './mealLocations';
import { CollaboratorAccommodation, getCollaboratorAccommodations, getAccommodationCollaborators, assignCollaboratorToAccommodation, updateCollaboratorAccommodation, removeCollaboratorFromAccommodation } from './collaboratorAccommodations';
import { Category, PlayerCategory, getCategories, getCategoryById, createCategory, updateCategory, deleteCategory, assignPlayerToCategory, removePlayerFromCategory, getPlayerCategories, getCategoryPlayers, migrateYouthPlayers } from './categories';
import { ClubTitle, getClubTitles, createClubTitle, updateClubTitle, deleteClubTitle } from './clubTitles';
import { CategoryLineup, getCategoryLineup, saveCategoryLineup, deleteCategoryLineup } from './categoryLineups';
import { uploadProfileImage, uploadPlayerDocument, registerPlayerDocument, verifyPlayerDocument, deletePlayerDocument, uploadMedicalCertificate, uploadExamFile, uploadSignature } from './storage';
import { uploadClubLogo } from './storage-simple';
import { Department, UserDepartment, getDepartments, createDepartment, updateDepartment, deleteDepartment, getDepartmentUsers, getUserDepartments, addUserToDepartment, removeUserFromDepartment, updateUserDepartmentRole } from './departments';
import { MedicalProfessional, MedicalProfessionalRole, MedicalProfessionalAccountOptions, getMedicalProfessionals, getMedicalProfessionalById, getMedicalProfessionalByUserId, createMedicalProfessional, updateMedicalProfessional, deleteMedicalProfessional, uploadMedicalProfessionalCertificate, createMedicalProfessionalAccount, MEDICAL_PROFESSIONAL_PERMISSIONS } from './medicalProfessionals';
import { Notification, getMedicalNotifications, markMedicalNotificationAsRead, markAllMedicalNotificationsAsRead, createMedicalNotification, deleteMedicalNotification, countUnreadMedicalNotifications } from './notifications';
import { MedicalAppointment, AppointmentStatus, getMedicalAppointments, createMedicalAppointment, updateMedicalAppointment, deleteMedicalAppointment, updateAppointmentStatus, markAppointmentNotificationSent, APPOINTMENT_PERMISSIONS } from './medicalAppointments';
import { MedicalAvailability, getMedicalAvailability, createMedicalAvailability, updateMedicalAvailability, deleteMedicalAvailability, MEDICAL_AVAILABILITY_PERMISSIONS } from './medicalAvailability';
import { MedicalTreatmentEvolution, TreatmentStatus, getTreatmentEvolutions, createTreatmentEvolution, updateTreatmentEvolution, deleteTreatmentEvolution, addTreatmentSignature, TREATMENT_PERMISSIONS } from './medicalTreatment';
import { MedicalExam, ExamStatus, getMedicalExams, getPlayerMedicalExams, createMedicalExam, updateMedicalExam, deleteMedicalExam, uploadExam, EXAM_PERMISSIONS } from './medicalExams';
import { MedicalPrescription, PrescriptionItem, PrescriptionStatus, getMedicalPrescriptions, getPlayerPrescriptions, getPrescriptionItems, createMedicalPrescription, addPrescriptionItem, updateMedicalPrescription, deleteMedicalPrescription, addPrescriptionSignature, PRESCRIPTION_PERMISSIONS } from './medicalPrescriptions';
import { BirthdayPerson, getMonthlyBirthdays } from './birthdays';
import { UserInvitation, UserPermissions, getUserPermissions, updateUserPermissions, createUserInvitation, getUserInvitationByToken, acceptUserInvitation, cancelUserInvitation, getClubInvitations, hasPermission } from './permissions';
import { PlayerDocument, DOCUMENT_TYPES, DOCUMENT_LABELS, uploadDocument, getPlayerDocuments, verifyDocument, deleteDocument, getPendingDocuments, checkRequiredDocuments } from './documents';
import { PlayerFinancialEntry, PlayerFinancialData, getPlayerFinancialData, addPlayerFinancialEntry, removePlayerFinancialEntry, calculateMonthlyBalance, calculateTotalBalance, getYearlyFinancialData } from './finances';
import { ClubUser, UserProfile, getClubUsers, getUserProfile, updateUserProfile } from './users';
import { FinancialAccount, getFinancialAccounts, createFinancialAccount, updateFinancialAccount, deleteFinancialAccount, uploadAccountReceipt } from './accounts';
import { Callup, CallupPlayer, getCallups, getCallupById, createCallup, updateCallup, deleteCallup, getCallupPlayers, addPlayerToCallup, removePlayerFromCallup, generateCallupPDF, generateCallupAccommodationPDF } from './callups';
import { AdministrativeDocument, AdministrativeTask, AdministrativeReminder, getAdministrativeDocuments, getAdministrativeDocumentById, createAdministrativeDocument, updateAdministrativeDocument, deleteAdministrativeDocument, signAdministrativeDocument, getAdministrativeTasks, createAdministrativeTask, updateAdministrativeTask, deleteAdministrativeTask, getAdministrativeReminders, createAdministrativeReminder, updateAdministrativeReminder, deleteAdministrativeReminder, generateDocumentPDF } from './administrative';
import { Collaborator, CollaboratorDocument, COLLABORATOR_DOCUMENT_TYPES, COLLABORATOR_DOCUMENT_LABELS, getCollaborators, getCollaboratorById, getCollaboratorByUserId, createCollaborator, updateCollaborator, deleteCollaborator, uploadCollaboratorDocument, getCollaboratorDocuments } from './collaborators';
import { Supplier, getSuppliers, getSupplierById, createSupplier, updateSupplier, deleteSupplier } from './suppliers';
import { COLLABORATOR_PERMISSIONS, SUPPLIER_PERMISSIONS } from '@/constants/permissions';
import { SupplierOrder, getSupplierOrders, getAllSupplierOrders, createSupplierOrder, deleteSupplierOrder } from './supplierOrders';
import { InventoryProduct, InventoryTransaction, InventoryNotificationSettings, INVENTORY_DEPARTMENTS, getInventoryProducts, getInventoryProductById, createInventoryProduct, updateInventoryProduct, deleteInventoryProduct, getInventoryTransactions, registerInventoryTransaction, getLowStockProducts, getInventoryNotificationSettings, updateInventoryNotificationSettings } from './inventory';

// Tipos
export type YouthPlayer = {
  id: string;
  club_id: number;
  name: string;
  position: string;
  age: number;
  number: number;
  nationality?: string;
  height?: number;
  weight?: number;
  birthdate?: string;
  birthplace?: string;
  status: string;
  image?: string;
  category?: string;
  potential?: number;
  since?: string;
};


export type AgendaEvent = {
  id: number;
  club_id: number;
  title: string;
  type: string;
  date: string;
  time: string;
  endTime: string;
  location: string;
  participants: string[];
  description: string;
};

export type ClubInfo = {
  id: number;
  name: string;
  founded_year?: number;
  stadium?: string;
  president?: string;
  logo_url?: string;
  logo?: string;
  address?: string;
  zip_code?: string;
  phone?: string;
  website?: string;
  email?: string;
  primary_color?: string;
  secondary_color?: string;
  contract_warning_days?: number;
};

export type Task = {
  id: number;
  club_id: number;
  title: string;
  description?: string;
  due_date?: string;
  status: string;
  assignee?: string;
  completed?: boolean;
};

export type ClubMember = {
  id: number;
  userId: string;
  clubId: number;
  role: string;
  status: string;
  created_at?: string;
  userName?: string;
  userEmail?: string;
};

export type MedicalRecord = {
  id: number;
  club_id: number;
  player_id: string;
  player?: string;
  date: string;
  description: string;
  doctor?: string;
  status?: string;
  severity?: string;
  is_referral?: boolean;
  referred_by?: string;
  doctor_id?: number;
  viewed?: boolean;
  viewed_at?: string;
  completed?: boolean;
};

export type FinancialTransaction = {
  id: number;
  club_id: number;
  date: string;
  type: string;
  category: string;
  amount: number;
  description: string;
  player_id?: string;
  player_name?: string;
  collaborator_id?: number;
  collaborator_role?: string;
  medical_professional_id?: number;
  medical_professional_role?: string;
  payment_status?: string; // 'pending' or 'paid'
  receipt_url?: string; // URL do comprovante de pagamento
};

export type Session = {
  id: number;
  club_id: number;
  player: string;
  type: string;
  date: string;
  professional: string;
  notes?: string;
};

export type Notification = {
  id: number;
  user_id: string;
  club_id?: number;
  title: string;
  description?: string;
  created_at: string;
  read: boolean;
};

// Re-exporta funções e tipos de Players para manter compatibilidade
export type { Player } from './players';
export { getPlayers, getPlayerById, createPlayer, updatePlayer, deletePlayer, uploadPlayerPhoto } from './players';

// Exporta funções e tipos de PlayerEvaluations
export type { PlayerEvaluation } from './playerEvaluations';
export {
  getPlayerEvaluation,
  getPlayerEvaluations,
  savePlayerEvaluation,
  lockPlayerEvaluation,
  updatePlayerEvaluationStatus,
  deletePlayerEvaluation,
  addDigitalSignature,
  updatePlayerEvaluationSignature,
  getPendingEvaluationsForApproval,
  approveEvaluationAsManager,
  approveEvaluationAsPresident,
  rejectEvaluation
} from './playerEvaluations';

// Exporta funções e tipos de PlayerStatistics
export type { PlayerMatchStatistics } from './playerStatistics';
export {
  getPlayerMatchStatistics,
  savePlayerMatchStatistics,
  getPlayerAggregatedStatistics,
  getPlayerMatchesWithStats
} from './playerStatistics';

// Exporta funções de sincronização de estatísticas
export {
  syncPlayerAggregatedStats,
  updatePlayerStatsFromMatch,
  initializePlayerStats
} from './playerStatsSync';

// Re-exporta funções e tipos de Matches para manter compatibilidade
export type { UpcomingMatch, MatchHistory, Gol, Cartao, MatchStats } from './matches';
export {
  getUpcomingMatches,
  createUpcomingMatch,
  deleteUpcomingMatch,
  getMatchHistory,
  createMatchHistory,
  updateMatchHistory,
  deleteMatchHistory,
  updateMatchEscalacao,
  createCallupFromMatch,
  addCategoryPlayersToCallup,
  getPlayerUpcomingMatches
} from './matches';

// Re-exporta funções e tipos de Opponents para manter compatibilidade
export type { Opponent } from './opponents';
export { getOpponents, getOpponentById, createOpponent, updateOpponent, deleteOpponent } from './opponents';

// Re-exporta funções e tipos de Competitions para manter compatibilidade
export type { Competition } from './competitions';
export { getCompetitions, getCompetitionById, createCompetition, updateCompetition, deleteCompetition } from './competitions';

// Re-exporta funções e tipos de MatchEvents para manter compatibilidade
export type { MatchEvent, GoalEvent, CardEvent, SubstitutionEvent, NoteEvent } from './matchEvents';
export { getMatchEvents, createGoalEvent, createCardEvent, createSubstitutionEvent, createNoteEvent, deleteMatchEvent } from './matchEvents';

// Re-exporta funções e tipos de Trainings para manter compatibilidade
export type { Training, PhysicalProgress, TrainingGoal, TrainingFinalization, TrainingExercise, Exercise } from './trainings';
export {
  getTrainings,
  getCompletedTrainings,
  createTraining,
  updateTraining,
  deleteTraining,
  finalizeTraining,
  getTrainingExercises,
  getExercises,
  createExercise,
  getPhysicalProgress,
  createPhysicalProgress,
  addExerciseToTraining,
  getTrainingGoals,
  createTrainingGoal,
  updateTrainingGoal,
  deleteTrainingGoal,
  getUpcomingTrainingsBySeasonAndCategory,
  getTrainingsByCategory,
  fixTrainingsWithoutPlayers,
  testTrainingPlayersPermissions,
  debugPlayerTrainings,
  fixInvalidTrainingDates,
  getPlayerTrainingsAlternative
} from './trainings';

// Re-exporta funções e tipos de Staff para manter compatibilidade
export type { Staff } from './staff';
export { getStaff, createStaff, updateStaff, deleteStaff } from './staff';

// ---- INÍCIO INTEGRAÇÃO REHAB ----
export * from "./rehabSessions";
// ---- FIM INTEGRAÇÃO REHAB ----

// Funções para Youth Players
export async function getYouthPlayers(clubId: number): Promise<YouthPlayer[]> {
  const { data, error } = await supabase
    .from("youth_players")
    .select("*")
    .eq("club_id", clubId as any);

  if (error) {
    throw new Error(`Erro ao buscar jogadores da base: ${error.message}`);
  }

  return data as unknown as YouthPlayer[];
}

export async function createYouthPlayer(clubId: number, player: Omit<YouthPlayer, "id">): Promise<YouthPlayer> {
  const newPlayer = {
    ...player,
    id: uuidv4(),
    club_id: clubId
  };

  const { data, error } = await supabase
    .from("youth_players")
    .insert(newPlayer  as any)
    .select()
    .single();

  if (error) {
    throw new Error(`Erro ao criar jogador da base: ${error.message}`);
  }

  return data as unknown as YouthPlayer;
}

export async function updateYouthPlayer(clubId: number, id: string, player: Partial<YouthPlayer>): Promise<YouthPlayer> {
  const { data, error } = await supabase
    .from("youth_players")
    .update({ ...player, club_id: clubId } as any)
    .eq("club_id", clubId as any)
    .eq("id", id as any)
    .select()
    .single();

  if (error) {
    console.error("Erro ao atualizar jogador da base:", error);
    throw new Error(`Erro ao atualizar jogador da base: ${error.message}`);
  }

  return data as unknown as YouthPlayer;
}

export async function deleteYouthPlayer(clubId: number, id: string): Promise<boolean> {
  const { error } = await supabase
    .from("youth_players")
    .delete()
    .eq("club_id", clubId as any)
    .eq("id", id as any);

  if (error) {
    console.error("Erro ao deletar jogador da base:", error);
    throw new Error(`Erro ao deletar jogador da base: ${error.message}`);
  }

  return true;
}

// Funções para Agenda Events
export async function getAgendaEvents(clubId: number): Promise<AgendaEvent[]> {
  const { data, error } = await supabase
    .from("agenda_events")
    .select("*")
    .eq("club_id", clubId as any);

  if (error) {
    console.error("Erro ao buscar eventos da agenda:", error);
    throw new Error(`Erro ao buscar eventos da agenda: ${error.message}`);
  }

  // Garantir que data é um array e não é null
  if (!data) {
    return [];
  }

  // Convertemos para o formato esperado e adicionamos tipagem explícita
  return data.map((event: any) => ({
    id: event.id,
    club_id: event.club_id || clubId,
    title: event.title,
    type: event.type || 'outro',
    date: event.date,
    time: event.date ? (() => {
      try {
        return new Date(event.date).toISOString().substring(11, 16);
      } catch {
        return '00:00';
      }
    })() : '00:00',
    endTime: event.end_time || event.date, // Usar end_time se disponível, senão usar date como fallback
    location: event.location || '',
    participants: event.participants || [],
    description: event.description || ''
  }));
}

export async function createAgendaEvent(clubId: number, event: Omit<AgendaEvent, "id">): Promise<AgendaEvent> {
  const { data, error } = await supabase
    .from("agenda_events")
    .insert({
      club_id: clubId,
      title: event.title,
      type: event.type,
      date: event.date,
      end_time: event.endTime,
      location: event.location,
      participants: event.participants,
      description: event.description
    } as any)
    .select()
    .single();

  if (error) {
    console.error("Erro ao criar evento da agenda:", error);
    throw new Error(`Erro ao criar evento da agenda: ${error.message}`);
  }

  return {
    id: data.id,
    club_id: data.club_id || clubId,
    title: data.title,
    type: data.type || 'outro',
    date: data.date,
    time: event.time,
    endTime: event.endTime,
    location: data.location || '',
    participants: data.participants || [],
    description: data.description || ''
  };
}

export async function updateAgendaEvent(clubId: number, id: number, event: Partial<AgendaEvent>): Promise<AgendaEvent> {
  const { data, error } = await supabase
    .from("agenda_events")
    .update({
      ...(event.title !== undefined && { title: event.title }),
      ...(event.type !== undefined && { type: event.type }),
      ...(event.date !== undefined && { date: event.date }),
      ...(event.endTime !== undefined && { end_time: event.endTime }),
      ...(event.location !== undefined && { location: event.location }),
      ...(event.participants !== undefined && { participants: event.participants }),
      ...(event.description !== undefined && { description: event.description })
    } as any)
    .eq("club_id", clubId as any)
    .eq("id", id as any)
    .select()
    .single();

  if (error) {
    console.error("Erro ao atualizar evento da agenda:", error);
    throw new Error(`Erro ao atualizar evento da agenda: ${error.message}`);
  }

  return {
    id: data.id,
    club_id: data.club_id || clubId,
    title: data.title,
    type: data.type || 'outro',
    date: data.date,
    time: event.time || (() => {
      try {
        return data.date ? new Date(data.date).toISOString().substring(11, 16) : '00:00';
      } catch {
        return '00:00';
      }
    })(),
    endTime: event.endTime || data.end_time || data.date,
    location: data.location || '',
    participants: data.participants || [],
    description: data.description || ''
  };
}

export async function deleteAgendaEvent(clubId: number, id: number): Promise<boolean> {
  const { error } = await supabase
    .from("agenda_events")
    .delete()
    .eq("club_id", clubId as any)
    .eq("id", id as any);

  if (error) {
    console.error("Erro ao deletar evento da agenda:", error);
    throw new Error(`Erro ao deletar evento da agenda: ${error.message}`);
  }

  return true;
}

// Funções para Club Info
export async function getClubInfo(clubId: number): Promise<ClubInfo> {
  console.log("Buscando informações do clube:", clubId);
  const { data, error } = await supabase
    .from("club_info")
    .select("*")
    .eq("id", clubId as any)
    .single();

  if (error) {
    console.error("Erro ao buscar informações do clube:", error);
    throw new Error(`Erro ao buscar informações do clube: ${error.message}`);
  }

  console.log("Informações do clube obtidas:", data);

  // Atualizar o localStorage com as informações do clube
  if (data && 'name' in data) {
    if (data.name) {
      console.log("Atualizando nome do clube no localStorage:", data.name);
      localStorage.setItem("clubName", data.name as string);
    }
    if (data.logo_url) {
      // Adicionar timestamp para evitar cache
      const timestamp = new Date().getTime();
      const logoUrl = `${data.logo_url}?t=${timestamp}`;

      console.log("Atualizando logo do clube no localStorage:", logoUrl);
      localStorage.setItem("teamLogo", logoUrl);

      // Modificar o objeto data para incluir o timestamp
      data.logo_url = logoUrl;
    }

    // Atualizar o tema com base na cor primária do clube
    if (data.primary_color) {
      console.log("Atualizando tema do clube no localStorage:", data.primary_color);
      localStorage.setItem("themeId", data.primary_color);
    }
  }

  // Adiciona compatibilidade com o campo logo no frontend
  return {
    ...data,
    logo: data.logo_url
  } as ClubInfo;
}

export async function updateClubInfo(clubId: number, clubInfo: Partial<ClubInfo>): Promise<ClubInfo> {
  // Garante compatibilidade entre logo e logo_url
  const dataToSend = { ...clubInfo };
  if (clubInfo.logo && !clubInfo.logo_url) {
    dataToSend.logo_url = clubInfo.logo;
    delete dataToSend.logo;
  }

  const { data, error } = await supabase
    .from("club_info")
    .update(dataToSend)
    .eq("id", clubId)
    .select()
    .single();

  if (error) {
    console.error("Erro ao atualizar informações do clube:", error);
    throw new Error(`Erro ao atualizar informações do clube: ${error.message}`);
  }

  // Atualizar o localStorage com as informações do clube
  if (data) {
    if (data.name) {
      localStorage.setItem("clubName", data.name);
    }
    if (data.logo_url) {
      // Adicionar timestamp para evitar cache
      const timestamp = new Date().getTime();
      const logoUrl = `${data.logo_url}?t=${timestamp}`;

      localStorage.setItem("teamLogo", logoUrl);

      // Modificar o objeto data para incluir o timestamp
      data.logo_url = logoUrl;
    }
  }

  return {
    ...data,
    logo: data.logo_url
  } as ClubInfo;
}

// Funções para Tasks
export async function getTasks(clubId: number): Promise<Task[]> {
  const { data, error } = await supabase
    .from("tasks")
    .select("*")
    .eq("club_id", clubId);

  if (error) {
    console.error("Erro ao buscar tarefas:", error);
    throw new Error(`Erro ao buscar tarefas: ${error.message}`);
  }

  return (data || []).map(task => ({
    ...task,
    assignee: task.description?.split('|')[1] || undefined,
    completed: task.status === 'concluída'
  })) as Task[];
}

export async function createTask(clubId: number, task: Omit<Task, "id">): Promise<Task> {
  const description = `${task.description || ''}|${task.assignee || ''}`;
  const status = task.completed ? 'concluída' : task.status;

  const { data, error } = await supabase
    .from("tasks")
    .insert({
      club_id: clubId,
      title: task.title,
      description: description,
      due_date: task.due_date,
      status: status
    })
    .select()
    .single();

  if (error) {
    console.error("Erro ao criar tarefa:", error);
    throw new Error(`Erro ao criar tarefa: ${error.message}`);
  }

  const assignee = data.description?.split('|')[1] || undefined;
  const taskDescription = data.description?.split('|')[0] || '';

  return {
    id: data.id,
    club_id: data.club_id,
    title: data.title,
    description: taskDescription,
    due_date: data.due_date,
    status: data.status,
    assignee,
    completed: data.status === 'concluída'
  } as Task;
}

export async function updateTask(clubId: number, id: number, task: Partial<Task>): Promise<Task> {
  // Buscamos a tarefa atual para manter os campos que não estão sendo atualizados
  const { data: existingTask, error: fetchError } = await supabase
    .from("tasks")
    .select("*")
    .eq("club_id", clubId)
    .eq("id", id)
    .single();

  if (fetchError) {
    console.error("Erro ao buscar tarefa para atualização:", fetchError);
    throw new Error(`Erro ao buscar tarefa para atualização: ${fetchError.message}`);
  }

  const existingParts = existingTask.description?.split('|') || ['', ''];
  const taskDescription = task.description !== undefined ? task.description : existingParts[0];
  const assignee = task.assignee !== undefined ? task.assignee : existingParts[1];
  const description = `${taskDescription}|${assignee}`;

  const { data, error } = await supabase
    .from("tasks")
    .update({
      title: task.title !== undefined ? task.title : existingTask.title,
      description: description,
      due_date: task.due_date !== undefined ? task.due_date : existingTask.due_date,
      status: task.status !== undefined ? task.status : existingTask.status
    })
    .eq("club_id", clubId)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    console.error("Erro ao atualizar tarefa:", error);
    throw new Error(`Erro ao atualizar tarefa: ${error.message}`);
  }

  const updatedParts = data.description?.split('|') || ['', ''];

  return {
    id: data.id,
    club_id: data.club_id,
    title: data.title,
    description: updatedParts[0],
    due_date: data.due_date,
    status: data.status,
    assignee: updatedParts[1] || undefined
  } as Task;
}

export async function deleteTask(clubId: number, id: number): Promise<boolean> {
  const { error } = await supabase
    .from("tasks")
    .delete()
    .eq("club_id", clubId)
    .eq("id", id);

  if (error) {
    console.error("Erro ao deletar tarefa:", error);
    throw new Error(`Erro ao deletar tarefa: ${error.message}`);
  }

  return true;
}

// Funções para Club Members
export async function getClubMembers(clubId: number): Promise<ClubMember[]> {
  try {
    // Garantir que clubId seja tratado como número
    const clubIdNumber = Number(clubId);

    if (isNaN(clubIdNumber)) {
      throw new Error(`ID do clube inválido: ${clubId}`);
    }

    // Primeiro, buscar os membros do clube
    const { data: members, error: membersError } = await supabase
      .from("club_members")
      .select("*")
      .eq("club_id", clubIdNumber);

    if (membersError) {
      console.error("Erro ao buscar membros do clube:", membersError);
      throw new Error(`Erro ao buscar membros do clube: ${membersError.message}`);
    }

    if (!members || members.length === 0) {
      return [];
    }

    // Extrair os IDs de usuários para buscar seus dados
    const userIds = members.map(member => member.user_id);

    // Buscar os dados dos usuários
    const { data: users, error: usersError } = await supabase
      .from("users")
      .select("id, name, email")
      .in("id", userIds);

    if (usersError) {
      console.error("Erro ao buscar dados dos usuários:", usersError);
      // Continuar mesmo com erro, apenas sem os nomes dos usuários
    }

    // Criar um mapa de usuários para facilitar o acesso
    const userMap = {};
    if (users) {
      users.forEach(user => {
        userMap[user.id] = user;
      });
    }

    // Combinar os dados
    return members.map(member => {
      const user = userMap[member.user_id];
      return {
        id: member.id,
        userId: member.user_id,
        clubId: member.club_id,
        role: member.role,
        status: member.status,
        created_at: member.created_at,
        userName: user?.name || 'Usuário sem nome',
        userEmail: user?.email || ''
      };
    }) as ClubMember[];
  } catch (error) {
    console.error("Erro ao buscar membros do clube:", error);
    if (error instanceof Error) {
      throw new Error(`Erro ao buscar membros do clube: ${error.message}`);
    } else {
      throw new Error("Erro ao buscar membros do clube");
    }
  }
}

export async function addUserToClub(userId: string, clubId: number, role: string): Promise<ClubMember> {
  try {
    console.log(`Adicionando usuário ${userId} ao clube ${clubId} com papel ${role}`);

    // Garantir que clubId seja tratado como número
    const clubIdNumber = Number(clubId);

    if (isNaN(clubIdNumber)) {
      throw new Error(`ID do clube inválido: ${clubId}`);
    }

    // Verificar se o usuário existe na tabela users
    const { data: userExists, error: userCheckError } = await supabase
      .from("users")
      .select("id")
      .eq("id", userId)
      .single();

    if (userCheckError) {
      console.error("Erro ao verificar usuário:", userCheckError);
      throw new Error(`Usuário não encontrado: ${userCheckError.message}`);
    }

    if (!userExists) {
      throw new Error(`Usuário com ID ${userId} não encontrado`);
    }

    console.log(`Usuário ${userId} encontrado, prosseguindo com a associação ao clube`);

    // Verificar se o usuário já é membro do clube
    const { data: existingMember, error: memberCheckError } = await supabase
      .from("club_members")
      .select("id")
      .eq("club_id", clubIdNumber)
      .eq("user_id", userId)
      .maybeSingle();

    if (memberCheckError) {
      console.error("Erro ao verificar associação existente:", memberCheckError);
    } else if (existingMember) {
      console.log(`Usuário ${userId} já é membro do clube ${clubId}`);
      return {
        id: existingMember.id,
        userId: userId,
        clubId: clubIdNumber,
        role: role,
        status: "ativo"
      } as ClubMember;
    }

    // Adicionar o usuário ao clube
    const { data, error } = await supabase
      .from("club_members")
      .insert({
        user_id: userId,
        club_id: clubIdNumber,
        role: role,
        status: "ativo"
      })
      .select()
      .single();

    if (error) {
      console.error("Erro ao adicionar usuário ao clube:", error);
      throw new Error(`Erro ao adicionar usuário ao clube: ${error.message}`);
    }

    console.log(`Usuário ${userId} adicionado com sucesso ao clube ${clubId}`);

    return {
      id: data.id,
      userId: data.user_id,
      clubId: data.club_id,
      role: data.role,
      status: data.status,
      created_at: data.created_at
    } as ClubMember;
  } catch (error) {
    console.error("Erro em addUserToClub:", error);
    throw error;
  }
}

// Função removeClubMember foi removida e substituída por removeUserFromClub em users.ts

export async function getUserClubs(userId: string): Promise<ClubInfo[]> {
  // Primeiro buscamos os clubes associados ao usuário
  const { data: memberships, error: membershipError } = await supabase
    .from("club_members")
    .select("club_id")
    .eq("user_id", userId);

  if (membershipError) {
    console.error("Erro ao buscar associações do usuário:", membershipError);
    throw new Error(`Erro ao buscar associações do usuário: ${membershipError.message}`);
  }

  if (!memberships || memberships.length === 0) {
    return [];
  }

  const clubIds = memberships.map(m => m.club_id);

  // Agora buscamos as informações dos clubes
  const { data: clubs, error: clubsError } = await supabase
    .from("club_info")
    .select("*")
    .in("id", clubIds);

  if (clubsError) {
    console.error("Erro ao buscar informações dos clubes:", clubsError);
    throw new Error(`Erro ao buscar informações dos clubes: ${clubsError.message}`);
  }

  return (clubs || []) as ClubInfo[];
}

// Funções para Medical Records
export async function getMedicalRecords(clubId: number): Promise<MedicalRecord[]> {
  const { data, error } = await supabase
    .from("medical_records")
    .select("*")
    .eq("club_id", clubId)
    .order("date", { ascending: false });

  if (error) {
    console.error("Erro ao buscar prontuários médicos:", error);
    throw new Error(`Erro ao buscar prontuários médicos: ${error.message}`);
  }

  // Adiciona compatibilidade com propriedade player
  return (data || []).map(record => ({
    ...record,
    player: record.player_id
  })) as MedicalRecord[];
}

export async function createMedicalRecord(clubId: number, record: Omit<MedicalRecord, "id">): Promise<MedicalRecord> {
  // Compatibilidade entre player e player_id
  const player_id = record.player_id || record.player;

  // Construir objeto de inserção
  const insertData: any = {
    club_id: clubId,
    player_id,
    date: record.date,
    description: record.description,
    doctor: record.doctor,
    status: record.status
  };

  // Adicionar campos para encaminhamentos se fornecidos
  if (record.symptoms !== undefined) insertData.symptoms = record.symptoms;
  if (record.is_referral !== undefined) insertData.is_referral = record.is_referral;
  if (record.referred_by !== undefined) insertData.referred_by = record.referred_by;
  if (record.doctor_id !== undefined) insertData.doctor_id = record.doctor_id;
  if (record.viewed !== undefined) insertData.viewed = record.viewed;
  if (record.viewed_at !== undefined) insertData.viewed_at = record.viewed_at;
  if (record.completed !== undefined) insertData.completed = record.completed;

  const { data, error } = await supabase
    .from("medical_records")
    .insert(insertData)
    .select()
    .single();

  if (error) {
    console.error("Erro ao criar prontuário médico:", error);
    throw new Error(`Erro ao criar prontuário médico: ${error.message}`);
  }

  return {
    ...data,
    player: data.player_id
  } as MedicalRecord;
}

export async function updateMedicalRecord(clubId: number, id: number, record: Partial<MedicalRecord>): Promise<MedicalRecord> {
  // Construir objeto de atualização apenas com os campos fornecidos
  const updateData: any = {};

  // Campos básicos
  if (record.player_id !== undefined) updateData.player_id = record.player_id;
  if (record.date !== undefined) updateData.date = record.date;
  if (record.description !== undefined) updateData.description = record.description;
  if (record.doctor !== undefined) updateData.doctor = record.doctor;
  if (record.status !== undefined) updateData.status = record.status;

  // Novos campos para encaminhamentos
  if (record.symptoms !== undefined) updateData.symptoms = record.symptoms;
  if (record.is_referral !== undefined) updateData.is_referral = record.is_referral;
  if (record.referred_by !== undefined) updateData.referred_by = record.referred_by;
  if (record.doctor_id !== undefined) updateData.doctor_id = record.doctor_id;
  if (record.viewed !== undefined) updateData.viewed = record.viewed;
  if (record.viewed_at !== undefined) updateData.viewed_at = record.viewed_at;
  if (record.completed !== undefined) updateData.completed = record.completed;

  const { data, error } = await supabase
    .from("medical_records")
    .update(updateData)
    .eq("club_id", clubId)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    console.error("Erro ao atualizar prontuário médico:", error);
    throw new Error(`Erro ao atualizar prontuário médico: ${error.message}`);
  }

  return {
    ...data,
    player: data.player_id
  } as MedicalRecord;
}

export async function deleteMedicalRecord(clubId: number, id: number): Promise<boolean> {
  const { error } = await supabase
    .from("medical_records")
    .delete()
    .eq("club_id", clubId)
    .eq("id", id);

  if (error) {
    console.error("Erro ao deletar prontuário médico:", error);
    throw new Error(`Erro ao deletar prontuário médico: ${error.message}`);
  }

  return true;
}

// Funções para Financial Transactions
export async function getFinancialTransactions(clubId: number): Promise<FinancialTransaction[]> {
  try {
    // Buscar transações financeiras
    const { data, error } = await supabase
      .from("financial_transactions")
      .select("*")
      .eq("club_id", clubId)
      .order("date", { ascending: false });

    if (error) {
      console.error("Erro ao buscar transações financeiras:", error);
      throw new Error(`Erro ao buscar transações financeiras: ${error.message}`);
    }

    // Se não houver dados, retornar array vazio
    if (!data || data.length === 0) {
      return [];
    }

    // Processar os dados para extrair informações do jogador da descrição
    const transactions = await Promise.all(data.map(async transaction => {
      // Só extrair o nome do jogador da descrição se realmente for um jogador (tem player_id)
      let playerName = null;
      if (transaction.player_id && transaction.description && transaction.description.includes(" - ")) {
        const parts = transaction.description.split(" - ");
        if (parts.length > 1) {
          playerName = parts[1];
        }
      }

      let collaborator_role = null;
      let medical_professional_role = null;
      let medical_professional_id = null;

      // Se tiver collaborator_id, buscar a função do colaborador
      if (transaction.collaborator_id) {
        try {
          const { data: collaboratorData, error: collaboratorError } = await supabase
            .from("collaborators")
            .select("role, full_name")
            .eq("id", transaction.collaborator_id)
            .eq("club_id", clubId)
            .single();

          if (!collaboratorError && collaboratorData) {
            collaborator_role = collaboratorData.role;
            console.log(`Função do colaborador ${transaction.collaborator_id}: ${collaborator_role}`);
          } else {
            console.error(`Erro ao buscar função do colaborador ${transaction.collaborator_id}:`, collaboratorError);
          }
        } catch (err) {
          console.error(`Erro ao buscar colaborador ${transaction.collaborator_id}:`, err);
        }
      }

      // Se tiver player_id, verificar se é um médico
      if (transaction.player_id) {
        try {
          const { data: medicalData, error: medicalError } = await supabase
            .from("medical_professionals")
            .select("id, role")
            .eq("user_id", transaction.player_id)
            .eq("club_id", clubId)
            .single();

          if (!medicalError && medicalData) {
            medical_professional_id = medicalData.id;
            medical_professional_role = medicalData.role;
          }
        } catch (err) {
          console.error(`Erro ao buscar profissional médico:`, err);
        }
      }

      return {
        ...transaction,
        payment_status: (transaction.payment_status || '').toLowerCase(),
        player_name: playerName,
        collaborator_role,
        medical_professional_id,
        medical_professional_role
      };
    }));

    console.log("Transações carregadas:", transactions);
    return transactions as FinancialTransaction[];
  } catch (error) {
    console.error("Erro ao buscar transações financeiras:", error);
    throw error;
  }
}

export async function createFinancialTransaction(clubId: number, transaction: Omit<FinancialTransaction, "id">): Promise<FinancialTransaction> {
  try {
    // Converter string para número se necessário
    const amount = typeof transaction.amount === 'string' ? parseFloat(transaction.amount) : transaction.amount;

    console.log("Criando transação financeira:", {
      club_id: clubId,
      date: transaction.date,
      type: transaction.type,
      category: transaction.category,
      amount,
      description: transaction.description,
      player_id: transaction.player_id
    });

    // Preparar os dados para inserção
    const insertData: any = {
      club_id: clubId,
      date: transaction.date,
      type: transaction.type,
      category: transaction.category,
      amount,
      description: transaction.description,
      payment_status: transaction.payment_status || 'pending'
    };

    // Adicionar player_id apenas se estiver presente
    if (transaction.player_id) {
      insertData.player_id = transaction.player_id;
    }

    // Adicionar collaborator_id apenas se estiver presente
    if (transaction.collaborator_id) {
      insertData.collaborator_id = transaction.collaborator_id;
    }

    const { data, error } = await supabase
      .from("financial_transactions")
      .insert(insertData)
      .select()
      .single();

    if (error) {
      console.error("Erro ao criar transação financeira:", error);
      throw new Error(`Erro ao criar transação financeira: ${error.message}`);
    }

    return data as FinancialTransaction;
  } catch (error) {
    console.error("Erro ao criar transação financeira:", error);
    throw error;
  }
}

export async function updateFinancialTransaction(clubId: number, id: number, transaction: Partial<FinancialTransaction>): Promise<FinancialTransaction> {
  // Preparar os dados para atualização
  const updateData: any = {
    date: transaction.date,
    type: transaction.type,
    category: transaction.category,
    amount: transaction.amount,
    description: transaction.description
  };

  // Adicionar payment_status se estiver presente
  if (transaction.payment_status !== undefined) {
    updateData.payment_status = transaction.payment_status;
  }

  // Adicionar player_id apenas se estiver presente
  if (transaction.player_id !== undefined) {
    updateData.player_id = transaction.player_id;
  }

  // Adicionar collaborator_id apenas se estiver presente
  if (transaction.collaborator_id !== undefined) {
    updateData.collaborator_id = transaction.collaborator_id;
  }

  const { data, error } = await supabase
    .from("financial_transactions")
    .update(updateData)
    .eq("club_id", clubId)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    console.error("Erro ao atualizar transação financeira:", error);
    throw new Error(`Erro ao atualizar transação financeira: ${error.message}`);
  }

  return data as FinancialTransaction;
}

export async function deleteFinancialTransaction(clubId: number, id: number): Promise<boolean> {
  const { error } = await supabase
    .from("financial_transactions")
    .delete()
    .eq("club_id", clubId)
    .eq("id", id);

  if (error) {
    console.error("Erro ao deletar transação financeira:", error);
    throw new Error(`Erro ao deletar transação financeira: ${error.message}`);
  }

  return true;
}

export async function markTransactionAsPaid(clubId: number, id: number): Promise<FinancialTransaction> {
  const { data, error } = await supabase
    .from("financial_transactions")
    .update({ payment_status: 'paid' })
    .eq("club_id", clubId)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    console.error("Erro ao marcar transação como paga:", error);
    throw new Error(`Erro ao marcar transação como paga: ${error.message}`);
  }

  return data as FinancialTransaction;
}

// Funções para Sessions
export async function getSessions(clubId: number): Promise<Session[]> {
  // Para esta função, usaremos uma tabela temporária ou uma visualização virtual
  // já que não existe uma tabela específica para sessões no banco
  // Vamos usar a tabela trainings e filtrar por um tipo específico

  const { data, error } = await supabase
    .from("trainings")
    .select("*")
    .eq("club_id", clubId)
    .ilike("notes", "%sessão%");

  if (error) {
    console.error("Erro ao buscar sessões:", error);
    throw new Error(`Erro ao buscar sessões: ${error.message}`);
  }

  // Convertemos os dados para o formato esperado
  return (data || []).map((item) => {
    const notesparts = item.notes?.split('|') || [];
    return {
      id: item.id,
      club_id: item.club_id,
      player: notesparts[0] || "Jogador",
      type: notesparts[1] || "Sessão",
      date: item.date,
      professional: notesparts[2] || "",
      notes: notesparts[3] || ""
    };
  });
}

export async function createSession(clubId: number, session: Omit<Session, "id">): Promise<Session> {
  // Usamos a tabela trainings para armazenar sessões
  const notes = `${session.player}|${session.type}|${session.professional}|${session.notes || ''}|sessão`;

  const { data, error } = await supabase
    .from("trainings")
    .insert({
      club_id: clubId,
      date: session.date,
      notes: notes
    })
    .select()
    .single();

  if (error) {
    console.error("Erro ao criar sessão:", error);
    throw new Error(`Erro ao criar sessão: ${error.message}`);
  }

  const notesparts = data.notes?.split('|') || [];

  return {
    id: data.id,
    club_id: data.club_id,
    player: notesparts[0] || session.player,
    type: notesparts[1] || session.type,
    date: data.date,
    professional: notesparts[2] || session.professional,
    notes: notesparts[3] || session.notes
  };
}

// CRUD para contratos (inclui salário)
export async function createContract(clubId: number, contract: Omit<any, "id">) {
  const { data, error } = await supabase
    .from("contracts")
    .insert({ ...contract, club_id: clubId })
    .select()
    .single();
  if (error) {
    throw new Error(`Erro ao criar contrato: ${error.message}`);
  }
  return data;
}

export async function updateContract(clubId: number, id: number, contract: Partial<any>) {
  const { data, error } = await supabase
    .from("contracts")
    .update({ ...contract })
    .eq("club_id", clubId)
    .eq("id", id)
    .select()
    .single();
  if (error) {
    throw new Error(`Erro ao atualizar contrato: ${error.message}`);
  }
  return data;
}

export async function deleteContract(clubId: number, id: number) {
  const { error } = await supabase
    .from("contracts")
    .delete()
    .eq("club_id", clubId)
    .eq("id", id);
  if (error) {
    throw new Error(`Erro ao deletar contrato: ${error.message}`);
  }
  return true;
}

export async function getContracts(clubId: number) {
  try {
    const { data, error } = await supabase
      .from("contracts")
      .select("*")
      .eq("club_id", clubId);

    if (error) {
      console.error("Erro ao buscar contratos:", error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error("Erro ao buscar contratos:", error);
    return [];
  }
}

// Função removida para corrigir lint

// Função para buscar artilheiros por temporada
export async function getTopScorers(clubId: number, seasonId?: number): Promise<{ player: string; goals: number }[]> {
  let matchIds: string[] = [];
  if (seasonId) {
    // Buscar os jogos da temporada e clube
    const { data: matches, error: matchError } = await supabase
      .from("matches")
      .select("id")
      .eq("club_id", clubId)
      .eq("season_id", seasonId);
    if (matchError) throw new Error(matchError.message);
    matchIds = (matches || []).map((m: any) => m.id);
    if (matchIds.length === 0) return [];
  } else {
    // Buscar todos os jogos do clube
    const { data: matches, error: matchError } = await supabase
      .from("matches")
      .select("id")
      .eq("club_id", clubId);
    if (matchError) throw new Error(matchError.message);
    matchIds = (matches || []).map((m: any) => m.id);
    if (matchIds.length === 0) return [];
  }

  const { data, error } = await supabase
    .from("gols")
    .select("player_id, match_id")
    .in("match_id", matchIds);

  if (error) throw new Error(error.message);

  // Agrupar por player_id
  const goalMap: Record<string, number> = {};
  const golsData = (data || []) as { player_id: string; match_id: string }[];
  golsData.forEach((g) => {
    goalMap[g.player_id] = (goalMap[g.player_id] || 0) + 1;
  });
  return Object.entries(goalMap).map(([player, goals]) => ({ player, goals }));
}

// Função para inserir gols na tabela gols
export async function insertGols(clubId: number, matchId: string, gols: { player_id: string; minuto?: number }[]) {
  if (!gols || gols.length === 0) return;
  const rows = gols.map(g => ({
    club_id: clubId,
    match_id: matchId,
    player_id: g.player_id,
    minute: g.minuto || null
  }));
  console.log('[insertGols] Tentando inserir gols:', rows);
  const { error, data } = await supabase.from("gols").insert(rows);
  if (error) {
    console.error("[insertGols] Erro ao inserir gols:", error, rows);
    throw new Error(error.message);
  }
  console.log("[insertGols] Inserção bem-sucedida:", data, rows);
}

// CRUD para player_salaries
// Define o tipo PlayerSalary localmente para evitar problemas de importação
export type PlayerSalary = {
  id: number;
  club_id: number;
  player_id: string;
  amount: number;
  currency: string;
  payment_date: string;
  status: string;
  notes?: string;
  created_at?: string;
};

export async function getSalaries(clubId: number): Promise<PlayerSalary[]> {
  try {
    // Buscar salários com join para obter nomes dos jogadores
    const { data, error } = await supabase
      .from("player_salaries")
      .select(`
        *,
        players:player_id (
          name
        )
      `)
      .eq("club_id", clubId);

    if (error) {
      console.error("Erro ao buscar salários:", error);

      // Fallback: buscar apenas os salários sem o join
      const { data: fallbackData, error: fallbackError } = await supabase
        .from("player_salaries")
        .select("*")
        .eq("club_id", clubId);

      if (fallbackError) {
        console.error("Erro ao buscar salários (fallback):", fallbackError);
        return [];
      }

      return fallbackData || [];
    }

    // Processar os dados para incluir o nome do jogador
    const salaries = (data || []).map(salary => {
      // Se o salário tem um jogador associado, adicionar o nome do jogador
      if (salary.players && salary.players.name) {
        return {
          ...salary,
          player_name: salary.players.name
        };
      }
      return salary;
    });

    return salaries;
  } catch (error) {
    console.error("Erro ao buscar salários:", error);
    return [];
  }
}

export async function createPlayerSalary(clubId: number, salary: Omit<PlayerSalary, "id" | "created_at">): Promise<PlayerSalary | null> {
  const { data, error } = await supabase
    .from("player_salaries")
    .insert({ ...salary, club_id: clubId })
    .select()
    .single();
  if (error) {
    throw new Error(`Erro ao criar salário: ${error.message}`);
  }
  return data;
}

export async function updatePlayerSalary(clubId: number, id: number, salary: Partial<Omit<PlayerSalary, "id" | "created_at">>): Promise<PlayerSalary | null> {
  const { data, error } = await supabase
    .from("player_salaries")
    .update({ ...salary })
    .eq("club_id", clubId)
    .eq("id", id)
    .select()
    .single();
  if (error) {
    throw new Error(`Erro ao atualizar salário: ${error.message}`);
  }
  return data;
}

export async function deletePlayerSalary(clubId: number, id: number): Promise<boolean> {
  const { error } = await supabase
    .from("player_salaries")
    .delete()
    .eq("club_id", clubId)
    .eq("id", id);
  if (error) {
    throw new Error(`Erro ao deletar salário: ${error.message}`);
  }
  return true;
}

// Funções para notificações
export async function getUserNotifications(userId: string): Promise<Notification[]> {
  const { data, error } = await supabase
    .from('notifications')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false });
  if (error) throw new Error(error.message);
  return data as Notification[];
}

export async function createNotification({ user_id, club_id, title, description }: {
  user_id: string;
  club_id?: number;
  title: string;
  description?: string;
}): Promise<Notification> {
  const { data, error } = await supabase
    .from('notifications')
    .insert([{ user_id, club_id, title, description }])
    .select()
    .single();
  if (error) throw new Error(error.message);
  return data as Notification;
}

export async function markNotificationAsRead(id: number): Promise<boolean> {
  const { error } = await supabase
    .from('notifications')
    .update({ read: true })
    .eq('id', id);
  if (error) throw new Error(error.message);
  return true;
}

export async function deleteNotification(id: number): Promise<boolean> {
  const { error } = await supabase
    .from('notifications')
    .delete()
    .eq('id', id);
  if (error) throw new Error(error.message);
  return true;
}

// Função para verificar e atualizar o acesso de contas de jogadores com base na data de fim de contrato
export async function checkPlayerAccountsExpiration(clubId: number): Promise<{ expired: number, active: number }> {
  try {
    const now = new Date().toISOString();

    // Buscar contas de jogadores expiradas
    const { data: expiredAccounts, error: expiredError } = await supabase
      .from("player_accounts")
      .select("id, player_id, user_id")
      .eq("club_id", clubId)
      .lt("expires_at", now);

    if (expiredError) {
      console.error("Erro ao buscar contas expiradas:", expiredError);
      throw new Error(`Erro ao buscar contas expiradas: ${expiredError.message}`);
    }

    // Para cada conta expirada, remover o usuário do clube
    let expiredCount = 0;
    for (const account of expiredAccounts || []) {
      try {
        await removeUserFromClub(clubId, account.user_id);
        expiredCount++;
      } catch (error) {
        console.error(`Erro ao remover usuário ${account.user_id} do clube:`, error);
      }
    }

    // Buscar contas de jogadores ativas
    const { data: activeAccounts, error: activeError } = await supabase
      .from("player_accounts")
      .select("id")
      .eq("club_id", clubId)
      .gte("expires_at", now);

    if (activeError) {
      console.error("Erro ao buscar contas ativas:", activeError);
      throw new Error(`Erro ao buscar contas ativas: ${activeError.message}`);
    }

    return {
      expired: expiredCount,
      active: (activeAccounts || []).length
    };
  } catch (error: unknown) {
    console.error("Erro ao verificar expiração de contas:", error);
    if (error instanceof Error) {
      throw new Error(error.message);
    } else {
      throw new Error("Erro ao verificar expiração de contas");
    }
  }
}

// Re-exporta funções e tipos de Accommodations para manter compatibilidade
export type { Accommodation, PlayerAccommodation } from './accommodations';
export {
  getAccommodations,
  getAccommodationById,
  createAccommodation,
  updateAccommodation,
  deleteAccommodation,
  getPlayerAccommodations,
  getAccommodationPlayers,
  assignPlayerToAccommodation,
  updatePlayerAccommodation,
  removePlayerFromAccommodation
} from './accommodations';

// Re-exporta funções e tipos de CollaboratorAccommodations para manter compatibilidade
export type { CollaboratorAccommodation } from './collaboratorAccommodations';
export {
  getCollaboratorAccommodations,
  getAccommodationCollaborators,
  assignCollaboratorToAccommodation,
  updateCollaboratorAccommodation,
  removeCollaboratorFromAccommodation
} from './collaboratorAccommodations';

// Exporta funções e tipos de HotelRooms
export type { HotelRoom } from './hotelRooms';
export {
  getHotelRooms,
  getHotelRoomById,
  createHotelRoom,
  updateHotelRoom,
  deleteHotelRoom,
  getRoomAvailability,
  getAvailableRooms,
  createMultipleRooms
} from './hotelRooms';

// Re-exporta funções e tipos de Categories para manter compatibilidade
export type { Category, PlayerCategory } from './categories';
export {
  getCategories,
  getCategoryById,
  createCategory,
  updateCategory,
  deleteCategory,
  assignPlayerToCategory,
  removePlayerFromCategory,
  getPlayerCategories,
  getCategoryPlayers,
  migrateYouthPlayers
} from './categories';

// Exporta funções e tipos de Títulos do Clube
export type { ClubTitle };
export {
  getClubTitles,
  createClubTitle,
  updateClubTitle,
  deleteClubTitle
};

// Exporta funções e tipos de Dashboard Statistics
export type { DashboardStats, CategoryPlayerCount } from './dashboardStats';
export {
  getDashboardStats,
  getTotalActivePlayers,
  getPlayersByCategory,
  getPlayersInRehabilitation,
  getAverageFitness
} from './dashboardStats';

// Exporta aniversariantes do mês
export type { BirthdayPerson } from './birthdays';
export { getMonthlyBirthdays } from './birthdays';

// Re-exporta funções e tipos de CategoryLineups para manter compatibilidade
export type { CategoryLineup } from './categoryLineups';
export {
  getCategoryLineup,
  saveCategoryLineup,
  deleteCategoryLineup
} from './categoryLineups';

// Re-exporta funções e tipos de Storage para manter compatibilidade
export {
  uploadProfileImage,
  uploadPlayerDocument,
  registerPlayerDocument,
  verifyPlayerDocument,
  deletePlayerDocument,
  uploadMedicalCertificate
} from './storage';

export { uploadClubLogo } from './storage-simple';

// Re-exporta funções e tipos de MedicalProfessionals para manter compatibilidade
export type { MedicalProfessional, MedicalProfessionalRole, MedicalProfessionalAccountOptions } from './medicalProfessionals';
export {
  getMedicalProfessionals,
  getMedicalProfessionalById,
  getMedicalProfessionalByUserId,
  createMedicalProfessional,
  updateMedicalProfessional,
  deleteMedicalProfessional,
  uploadMedicalProfessionalCertificate,
  createMedicalProfessionalAccount,
  MEDICAL_PROFESSIONAL_PERMISSIONS
} from './medicalProfessionals';

// Re-exporta funções e tipos de MedicalAppointments
export type { MedicalAppointment, AppointmentStatus } from './medicalAppointments';
export {
  getMedicalAppointments,
  createMedicalAppointment,
  updateMedicalAppointment,
  deleteMedicalAppointment,
  updateAppointmentStatus,
  markAppointmentNotificationSent,
  APPOINTMENT_PERMISSIONS
} from './medicalAppointments';

// Re-exporta funções e tipos de MedicalAvailability
export type { MedicalAvailability } from './medicalAvailability';
export {
  getMedicalAvailability,
  createMedicalAvailability,
  updateMedicalAvailability,
  deleteMedicalAvailability,
  MEDICAL_AVAILABILITY_PERMISSIONS
} from './medicalAvailability';

// Re-exporta funções e tipos de MedicalTreatment
export type { MedicalTreatmentEvolution, TreatmentStatus } from './medicalTreatment';
export {
  getTreatmentEvolutions,
  createTreatmentEvolution,
  updateTreatmentEvolution,
  deleteTreatmentEvolution,
  addTreatmentSignature,
  TREATMENT_PERMISSIONS
} from './medicalTreatment';

// Re-exporta funções e tipos de MedicalExams
export type { MedicalExam, ExamStatus } from './medicalExams';
export {
  getMedicalExams,
  getPlayerMedicalExams,
  createMedicalExam,
  updateMedicalExam,
  deleteMedicalExam,
  uploadExam,
  EXAM_PERMISSIONS
} from './medicalExams';

// Re-exporta funções e tipos de MedicalPrescriptions
export type { MedicalPrescription, PrescriptionItem, PrescriptionStatus } from './medicalPrescriptions';
export {
  getMedicalPrescriptions,
  getPlayerPrescriptions,
  getPrescriptionItems,
  createMedicalPrescription,
  addPrescriptionItem,
  updateMedicalPrescription,
  deleteMedicalPrescription,
  addPrescriptionSignature,
  PRESCRIPTION_PERMISSIONS
} from './medicalPrescriptions';

// Re-exporta funções e tipos de Notifications para manter compatibilidade
export type { Notification } from './notifications';
export {
  getMedicalNotifications,
  markMedicalNotificationAsRead,
  markAllMedicalNotificationsAsRead,
  createMedicalNotification,
  deleteMedicalNotification,
  countUnreadMedicalNotifications
} from './notifications';

// Função para criar uma conta de usuário para um jogador
export async function createPlayerWithAccount(
  clubId: number,
  playerId: string,
  email: string,
  password?: string
): Promise<boolean> {
  try {
    // Verificar se o jogador existe
    const { data: player, error: playerError } = await supabase
      .from("players")
      .select("*")
      .eq("club_id", clubId)
      .eq("id", playerId)
      .single();

    if (playerError) {
      throw new Error(`Jogador não encontrado: ${playerError.message}`);
    }

    // Verificar se o email já está em uso
    const { data: existingUser, error: userError } = await supabase
      .from("users")
      .select("id")
      .eq("email", email);

    if (userError) {
      throw new Error(`Erro ao verificar email: ${userError.message}`);
    }

    if (existingUser && existingUser.length > 0) {
      // Se o usuário já existe, vincular ao jogador
      const { error: updateError } = await supabase
        .from("players")
        .update({ user_id: existingUser[0].id })
        .eq("id", playerId)
        .eq("club_id", clubId);

      if (updateError) {
        throw new Error(`Erro ao vincular jogador à conta existente: ${updateError.message}`);
      }

      // Verificar se o usuário já é membro do clube
      const { data: existingMember, error: memberCheckError } = await supabase
        .from("club_members")
        .select("id")
        .eq("club_id", clubId)
        .eq("user_id", existingUser[0].id)
        .maybeSingle();

      if (memberCheckError) {
        console.error("Erro ao verificar associação do usuário ao clube:", memberCheckError);
      }

      // Se o usuário ainda não é membro do clube, adicioná-lo
      if (!existingMember) {
        const { error: memberError } = await supabase
          .from("club_members")
          .insert({
            club_id: clubId,
            user_id: existingUser[0].id,
            role: "player",
            status: "ativo"
          });

        if (memberError) {
          console.error("Erro ao adicionar usuário existente ao clube:", memberError);
          throw new Error(`Erro ao adicionar usuário ao clube: ${memberError.message}`);
        }
      }

      return true;
    }

    // Criar novo usuário
    let userId: string;

    // Obter informações do clube
    const { data: clubData, error: clubError } = await supabase
      .from("club_info")
      .select("name")
      .eq("id", clubId)
      .single();

    if (clubError) {
      console.error("Erro ao obter informações do clube:", clubError);
    }

    const clubName = clubData?.name || "Seu Clube";

    // Usar nossa nova função para criar usuário diretamente
    const result = await createUserDirectly(
      email,
      player.name,
      clubName,
      "player",
      clubId,
      password
    );

    if (!result.success) {
      throw new Error(result.message);
    }

    userId = result.userId;

    if (!userId) {
      throw new Error("Erro ao criar usuário: ID não retornado");
    }

    // Vincular usuário ao jogador
    const { error: updateError } = await supabase
      .from("players")
      .update({ user_id: userId })
      .eq("id", playerId)
      .eq("club_id", clubId);

    if (updateError) {
      throw new Error(`Erro ao vincular jogador à conta: ${updateError.message}`);
    }

    // Verificar se o usuário já é membro do clube
    const { data: existingMember, error: memberCheckError } = await supabase
      .from("club_members")
      .select("id")
      .eq("club_id", clubId)
      .eq("user_id", userId)
      .maybeSingle();

    if (memberCheckError) {
      console.error("Erro ao verificar associação do usuário ao clube:", memberCheckError);
    }

    // Se o usuário ainda não é membro do clube, adicioná-lo
    if (!existingMember) {
      const { error: memberError } = await supabase
        .from("club_members")
        .insert({
          club_id: clubId,
          user_id: userId,
          role: "player",
          status: "ativo"
        });

      if (memberError) {
        console.error("Erro ao adicionar usuário ao clube:", memberError);
        throw new Error(`Erro ao adicionar usuário ao clube: ${memberError.message}`);
      }
    }

    // Verificar se o jogador tem data de fim de contrato
    if (player.contract_end_date) {
      // Adicionar entrada na tabela player_accounts para rastrear a expiração da conta
      const { error: accountError } = await supabase
        .from("player_accounts")
        .insert({
          club_id: clubId,
          player_id: playerId,
          user_id: userId,
          expires_at: player.contract_end_date
        });

      if (accountError) {
        console.error("Erro ao configurar expiração da conta do jogador:", accountError);
        // Não lançamos erro aqui para não interromper o fluxo principal
      }
    }

    return true;
  } catch (error: unknown) {
    console.error("Erro ao criar conta para jogador:", error);
    if (error instanceof Error) {
      throw new Error(error.message);
    } else {
      throw new Error("Erro ao criar conta para jogador");
    }
  }
}

// Re-exporta funções e tipos de Departments para manter compatibilidade
export type { Department, UserDepartment } from './departments';
export {
  getDepartments,
  createDepartment,
  updateDepartment,
  deleteDepartment,
  getDepartmentUsers,
  getUserDepartments,
  addUserToDepartment,
  removeUserFromDepartment,
  updateUserDepartmentRole
} from './departments';

// Re-exporta funções e tipos de Permissions para manter compatibilidade
export type { UserInvitation, UserPermissions } from './permissions';
export {
  getUserPermissions,
  updateUserPermissions,
  createUserInvitation,
  getUserInvitationByToken,
  acceptUserInvitation,
  cancelUserInvitation,
  getClubInvitations,
  hasPermission,
  checkInvitationExists
} from './permissions';

// Re-exporta funções e tipos de Documents para manter compatibilidade
export type { PlayerDocument } from './documents';
export {
  DOCUMENT_TYPES,
  DOCUMENT_LABELS,
  uploadDocument,
  getPlayerDocuments,
  verifyDocument,
  deleteDocument,
  getPendingDocuments,
  checkRequiredDocuments
} from './documents';

// Re-exporta funções e tipos de Callups para manter compatibilidade
export type { Callup, CallupPlayer } from './callups';
export {
  getCallups,
  getCallupById,
  createCallup,
  updateCallup,
  deleteCallup,
  getCallupPlayers,
  addPlayerToCallup,
  removePlayerFromCallup,
  generateCallupPDF,
  generateCallupAccommodationPDF,
  getUpcomingCallups,
  getPlayerUpcomingCallups
} from './callups';

// Re-exporta funções e tipos de Finances para manter compatibilidade
export type { PlayerFinancialEntry, PlayerFinancialData, PlayerBankingInfo } from './finances';
export {
  getPlayerFinancialData,
  addPlayerFinancialEntry,
  removePlayerFinancialEntry,
  calculateMonthlyBalance,
  calculateTotalBalance,
  getYearlyFinancialData,
  getPlayerBankingInfo,
  updatePlayerBankingInfo
} from './finances';

// Re-exporta funções e tipos de CollaboratorFinances
export type { CollaboratorFinancialEntry, CollaboratorFinancialData, CollaboratorBankingInfo } from './collaboratorFinances';
export {
  getCollaboratorFinancialData,
  addCollaboratorFinancialEntry,
  removeCollaboratorFinancialEntry,
  calculateMonthlyBalance as calculateCollaboratorMonthlyBalance,
  calculateTotalBalance as calculateCollaboratorTotalBalance,
  getYearlyFinancialData as getCollaboratorYearlyFinancialData,
  getCollaboratorBankingInfo,
  updateCollaboratorBankingInfo
} from './collaboratorFinances';

// Re-exporta funções e tipos de SalaryAdvances
export type { SalaryAdvance } from './salaryAdvances';
export {
  getSalaryAdvances,
  createSalaryAdvance,
  cancelSalaryAdvance,
  getTotalAdvances
} from './salaryAdvances';

// Re-exporta funções de sincronização de roles
export {
  syncCollaboratorRoles,
  checkAndFixRoleInconsistencies
} from './syncCollaboratorRoles';

// Re-exporta funções e tipos de Users para manter compatibilidade
export type { ClubUser, UserProfile } from './users';
export {
  getClubUsers,
  removeUserFromClub,
  getUserProfile,
  updateUserProfile
} from './users';

// Re-exporta funções e tipos de Administrative para manter compatibilidade
export type { AdministrativeDocument, AdministrativeTask, AdministrativeReminder } from './administrative';
export {
  getAdministrativeDocuments,
  getAdministrativeDocumentById,
  createAdministrativeDocument,
  updateAdministrativeDocument,
  deleteAdministrativeDocument,
  signAdministrativeDocument,
  getAdministrativeTasks,
  createAdministrativeTask,
  updateAdministrativeTask,
  deleteAdministrativeTask,
  getAdministrativeReminders,
  createAdministrativeReminder,
  updateAdministrativeReminder,
  deleteAdministrativeReminder,
  generateDocumentPDF
} from './administrative';

// Re-exporta funções e tipos de Form Templates para manter compatibilidade
export type { ClubFormTemplate, FormTemplateInput } from './clubFormTemplates';
export {
  getClubFormTemplates,
  getActiveFormTemplates,
  getFormTemplateById,
  createFormTemplate,
  updateFormTemplate,
  deleteFormTemplate,
  toggleTemplateStatus,
  generateFormTemplatePDF,
  generateFormTemplatePDFHtml,
  FORM_TEMPLATE_PERMISSIONS
} from './clubFormTemplates';

// Re-exporta funções e tipos de Collaborators para manter compatibilidade
export type { Collaborator, CollaboratorDocument } from './collaborators';
export {
  COLLABORATOR_DOCUMENT_TYPES,
  COLLABORATOR_DOCUMENT_LABELS,
  getCollaborators,
  getCollaboratorById,
  getCollaboratorByUserId,
  createCollaborator,
  updateCollaborator,
  deleteCollaborator,
  uploadCollaboratorDocument,
  getCollaboratorDocuments,
} from './collaborators';

// Re-exporta funções e tipos de Suppliers para manter compatibilidade
export type { Supplier } from './suppliers';
export {
  getSuppliers,
  getSupplierById,
  createSupplier,
  updateSupplier,
  deleteSupplier,
} from './suppliers';

export { COLLABORATOR_PERMISSIONS, SUPPLIER_PERMISSIONS } from '@/constants/permissions';

// Re-exporta funções e tipos de SupplierOrders para manter compatibilidade
export type { SupplierOrder } from './supplierOrders';
export {
  getSupplierOrders,
  getAllSupplierOrders,
  createSupplierOrder,
  deleteSupplierOrder
} from './supplierOrders';

// Re-exporta funções e tipos de Inventory para manter compatibilidade
export type { InventoryProduct, InventoryTransaction, InventoryNotificationSettings } from './inventory';
export {
  INVENTORY_DEPARTMENTS,
  getInventoryProducts,
  getInventoryProductById,
  createInventoryProduct,
  updateInventoryProduct,
  deleteInventoryProduct,
  getInventoryTransactions,
  registerInventoryTransaction,
  getLowStockProducts,
  getInventoryNotificationSettings,
  updateInventoryNotificationSettings
} from './inventory';

// Re-exporta funções e tipos de InventoryRequests para manter compatibilidade
export type { InventoryRequest, InventoryRequestItem } from './inventoryRequests';
export {
  REQUESTER_TYPES,
  OPERATIONAL_DEPARTMENTS,
  getInventoryRequests,
  getMyInventoryRequests,
  getInventoryRequestById,
  getInventoryRequestItems,
  createInventoryRequest,
  updateInventoryRequest,
  cancelInventoryRequest,
  deleteInventoryRequest,
  addInventoryRequestItem,
  removeInventoryRequestItem,
  PLAYER_REQUEST_CATEGORIES,
  processInventoryRequest,
  processInventoryRequestWithAutoSignature,
  returnInventoryRequestItem,
  getLowStockShoppingList,
  updateProductImage
} from './inventoryRequests';

// Re-exporta funções e tipos de Season para manter compatibilidade
export type { Season } from './seasonApi';
export {
  deleteSeason
} from './seasonApi';

// Re-exporta funções e tipos de Accounts para manter compatibilidade
export type { FinancialAccount } from './accounts';
export {
  getFinancialAccounts,
  createFinancialAccount,
  updateFinancialAccount,
  deleteFinancialAccount,
  uploadAccountReceipt
} from './accounts';

// Re-exporta funções e tipos de Meals para manter compatibilidade
export type { MealType, MealSession, MealParticipant, MealSessionWithDetails, MealParticipantWithDetails } from './meals';
export {
  getMealTypes,
  createMealType,
  updateMealType,
  deleteMealType,
  getMealSessions,
  createMealSession,
  updateMealSession,
  deleteMealSession,
  getMealParticipants,
  addMealParticipant,
  removeMealParticipant,
  updateMealParticipantSignature,
  getPlayerUpcomingMeals
} from './meals';
export {
  getMealLocations,
  createMealLocation,
  updateMealLocation,
  deleteMealLocation
} from './mealLocations';
export type { MealLocation } from './mealLocations';

export interface PlayerOccurrence {
  id: number;
  player_id: string;
  club_id: number;
  type: 'divergence' | 'punishment';
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high';
  status: 'active' | 'resolved' | 'archived';
  created_at: string;
  created_by: string;
  updated_at: string;
  updated_by: string | null;
  resolved_at: string | null;
  resolved_by: string | null;
  resolution_notes: string | null;
  created_by_name?: string;
  created_by_role?: string;
  updated_by_name?: string;
  updated_by_role?: string;
  resolved_by_name?: string;
  resolved_by_role?: string;
}

// ... existing code ...

// Funções para ocorrências
export async function getPlayerOccurrences(clubId: number, playerId: string): Promise<PlayerOccurrence[]> {
  const { data, error } = await supabase
    .from('player_occurrences')
    .select(`
      *,
      created_by_name:created_by(name),
      created_by_role:created_by(role),
      updated_by_name:updated_by(name),
      updated_by_role:updated_by(role),
      resolved_by_name:resolved_by(name),
      resolved_by_role:resolved_by(role)
    `)
    .eq('club_id', clubId)
    .eq('player_id', playerId)
    .order('created_at', { ascending: false });

  if (error) throw error;
  return data;
}

export async function createPlayerOccurrence(
  clubId: number,
  playerId: string,
  occurrence: Omit<PlayerOccurrence, 'id' | 'created_at' | 'updated_at' | 'created_by' | 'updated_by'>
): Promise<PlayerOccurrence> {
  const { data, error } = await supabase
    .from('player_occurrences')
    .insert({
      ...occurrence,
      club_id: clubId,
      player_id: playerId,
      created_by: (await supabase.auth.getUser()).data.user?.id,
      updated_by: (await supabase.auth.getUser()).data.user?.id
    })
    .select()
    .single();

  if (error) throw error;
  return data;
}

export async function updatePlayerOccurrence(
  occurrenceId: number,
  updates: Partial<Omit<PlayerOccurrence, 'id' | 'created_at' | 'created_by'>>
): Promise<PlayerOccurrence> {
  const { data, error } = await supabase
    .from('player_occurrences')
    .update({
      ...updates,
      updated_by: (await supabase.auth.getUser()).data.user?.id,
      updated_at: new Date().toISOString()
    })
    .eq('id', occurrenceId)
    .select()
    .single();

  if (error) throw error;
  return data;
}

export async function deletePlayerOccurrence(occurrenceId: number): Promise<void> {
  const { error } = await supabase
    .from('player_occurrences')
    .delete()
    .eq('id', occurrenceId);

  if (error) throw error;
}

// ... existing code ...