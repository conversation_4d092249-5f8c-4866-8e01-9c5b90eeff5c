import { <PERSON><PERSON> } from "@/components/ui/button";
import { useClubInfoStore } from "@/store/useClubInfoStore";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { UseFormReturn } from "react-hook-form";
import * as z from "zod";
import { Download } from "lucide-react";
import { useRef } from "react";
import { useToast } from "@/components/ui/use-toast";
import { PDFDocument, rgb } from 'pdf-lib';

export const occurrenceSchema = z.object({
  type: z.string().min(1, "Tipo é obrigatório"),
  title: z.string().min(1, "Título é obrigatório"),
  description: z.string().min(1, "Descrição é obrigatória"),
  severity: z.enum(["low", "medium", "high"]),
  status: z.enum(["active", "resolved", "archived"]),
  resolution_notes: z.string().optional(),
});

export type OccurrenceFormData = z.infer<typeof occurrenceSchema>;

interface OccurrenceFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: OccurrenceFormData) => void;
  form: UseFormReturn<OccurrenceFormData>;
}

export function OccurrenceForm({
  isOpen,
  onClose,
  onSubmit,
  form,
}: OccurrenceFormProps) {
  const printRef = useRef<HTMLDivElement>(null);
  const { clubInfo } = useClubInfoStore();
  const { toast } = useToast();
  
  // Removendo a referência não utilizada para o relatório de impressão
  // Apenas mantendo para referência futura se necessário

  const handleSubmit = (data: OccurrenceFormData) => {
    onSubmit(data);
  };

  // Função para carregar o emblema do time como uma imagem
  const loadEmblem = (emblemUrl: string): Promise<HTMLImageElement> => {
    return new Promise((resolve, reject) => {
      if (!emblemUrl) {
        reject(new Error('URL do emblema não fornecida'));
        return;
      }

      const img = new Image();
      img.crossOrigin = 'Anonymous'; // Permite carregar imagens de outros domínios (se o servidor permitir)
      
      img.onload = () => {
        console.log('Emblema carregado com sucesso:', emblemUrl);
        resolve(img);
      };
      
      img.onerror = (error) => {
        console.error('Erro ao carregar emblema:', error);
        reject(error);
      };

      // Adiciona um timestamp para evitar cache
      const url = new URL(emblemUrl);
      url.searchParams.append('t', Date.now().toString());
      
      // Inicia o carregamento da imagem
      img.src = url.toString();
    });
  };

  const generatePdf = async () => {
    try {
      console.log('Iniciando geração do PDF...');
      console.log('clubInfo:', clubInfo);
      console.log('logo_url:', clubInfo?.logo_url);
      
      if (!form.getValues()) {
        console.error('Dados do formulário não disponíveis');
        return null;
      }
      
      // Obter valores do formulário
      const { title, type, description, severity, status, resolution_notes } = form.getValues();
      
      // Mapear valores para textos amigáveis
      const severityText = {
        low: 'Baixa',
        medium: 'Média',
        high: 'Alta'
      }[severity || 'medium'];
      
      const statusText = {
        active: 'Ativa',
        resolved: 'Resolvida',
        archived: 'Arquivada'
      }[status || 'active'];

      // Criar documento PDF
      const doc = await PDFDocument.create();
      let currentPage = doc.addPage([600, 800]);
      const { width, height } = currentPage.getSize();
      const margin = 50;
      let y = height - margin;
      const fontSize = 12;
      const footerY = 100; // Altura mínima antes de criar nova página
      
      // Função auxiliar para adicionar texto
      const drawText = (text: string, x: number, y: number, size: number, isBold = false) => {
        currentPage.drawText(text, {
          x,
          y,
          size,
          color: rgb(0, 0, 0),
        });
        return y - 20; // Retorna a nova posição y
      };
      
      // Adicionar cabeçalho com emblema e nome do time se disponível
      try {
        const clubName = clubInfo?.name || 'Clube';
        
        // Adicionar nome do time
        y = drawText(clubName, margin, y, 16);
        
        // Adicionar emblema se disponível
        const logoUrl = clubInfo?.logo_url;
        if (logoUrl) {
          console.log('Tentando carregar emblema do time:', logoUrl);
          try {
            const emblemImg = await loadEmblem(logoUrl);
            const emblemSize = 50;
            
            // Converte a imagem para um formato que o PDF pode usar
            const canvas = document.createElement('canvas');
            canvas.width = emblemImg.width;
            canvas.height = emblemImg.height;
            const ctx = canvas.getContext('2d');
            if (!ctx) throw new Error('Não foi possível criar o contexto 2D');
            
            ctx.drawImage(emblemImg, 0, 0);
            const dataUrl = canvas.toDataURL('image/png');
            
            // Adiciona a imagem ao PDF
            currentPage.drawImage(await doc.embedPng(dataUrl), {
              x: width - margin - emblemSize,
              y: y + 10, // Ajuste fino para posicionar o emblema mais para baixo
              width: emblemSize,
              height: emblemSize,
            });
            
            console.log('Emblema adicionado ao PDF com sucesso');
          } catch (error) {
            console.error('Erro ao processar emblema no PDF:', error);
            // Não interrompe o fluxo se o emblema não carregar
          }
        } else {
          console.warn('URL do emblema não disponível');
        }
        
        y -= 40; // Ajustar posição vertical após o cabeçalho
        
        // Linha divisória
        currentPage.drawLine({
          start: { x: margin, y: y + 10 },
          end: { x: width - margin, y: y + 10 },
          thickness: 1,
          color: rgb(0.8, 0.8, 0.8),
        });
        
        y -= 20; // Espaçamento extra após a linha
        
      } catch (error) {
        console.error('Erro ao renderizar cabeçalho:', error);
        // Continua mesmo com erro no cabeçalho
      }
    
      // Título do documento
      y = drawText('REGISTRO DE OCORRÊNCIA', margin, y, 18);
      
      y -= 20; // Espaçamento após o título
      
      // Função auxiliar para adicionar texto com label e valor
      const addText = (label: string, value: string, x: number, isBold = false, size = fontSize) => {
        const text = `${label}: ${value}`;
        return drawText(text, x, y, size, isBold);
      };
      
      // Adicionar informações da ocorrência
      y = addText('Título', title || 'Não informado', margin);
      y = addText('Tipo', type || 'Não informado', margin);
      y = addText('Severidade', severityText, margin);
      y = addText('Status', statusText, margin);
      
      // Adicionar descrição com quebra de linha
      y -= 10; // Espaçamento extra antes da descrição
      y = drawText('Descrição:', margin, y, fontSize);
      
      const descriptionLines = description?.match(/[\s\S]{1,80}/g) || [];
      for (const line of descriptionLines) {
        y = drawText(line, margin + 10, y, fontSize);
        
        // Se não couber na página atual, cria uma nova página
        if (y < footerY) {
          currentPage = doc.addPage([width, height]);
          y = height - margin - 20; // Reinicia a posição Y
        }
      }
      
      // Adicionar notas de resolução se disponíveis
      if (resolution_notes) {
        y -= 10; // Espaçamento extra antes das notas de resolução
        y = drawText('Notas de Resolução:', margin, y, fontSize);
        
        const notesLines = resolution_notes.match(/[\s\S]{1,80}/g) || [];
        for (const line of notesLines) {
          y = drawText(line, margin + 10, y, fontSize);
          
          // Verificar se precisa de uma nova página
          if (y < footerY) {
            currentPage = doc.addPage([width, height]);
            y = height - margin - 20;
          }
        }
      }
      
      // Adicionar rodapé com data de geração
      const footerText = `Gerado em: ${new Date().toLocaleDateString('pt-BR')}`;
      drawText(footerText, margin, 40, 10);
      
      // Gerar o PDF
      const pdfBytes = await doc.save();
      const blob = new Blob([pdfBytes], { type: 'application/pdf' });
      return URL.createObjectURL(blob);
      
    } catch (error) {
      console.error('Erro ao gerar PDF:', error);
      toast({
        title: 'Erro',
        description: 'Ocorreu um erro ao gerar o PDF. Por favor, tente novamente.',
        variant: 'destructive',
      });
      return null;
    }
  };

  const handleDownloadPDF = async () => {
    try {
      const pdfDataUri = await generatePdf();
      if (pdfDataUri) {
        const link = document.createElement('a');
        link.href = pdfDataUri;
        link.download = `ocorrencia-${form.getValues('title')?.toLowerCase().replace(/\s+/g, '-') || 'sem-titulo'}.pdf`;
        link.click();
      }
    } catch (error) {
      console.error('Erro ao gerar PDF:', error);
      toast({
        title: 'Erro',
        description: 'Não foi possível gerar o PDF da ocorrência.',
        variant: 'destructive',
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Ocorrência</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tipo</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Digite o tipo da ocorrência" 
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Título</FormLabel>
                  <FormControl>
                    <Input placeholder="Digite o título" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Descrição</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Digite a descrição"
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="severity"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Severidade</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione a severidade" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="low">Baixa</SelectItem>
                      <SelectItem value="medium">Média</SelectItem>
                      <SelectItem value="high">Alta</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Status</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o status" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="active">Ativo</SelectItem>
                      <SelectItem value="resolved">Resolvido</SelectItem>
                      <SelectItem value="archived">Arquivado</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {form.watch("status") === "resolved" && (
              <FormField
                control={form.control}
                name="resolution_notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notas de Resolução</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Digite as notas de resolução"
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* Área oculta para impressão */}
            <div ref={printRef} style={{ display: 'none' }}>
              <div style={{ padding: '20px', fontFamily: 'Arial' }}>
                <h1 style={{ fontSize: '24px', textAlign: 'center', marginBottom: '30px' }}>
                  REGISTRO DE OCORRÊNCIA
                </h1>
                <div style={{ marginBottom: '20px' }}>
                  <p><strong>Título:</strong> {form.watch('title')}</p>
                  <p><strong>Tipo:</strong> {form.watch('type')}</p>
                  <p><strong>Severidade:</strong> {form.watch('severity') === 'low' ? 'Baixa' : 
                                                      form.watch('severity') === 'medium' ? 'Média' : 'Alta'}</p>
                  <p><strong>Status:</strong> {form.watch('status') === 'active' ? 'Ativa' : 
                                               form.watch('status') === 'resolved' ? 'Resolvida' : 'Arquivada'}</p>
                  <p><strong>Data:</strong> {new Date().toLocaleDateString('pt-BR')}</p>
                </div>
                <div style={{ marginBottom: '20px' }}>
                  <h2 style={{ fontSize: '18px', marginBottom: '10px' }}>Descrição:</h2>
                  <p style={{ whiteSpace: 'pre-wrap' }}>{form.watch('description')}</p>
                </div>
                {form.watch('status') === 'resolved' && form.watch('resolution_notes') && (
                  <div style={{ marginBottom: '20px' }}>
                    <h2 style={{ fontSize: '18px', marginBottom: '10px' }}>Notas de Resolução:</h2>
                    <p style={{ whiteSpace: 'pre-wrap' }}>{form.watch('resolution_notes')}</p>
                  </div>
                )}
              </div>
            </div>

            <DialogFooter className="gap-2">
              <div className="flex gap-2">
                {form.watch('title') && (
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={handleDownloadPDF}
                    className="gap-2"
                  >
                    <Download className="h-4 w-4" />
                    Baixar PDF
                  </Button>
                )}
              </div>
              <div className="flex gap-2">
                <Button type="button" variant="outline" onClick={onClose}>
                  Cancelar
                </Button>
                <Button type="submit">
                  Salvar
                </Button>
              </div>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 