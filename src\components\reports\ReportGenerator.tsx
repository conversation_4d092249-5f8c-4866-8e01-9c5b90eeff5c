import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { FileText, Download, Loader2 } from 'lucide-react';
import { useCurrentClubId } from '@/context/ClubContext';
import { useClubInfoStore } from '@/store/useClubInfoStore';
import { useCategoriesStore } from '@/store/useCategoriesStore';
import { getPlayers, getAccommodations, getCategoryPlayers, getAccommodationCollaborators } from '@/api/api';
import {
  generatePlayerReport,
  generateAccommodationReport,
  generatePlayersByCategoryReport,
  generateWeeklyMenuReport,
  generateClothingControlReport,
  generateClothingReport
} from '@/utils/pdfGenerator';
import { generateCategoryReport } from '@/utils/categoryReportGenerator';
import { generateLoanReport } from '@/utils/loanReportGenerator';
import { generateMedicalReport } from '@/utils/medicalReportGenerator';
import { generateFeedingControlReport } from '@/utils/feedingControlReportGenerator';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { usePermission } from '@/hooks/usePermission';
import { PermissionControl } from '@/components/PermissionControl';
import { REPORT_PERMISSIONS } from '@/constants/permissions';
import { WeeklyMenuDialog, WeeklyMenuMeal } from './WeeklyMenuDialog';

export function ReportGenerator() {
  const { toast } = useToast();
  const clubId = useCurrentClubId();
  const { clubInfo, fetchClubInfo } = useClubInfoStore();
  const { categories, fetchCategories } = useCategoriesStore();
  const { can } = usePermission();
  const [reportType, setReportType] = useState<string>('players');
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);

  // Estados para os novos tipos de relatório
  const [doctorName, setDoctorName] = useState<string>('');
  const [includeSignature, setIncludeSignature] = useState<boolean>(true);
  const [mealType, setMealType] = useState<string>('Almoço');
  const [supervisorName, setSupervisorName] = useState<string>('');
  const [weeklyMenuOpen, setWeeklyMenuOpen] = useState(false);

  // Pré-carregar as informações do clube e categorias quando o componente for montado
  useEffect(() => {
    if (!clubInfo) {
      fetchClubInfo(clubId);
    }

    // Carregar categorias
    fetchCategories(clubId);
  }, [clubId, clubInfo, fetchClubInfo, fetchCategories]);

  // Buscar informações do clube se ainda não estiverem disponíveis
  const ensureClubInfo = async () => {
    if (!clubInfo) {
      await fetchClubInfo(clubId);
      // Após buscar, precisamos obter o valor atualizado do store
      return useClubInfoStore.getState().clubInfo;
    }
    return clubInfo;
  };

  const handleGenerateReport = async () => {
    if (!can(REPORT_PERMISSIONS.GENERATE)) {
      toast({
        title: 'Permissão negada',
        description: 'Você não pode gerar relatórios',
        variant: 'destructive'
      });
      return;
    }
    try {
      setLoading(true);

      // Garantir que temos as informações do clube
      const info = await ensureClubInfo();

      if (!info) {
        throw new Error('Não foi possível obter as informações do clube');
      }

      if (reportType === 'players') {
        // Buscar jogadores
        const players = await getPlayers(clubId);

        // Buscar categorias para cada jogador
        const playersWithCategories = await Promise.all(
          players.map(async (player) => {
            // Usar a função auxiliar para buscar a categoria do jogador
            const categoryName = await getPlayerCategoriesHelper(clubId, player.id);

            // Adicionar a categoria ao jogador
            return {
              ...player,
              category: categoryName
            };
          })
        );

        // Log para depuração
        console.log('Jogadores com categorias:', playersWithCategories);

        // Gerar relatório de jogadores
        await generatePlayerReport(playersWithCategories, info);

        toast({
          title: 'Relatório gerado com sucesso',
          description: 'O relatório de jogadores foi baixado para o seu computador.',
        });
      } else if (reportType === 'accommodations') {
        // Buscar alojamentos
        const accommodations = await getAccommodations(clubId);

        // Para cada alojamento, buscar os jogadores e colaboradores alojados
        const accommodationsWithGuests = await Promise.all(
          accommodations.map(async (accommodation) => {
            const [players, collaborators] = await Promise.all([
              getAccommodationPlayers(clubId, accommodation.id),
              getAccommodationCollaborators(clubId, accommodation.id)
            ]);
            return {
              ...accommodation,
              players,
              collaborators,
            };
          })
        );

        // Log para depuração
        console.log('Dados para o relatório de alojamentos:', accommodationsWithGuests);

        // Gerar relatório de alojamentos (sem incluir jogadores sem alojamento)
        await generateAccommodationReport(accommodationsWithGuests, info, {});

        toast({
          title: 'Relatório gerado com sucesso',
          description: 'O relatório de alojamentos foi baixado para o seu computador.',
        });
      } else if (reportType === 'category') {
        // Verificar se uma categoria foi selecionada
        if (!selectedCategoryId) {
          toast({
            title: 'Categoria não selecionada',
            description: 'Por favor, selecione uma categoria para gerar o relatório.',
            variant: 'destructive',
          });
          setLoading(false);
          return;
        }

        // Buscar informações da categoria
        const categoryId = parseInt(selectedCategoryId);
        const category = categories.find(c => c.id === categoryId);

        if (!category) {
          toast({
            title: 'Categoria não encontrada',
            description: 'A categoria selecionada não foi encontrada.',
            variant: 'destructive',
          });
          setLoading(false);
          return;
        }

        // Buscar jogadores da categoria (excluindo inativos e emprestados)
        const categoryPlayers = await getCategoryPlayers(clubId, categoryId, {
          includeInactive: false,
          includeLoaned: false
        });

        // Agrupar jogadores por categoria
        const playersByCategory = {
          [category.name]: categoryPlayers
        };

        // Gerar relatório de jogadores por categoria
        await generatePlayersByCategoryReport(playersByCategory, info);

        toast({
          title: 'Relatório gerado com sucesso',
          description: `O relatório de jogadores da categoria ${category.name} foi baixado para o seu computador.`,
        });
      } else if (reportType === 'loaned') {
        // Buscar todos os jogadores
        const players = await getPlayers(clubId);

        // Gerar relatório de jogadores emprestados
        await generateLoanReport(players, info);

        toast({
          title: 'Relatório gerado com sucesso',
          description: 'O relatório de jogadores emprestados foi baixado para o seu computador.',
        });
      } else if (reportType === 'medical') {
        // Verificar se uma categoria foi selecionada
        if (!selectedCategoryId) {
          toast({
            title: 'Categoria não selecionada',
            description: 'Por favor, selecione uma categoria para gerar o relatório médico.',
            variant: 'destructive',
          });
          setLoading(false);
          return;
        }

        // Buscar informações da categoria
        const categoryId = parseInt(selectedCategoryId);
        const category = categories.find(c => c.id === categoryId);

        if (!category) {
          toast({
            title: 'Categoria não encontrada',
            description: 'A categoria selecionada não foi encontrada.',
            variant: 'destructive',
          });
          setLoading(false);
          return;
        }

        // Buscar jogadores da categoria
        const categoryPlayers = await getCategoryPlayers(clubId, categoryId, {
          includeInactive: false,
          includeLoaned: false
        });

        if (categoryPlayers.length === 0) {
          toast({
            title: 'Nenhum jogador encontrado',
            description: 'Não há jogadores ativos nesta categoria.',
            variant: 'destructive',
          });
          setLoading(false);
          return;
        }

        // Dados médicos fictícios para o exemplo
        const medicalData = {
          diagnosis: 'Preencher manualmente',
          treatment: 'Preencher manualmente',
          observations: 'Preencher manualmente',
          recommendations: 'Preencher manualmente'
        };

        // Gerar relatório médico para o primeiro jogador
        const blob = await generateMedicalReport(
          categoryPlayers[0],
          medicalData,
          info,
          includeSignature,
          doctorName
        );

        // Criar URL para download
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `relatorio-medico-${categoryPlayers[0].name.replace(/\s+/g, '-').toLowerCase()}.pdf`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);

        toast({
          title: 'Relatório gerado com sucesso',
          description: 'O relatório médico foi baixado para o seu computador.',
        });
      } else if (reportType === 'clothing') {
        // Verificar se uma categoria foi selecionada
        if (!selectedCategoryId) {
          toast({
            title: 'Categoria não selecionada',
            description: 'Por favor, selecione uma categoria para gerar o relatório de rouparia.',
            variant: 'destructive',
          });
          setLoading(false);
          return;
        }

        // Buscar informações da categoria
        const categoryId = parseInt(selectedCategoryId);
        const category = categories.find(c => c.id === categoryId);

        if (!category) {
          toast({
            title: 'Categoria não encontrada',
            description: 'A categoria selecionada não foi encontrada.',
            variant: 'destructive',
          });
          setLoading(false);
          return;
        }

        // Buscar jogadores da categoria
        const categoryPlayers = await getCategoryPlayers(clubId, categoryId, {
          includeInactive: false,
          includeLoaned: false
        });

        // Preparar dados para o relatório de controle de rouparia
        const clothingData = [{
          category: category.name,
          players: categoryPlayers.map(player => ({
            id: player.id,
            name: player.name,
            birthdate: player.birthdate,
            shirt_size: player.shirt_size || '-',
            shorts_size: player.shorts_size || '-',
            sock_size: player.sock_size || '-',
            shoes_size: player.shoes_size || '-',
            training_shirt_size: player.training_shirt_size || '-',
            training_shorts_size: player.training_shorts_size || '-',
            jacket_size: player.jacket_size || '-',
            pants_size: player.pants_size || '-'
          }))
        }];

        // Gerar relatório de controle de rouparia
        await generateClothingControlReport(clothingData, info);

        toast({
          title: 'Relatório gerado com sucesso',
          description: 'O relatório de controle de rouparia foi baixado para o seu computador.',
        });
      } else if (reportType === 'clothing-report') {
        // Verificar se uma categoria foi selecionada
        if (!selectedCategoryId) {
          toast({
            title: 'Categoria não selecionada',
            description: 'Por favor, selecione uma categoria para gerar o relatório de rouparia.',
            variant: 'destructive',
          });
          setLoading(false);
          return;
        }

        // Buscar informações da categoria
        const categoryId = parseInt(selectedCategoryId);
        const category = categories.find(c => c.id === categoryId);

        if (!category) {
          toast({
            title: 'Categoria não encontrada',
            description: 'A categoria selecionada não foi encontrada.',
            variant: 'destructive',
          });
          setLoading(false);
          return;
        }

        // Buscar jogadores da categoria
        const categoryPlayers = await getCategoryPlayers(clubId, categoryId, {
          includeInactive: false,
          includeLoaned: false
        });

        // Preparar dados para o relatório de rouparia
        const clothingReportData = [{
          category: category.name,
          players: categoryPlayers.map(player => ({
            id: player.id,
            name: player.name,
            nickname: player.nickname,
            birthdate: player.birthdate,
            position: player.position,
            registration_number: player.registration_number
          }))
        }];

        // Gerar relatório de rouparia
        await generateClothingReport(clothingReportData, info);

        toast({
          title: 'Relatório gerado com sucesso',
          description: 'O relatório de rouparia foi baixado para o seu computador.',
        });
      } else if (reportType === 'menu') {
        // Abrir modal para preenchimento do cardápio
        setWeeklyMenuOpen(true);
        setLoading(false);
        return;
      } else if (reportType === 'feeding') {
        // Verificar se uma categoria foi selecionada
        if (!selectedCategoryId) {
          toast({
            title: 'Categoria não selecionada',
            description: 'Por favor, selecione uma categoria para gerar o relatório de controle de alimentação.',
            variant: 'destructive',
          });
          setLoading(false);
          return;
        }

        // Verificar se o supervisor foi informado
        if (!supervisorName.trim()) {
          toast({
            title: 'Supervisor não informado',
            description: 'Por favor, informe o nome do supervisor responsável.',
            variant: 'destructive',
          });
          setLoading(false);
          return;
        }

        // Buscar informações da categoria
        const categoryId = parseInt(selectedCategoryId);
        const category = categories.find(c => c.id === categoryId);

        if (!category) {
          toast({
            title: 'Categoria não encontrada',
            description: 'A categoria selecionada não foi encontrada.',
            variant: 'destructive',
          });
          setLoading(false);
          return;
        }

        // Buscar jogadores da categoria
        const categoryPlayers = await getCategoryPlayers(clubId, categoryId, {
          includeInactive: false,
          includeLoaned: false
        });

        // Gerar relatório de controle de alimentação
        const blob = await generateFeedingControlReport(
          categoryPlayers,
          supervisorName,
          mealType,
          info
        );

        // Criar URL para download
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `controle-alimentacao-${category.name.replace(/\s+/g, '-').toLowerCase()}.pdf`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);

        toast({
          title: 'Relatório gerado com sucesso',
          description: 'O relatório de controle de alimentação foi baixado para o seu computador.',
        });
      }
    } catch (error) {
      console.error('Erro ao gerar relatório:', error);
      toast({
        title: 'Erro',
        description: 'Não foi possível gerar o relatório. Tente novamente mais tarde.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Gerar Relatórios</CardTitle>
        <CardDescription>
          Gere relatórios em PDF com informações do clube, jogadores e alojamentos.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Tipo de Relatório</label>
            <Select
              value={reportType}
              onValueChange={setReportType}
              disabled={loading}
            >
              <SelectTrigger>
                <SelectValue placeholder="Selecione o tipo de relatório" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="players">Todos os Jogadores</SelectItem>
                <SelectItem value="category">Jogadores por Categoria</SelectItem>
                <SelectItem value="loaned">Jogadores Emprestados</SelectItem>
                <SelectItem value="accommodations">Alojamentos</SelectItem>
                <SelectItem value="clothing-report">Relatório de Rouparia</SelectItem>
                <SelectItem value="menu">Cardápio Semanal</SelectItem>
                <SelectItem value="feeding">Controle Alimentação Base</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Seleção de categoria (aparece para vários tipos de relatório) */}
          {(reportType === 'category' || reportType === 'medical' || reportType === 'clothing' || reportType === 'clothing-report' || reportType === 'feeding') && (
            <div className="space-y-2 mt-4">
              <label className="text-sm font-medium">Categoria</label>
              <Select
                value={selectedCategoryId}
                onValueChange={setSelectedCategoryId}
                disabled={loading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione uma categoria" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map(category => (
                    <SelectItem key={category.id} value={category.id.toString()}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Campos para relatório médico */}
          {reportType === 'medical' && (
            <div className="space-y-4 mt-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Nome do Médico Responsável</label>
                <Input
                  placeholder="Digite o nome do médico"
                  value={doctorName}
                  onChange={(e) => setDoctorName(e.target.value)}
                  disabled={loading}
                />
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeSignature"
                  checked={includeSignature}
                  onCheckedChange={(checked) => setIncludeSignature(checked as boolean)}
                  disabled={loading}
                />
                <Label htmlFor="includeSignature">Incluir campo para assinatura digital</Label>
              </div>
            </div>
          )}

          {/* Campos para relatório de controle de alimentação */}
          {reportType === 'feeding' && (
            <div className="space-y-4 mt-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Supervisor Responsável</label>
                <Input
                  placeholder="Digite o nome do supervisor"
                  value={supervisorName}
                  onChange={(e) => setSupervisorName(e.target.value)}
                  disabled={loading}
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Tipo de Alimentação</label>
                <Select
                  value={mealType}
                  onValueChange={setMealType}
                  disabled={loading}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o tipo de alimentação" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Café da Manhã">Café da Manhã</SelectItem>
                    <SelectItem value="Almoço">Almoço</SelectItem>
                    <SelectItem value="Lanche da Tarde">Lanche da Tarde</SelectItem>
                    <SelectItem value="Jantar">Jantar</SelectItem>
                    <SelectItem value="Ceia">Ceia</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}

          <div className="bg-muted/50 p-4 rounded-md mt-4">
            <div className="flex items-start gap-3">
              <FileText className="h-10 w-10 text-muted-foreground" />
              <div>
                <h3 className="font-medium">
                  {reportType === 'players' ? 'Relatório de Todos os Jogadores' :
                   reportType === 'category' ? 'Relatório de Jogadores por Categoria' :
                   reportType === 'loaned' ? 'Relatório de Jogadores Emprestados' :
                   reportType === 'accommodations' ? 'Relatório de Alojamentos' :
                   reportType === 'medical' ? 'Relatório Médico' :
                   reportType === 'clothing-report' ? 'Relatório de Rouparia' :
                   reportType === 'menu' ? 'Cardápio Semanal' :
                   'Controle de Alimentação Base'}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {reportType === 'players'
                    ? 'Lista completa de jogadores do clube com suas informações principais.'
                    : reportType === 'category'
                    ? 'Lista de jogadores de uma categoria específica, organizados por posição.'
                    : reportType === 'loaned'
                    ? 'Lista de jogadores emprestados com informações sobre o clube de destino e data de retorno.'
                    : reportType === 'accommodations'
                    ? 'Informações sobre alojamentos, jogadores e colaboradores alojados.'
                    : reportType === 'medical'
                    ? 'Relatório médico com campos para diagnóstico, tratamento, observações e recomendações, com opção de assinatura digital.'
                    : reportType === 'clothing'
                    ? 'Controle de rouparia por categoria, com campos para marcação manual de itens entregues.'
                    : reportType === 'clothing-report'
                    ? 'Relatório de rouparia com colunas para Posição, Nome, Apelido, Data Nascimento, NC (vazio) e 7 colunas vazias separadas por bordas para preenchimento manual.'
                    : reportType === 'menu'
                    ? 'Modelo de cardápio semanal com campos para preenchimento manual das refeições diárias.'
                    : 'Controle de alimentação da base com lista de jogadores e campos para assinaturas.'}
                </p>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <PermissionControl permission={REPORT_PERMISSIONS.GENERATE}>
          <Button
            onClick={handleGenerateReport}
            disabled={loading}
            className="w-full"
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Gerando relatório...
              </>
          ) : (
            <>
              <Download className="mr-2 h-4 w-4" />
              Gerar Relatório
            </>
          )}
          </Button>
        </PermissionControl>
      </CardFooter>
      <WeeklyMenuDialog
        open={weeklyMenuOpen}
        onOpenChange={setWeeklyMenuOpen}
        onConfirm={async (meals) => {
          setLoading(true);
          const info = await ensureClubInfo();
          if (!info) {
            toast({
              title: 'Erro',
              description: 'Não foi possível obter as informações do clube.',
              variant: 'destructive',
            });
            setLoading(false);
            return;
          }
          const start = new Date();
          const end = new Date(start.getTime() + 6 * 24 * 60 * 60 * 1000);
          const week = `${start.getDate()}/${start.getMonth() + 1}/${start.getFullYear()} - ${end.getDate()}/${end.getMonth() + 1}/${end.getFullYear()}`;
          await generateWeeklyMenuReport({ week, meals }, info);
          toast({
            title: 'Relatório gerado com sucesso',
            description: 'O relatório de cardápio semanal foi baixado para o seu computador.',
          });
          setLoading(false);
        }}
      />
    </Card>
  );
}

// Função auxiliar para buscar categorias de um jogador
async function getPlayerCategoriesHelper(clubId: number, playerId: string) {
  try {
    // Usar o Supabase diretamente para evitar problemas com a função getPlayerCategories
    const { supabase } = await import('@/integrations/supabase/client');

    // Buscar as categorias do jogador diretamente
    const { data, error } = await supabase
      .from("player_categories")
      .select(`
        category:category_id(id, name, type)
      `)
      .eq("club_id", clubId as any)
      .eq("player_id", playerId as any);

    if (error) {
      console.error(`Erro ao buscar categorias do jogador:`, error);
      return '-';
    }

    // Log para depuração
    console.log(`Categorias do jogador ${playerId}:`, data);

    // Verificar se temos dados e extrair o nome da categoria
    if (data && data.length > 0) {
      // Log detalhado para depuração
      console.log('Estrutura de dados da categoria:', JSON.stringify(data[0]));

      // Verificar se temos a categoria
      if (data[0] && 'category' in data[0] && data[0].category && typeof data[0].category === 'object') {
        // @ts-ignore - Sabemos que category existe e é um objeto
        return data[0].category.name || '-';
      }
    }

    // Se não encontrou nenhuma categoria, tentar buscar de outra forma
    const { data: altData, error: altError } = await supabase
      .from("player_categories")
      .select("category_id")
      .eq("club_id", clubId as any)
      .eq("player_id", playerId as any);

    if (!altError && altData && altData.length > 0 && 'category_id' in altData[0] && altData[0].category_id) {
      // Buscar o nome da categoria diretamente
      const { data: categoryData, error: categoryError } = await supabase
        .from("categories")
        .select("name")
        .eq("id", altData[0].category_id)
        .single();

      if (!categoryError && categoryData && 'name' in categoryData) {
        // @ts-ignore - Sabemos que name existe
        return categoryData.name;
      }
    }

    return '-';
  } catch (error) {
    console.error(`Erro ao buscar categorias do jogador ${playerId}:`, error);
    return '-';
  }
}

// Função auxiliar para buscar jogadores de um alojamento
async function getAccommodationPlayers(clubId: number, accommodationId: number) {
  try {
    // Importar a função de forma dinâmica para evitar problemas de importação circular
    const { getAccommodationPlayers, getPlayerById } = await import('@/api/api');
    const playerAccommodations = await getAccommodationPlayers(clubId, accommodationId);

    // Log para depuração
    console.log(`Jogadores do alojamento ${accommodationId}:`, playerAccommodations);

    // Buscar informações detalhadas de cada jogador
    const playersWithDetails = await Promise.all(
      playerAccommodations.map(async (pa) => {
        try {
          // Se temos um player_id, buscar informações detalhadas do jogador
          if (pa.player_id) {
            const playerDetails = await getPlayerById(clubId, pa.player_id);

            // Buscar categoria do jogador
            const categoryName = await getPlayerCategoriesHelper(clubId, pa.player_id);

            return {
              name: playerDetails.name || pa.player_name || 'Jogador sem nome',
              nickname: playerDetails.nickname || pa.player_nickname || '-',
              birthdate: playerDetails.birthdate || pa.player_birthdate || null,
              entry_date: playerDetails.entry_date || pa.player_entry_date || null,
              position: playerDetails.position || '-',
              number: playerDetails.number || '-',
              state: playerDetails.state || '-', // Estado do jogador
              referred_by: playerDetails.referred_by || '-', // Quem indicou o jogador
              room: pa.room_number || '-',
              since: pa.check_in_date || null,
              category: categoryName
            };
          } else {
            // Caso não tenha player_id, usar as informações disponíveis
            return {
              name: pa.player_name || 'Jogador sem nome',
              nickname: pa.player_nickname || '-',
              birthdate: pa.player_birthdate || null,
              entry_date: pa.player_entry_date || null,
              position: '-',
              number: '-',
              state: '-',
              referred_by: '-',
              room: pa.room_number || '-',
              since: pa.check_in_date || null,
              category: '-'
            };
          }
        } catch (playerError) {
          console.error('Erro ao buscar detalhes do jogador:', playerError);
          return {
            name: pa.player_name || 'Jogador sem nome',
            nickname: pa.player_nickname || '-',
            birthdate: pa.player_birthdate || null,
            entry_date: pa.player_entry_date || null,
            position: '-',
            number: '-',
            state: '-',
            referred_by: '-',
            room: pa.room_number || '-',
            since: pa.check_in_date || null,
            category: '-'
          };
        }
      })
    );

    return playersWithDetails;
  } catch (error) {
    console.error('Erro ao buscar jogadores do alojamento:', error);
    return [];
  }
}



