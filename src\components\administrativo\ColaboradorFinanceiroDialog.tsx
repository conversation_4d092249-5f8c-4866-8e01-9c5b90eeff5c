import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Collaborator, getSalaryAdvances } from "@/api/api";
import { CollaboradorFinances } from "./CollaboradorFinances";
import { AdicionarValeDialog } from "@/components/financeiro/AdicionarValeDialog";
import { useToast } from "@/components/ui/use-toast";
import { Plus } from "lucide-react";

interface ColaboradorFinanceiroDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: number;
  collaborator: Collaborator;
  onSuccess?: () => void;
}

export function ColaboradorFinanceiroDialog({
  open,
  onOpenChange,
  clubId,
  collaborator,
  onSuccess
}: ColaboradorFinanceiroDialogProps) {
  const [activeTab, setActiveTab] = useState("finances");
  const [adicionarValeDialogOpen, setAdicionarValeDialogOpen] = useState(false);
  const [salaryAdvances, setSalaryAdvances] = useState<any[]>([]);
  const { toast } = useToast();

  // Carregar vales quando o diálogo abrir
  useEffect(() => {
    if (open) {
      loadSalaryAdvances();
    }
  }, [open, clubId]);

  const loadSalaryAdvances = async () => {
    try {
      const advances = await getSalaryAdvances(clubId);
      // Garantir que todos os IDs sejam números
      const formattedAdvances = advances.map(adv => ({
        ...adv,
        id: Number(adv.id),
        person_id: Number(adv.person_id)
      }));
      setSalaryAdvances(formattedAdvances);
    } catch (error) {
      console.error("Erro ao carregar vales:", error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar os vales. Por favor, tente novamente.",
        variant: "destructive"
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0 flex flex-row items-center justify-between">
          <DialogTitle>Dados Financeiros - {collaborator.full_name}</DialogTitle>
          <Button
            onClick={() => setAdicionarValeDialogOpen(true)}
            size="sm"
            className="bg-orange-500 hover:bg-orange-600"
          >
            <Plus className="h-4 w-4 mr-2" />
            Adicionar Vale
          </Button>
        </DialogHeader>

        <div className="overflow-y-auto flex-grow">
          <CollaboradorFinances
            collaboratorId={collaborator.id}
            canEdit={true}
            salaryAdvances={salaryAdvances}
            setSalaryAdvances={setSalaryAdvances}
            collaborator={collaborator}
          />
        </div>
      </DialogContent>

      {/* Diálogo para adicionar vale */}
      <AdicionarValeDialog
        open={adicionarValeDialogOpen}
        onOpenChange={setAdicionarValeDialogOpen}
        personId={collaborator.id}
        personType="collaborator"
        personName={collaborator.full_name}
        personRole={collaborator.role}
        onSuccess={async () => {
          await loadSalaryAdvances();
          toast({
            title: "Vale registrado",
            description: "O adiantamento foi registrado com sucesso",
          });
          if (onSuccess) onSuccess();
        }}
      />
    </Dialog>
  );
}
