import { useState, useEffect } from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardH<PERSON>er,
  CardTitle,
  CardDescription
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { toast } from "@/components/ui/use-toast";
import {
  Plus,
  Pencil,
  Trash2,
  Calendar,
  User,
  Clock,
  Settings,
  Users,
  Filter
} from "lucide-react";
import { AdministrativeTask, Collaborator, getCollaborators } from "@/api/api";
import { useAdministrativeTasksStore } from "@/store/useAdministrativeTasksStore";
import { NovaTarefaDialog } from "@/components/administrativo/NovaTarefaDialog";
import { EditarTarefaDialog } from "@/components/administrativo/EditarTarefaDialog";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";
import { DragDropContext, Droppable, Draggable, DropResult } from "react-beautiful-dnd";
import { GerenciarTiposTarefaDialog } from "@/components/administrativo/GerenciarTiposTarefaDialog";
import { supabase } from "@/integrations/supabase/client";
import type { Database } from "@/integrations/supabase/types";
import { usePermission } from "@/hooks/usePermission";
import { ADMINISTRATIVE_PERMISSIONS } from "@/constants/permissions";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";

type TaskType = Database['public']['Tables']['task_types']['Row'];

interface TarefasKanbanTabProps {
  tasks: AdministrativeTask[];
  loading: boolean;
  error: string | null;
  clubId: number;
  currentCollaboratorId?: number | null;
}

export function TarefasKanbanTab({ tasks, loading, error, clubId, currentCollaboratorId }: TarefasKanbanTabProps) {
  const [novaTarefaDialogOpen, setNovaTarefaDialogOpen] = useState(false);
  const [editarTarefaDialogOpen, setEditarTarefaDialogOpen] = useState(false);
  const [excluirTarefaDialogOpen, setExcluirTarefaDialogOpen] = useState(false);
  const [gerenciarTiposDialogOpen, setGerenciarTiposDialogOpen] = useState(false);
  const [tarefaParaExcluir, setTarefaParaExcluir] = useState<AdministrativeTask | null>(null);
  const [taskTypes, setTaskTypes] = useState<TaskType[]>([]);
  const [loadingTaskTypes, setLoadingTaskTypes] = useState(false);
  const [collaborators, setCollaborators] = useState<Collaborator[]>([]);
  const [loadingCollaborators, setLoadingCollaborators] = useState(false);
  const [selectedCollaboratorId, setSelectedCollaboratorId] = useState<string | null>(null);

  // We don't need the user object anymore since we're using the permission hook

  const {
    updateTaskStatus,
    removeTask,
    getTasksByStatus
  } = useAdministrativeTasksStore();

    // Permission checks
    const { can } = usePermission();
    const canCreate = can(ADMINISTRATIVE_PERMISSIONS.TASKS.CREATE);
    const canEdit = can(ADMINISTRATIVE_PERMISSIONS.TASKS.EDIT);
    const canDelete = can(ADMINISTRATIVE_PERMISSIONS.TASKS.DELETE);
    const canViewAll = can(ADMINISTRATIVE_PERMISSIONS.TASKS.VIEW);  

  const [selectedTask, setSelectedTask] = useState<AdministrativeTask | null>(null);

  useEffect(() => {
    fetchTaskTypes();
    fetchCollaborators();
  }, [clubId, currentCollaboratorId, canViewAll]);

  const fetchTaskTypes = async () => {
    setLoadingTaskTypes(true);
    try {
      const { data, error } = await supabase
        .from("task_types")
        .select("*")
        .eq("club_id", clubId)
        .order("position", { ascending: true });

      if (error) {
        throw new Error(`Erro ao buscar tipos de tarefas: ${error.message}`);
      }
      setTaskTypes(data || []);
    } catch (err: any) {
      console.error("Erro ao buscar tipos de tarefas:", err);
      toast({
        title: "Erro",
        description: "Erro ao carregar tipos de tarefas",
        variant: "destructive",
      });
    } finally {
      setLoadingTaskTypes(false);
    }
  };

  const fetchCollaborators = async () => {
    setLoadingCollaborators(true);
    try {
      const clubCollaborators = await getCollaborators(clubId);
      // Sort collaborators alphabetically by name
      clubCollaborators.sort((a, b) => a.full_name.localeCompare(b.full_name));
      // Filter out inactive collaborators
      let activeCollaborators = clubCollaborators.filter(
        collaborator => collaborator.status !== 'inactive'
      );
      if (!canViewAll && currentCollaboratorId) {
        activeCollaborators = activeCollaborators.filter(c => c.id === currentCollaboratorId);
        setSelectedCollaboratorId(currentCollaboratorId.toString());
      } else if (canViewAll) {
        setSelectedCollaboratorId(null);
      }
      setCollaborators(activeCollaborators);
    } catch (error) {
      console.error("Erro ao buscar colaboradores:", error);
      toast({
        title: "Erro",
        description: "Erro ao carregar colaboradores",
        variant: "destructive",
      });
    } finally {
      setLoadingCollaborators(false);
    }
  };

  // Filter tasks by status and selected collaborator
  const getTasksByCustomStatus = (status: string) => {
    return tasks.filter(task => {
      // First filter by status
      const statusMatch = task.status === status;

      // If no collaborator is selected or the user is viewing their own tasks, show all tasks with matching status
      if (!selectedCollaboratorId) {
        return statusMatch;
      }

      // Otherwise, filter by the selected collaborator
      return statusMatch && task.collaborator_id === parseInt(selectedCollaboratorId);
    });
  };

  const handleEditTask = (task: AdministrativeTask) => {
    setSelectedTask(task);
    setEditarTarefaDialogOpen(true);
  };

  const handleDeleteTask = (task: AdministrativeTask) => {
    setTarefaParaExcluir(task);
    setExcluirTarefaDialogOpen(true);
  };

  const confirmDeleteTask = async () => {
    if (tarefaParaExcluir) {
      try {
        await removeTask(clubId, tarefaParaExcluir.id);
        toast({
          title: "Tarefa excluída",
          description: "A tarefa foi excluída com sucesso.",
        });
      } catch (error) {
        toast({
          title: "Erro ao excluir tarefa",
          description: "Ocorreu um erro ao excluir a tarefa.",
          variant: "destructive",
        });
      }
    }
    setExcluirTarefaDialogOpen(false);
  };

  const handleDragEnd = async (result: DropResult) => {
    const { source, destination, draggableId } = result;

    if (!destination) return;

    if (
      source.droppableId === destination.droppableId &&
      source.index === destination.index
    ) return;

    const taskId = parseInt(draggableId.replace('task-', ''));

    const newStatus = destination.droppableId;

    try {
      await updateTaskStatus(clubId, taskId, newStatus);
    } catch (error) {
      toast({
        title: "Erro ao mover tarefa",
        description: "Ocorreu um erro ao atualizar o status da tarefa.",
        variant: "destructive",
      });
    }
  };

  // Check if user has permission to delete tasks
  const canDeleteTasks = () => {
    return canDelete;
  };

  const renderTaskCard = (task: AdministrativeTask) => (
    <Draggable key={task.id} draggableId={`task-${task.id}`} index={task.id}>
      {(provided) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          className="bg-white p-3 rounded-md shadow-sm mb-2 border border-gray-200"
        >
          <div className="flex justify-between items-start">
            <h3 className="font-medium text-sm">{task.title}</h3>
            <div className="flex gap-1">
              {canEdit && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6"
                  onClick={() => handleEditTask(task)}
                >
                  <Pencil className="h-3 w-3" />
                </Button>
              )}
              {canDeleteTasks() && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6"
                  onClick={() => handleDeleteTask(task)}
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              )}
            </div>
          </div>

          {task.description && (
            <p className="text-xs text-gray-500 mt-1">{task.description}</p>
          )}

          <div className="flex flex-wrap gap-2 mt-2">
            {task.collaborator_name && (
              <div className="flex items-center text-xs text-gray-500">
                <User className="h-3 w-3 mr-1" />
                {task.collaborator_name}
              </div>
            )}

            {task.due_date && (
              <div className="flex items-center text-xs text-gray-500">
                <Calendar className="h-3 w-3 mr-1" />
                {new Date(task.due_date).toLocaleDateString('pt-BR')}
              </div>
            )}
          </div>
        </div>
      )}
    </Draggable>
  );

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Tarefas Diárias (Kanban)</CardTitle>
          <CardDescription>
            Organize e acompanhe as tarefas do clube
          </CardDescription>
        </div>
        <div className="flex gap-2">
          {canEdit && (
            <Button
              variant="outline"
              onClick={() => setGerenciarTiposDialogOpen(true)}
              title="Gerenciar Tipos de Tarefa"
            >
              <Settings className="h-4 w-4 mr-2" />
              Tipos de Tarefa
            </Button>
          )}
          {canCreate && (
            <Button
              onClick={() => {
                setSelectedTask(null);
                setNovaTarefaDialogOpen(true);
              }}
            >
              <Plus className="h-4 w-4 mr-2" />
              Nova Tarefa
            </Button>
          )}
        </div>
      </CardHeader>
      <div className="px-6 pb-2 flex items-center">
        <div className="flex items-center gap-2">
          <Users className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium">Colaborador:</span>
        </div>
        <div className="ml-2 w-64">
        <Select
            value={selectedCollaboratorId || "all"}
            onValueChange={(value) => setSelectedCollaboratorId(value === "all" ? null : value)}
            disabled={!canViewAll}
          >
            <SelectTrigger>
              <SelectValue placeholder={canViewAll ? "Todos os colaboradores" : "Seu cadastro"} />
            </SelectTrigger>
            <SelectContent>
              {canViewAll && <SelectItem value="all">Todos os colaboradores</SelectItem>}
              {loadingCollaborators ? (
                <SelectItem value="loading" disabled>
                  Carregando colaboradores...
                </SelectItem>
              ) : (
                collaborators.map((collaborator) => (
                  <SelectItem key={collaborator.id} value={collaborator.id.toString()}>
                    {collaborator.full_name}
                  </SelectItem>
                ))
              )}
            </SelectContent>
          </Select>
        </div>
      </div>
      <CardContent>
        {loading ? (
          <div className="text-center py-4">Carregando tarefas...</div>
        ) : error ? (
          <div className="text-center py-4 text-red-500">{error}</div>
        ) : (
          <DragDropContext onDragEnd={handleDragEnd}>
            <div className={`grid grid-cols-1 ${taskTypes.length > 2 ? 'md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' : 'md:grid-cols-' + (taskTypes.length === 0 ? 1 : taskTypes.length) } gap-4`}>
              {taskTypes.map(type => {
                const tasksInColumn = getTasksByCustomStatus(type.name);
                let displayName = type.name.charAt(0).toUpperCase() + type.name.slice(1).replace(/_/g, ' ');
                if (type.name === 'a_fazer') displayName = 'A Fazer';
                if (type.name === 'em_andamento') displayName = 'Em Andamento';
                if (type.name === 'concluido') displayName = 'Concluído';

                return (
                  <div
                    key={type.id}
                    className="bg-gray-50 p-4 rounded-md"
                    style={{
                      borderTop: `4px solid ${type.color || '#6b7280'}`, // Default to gray if no color
                    }}
                  >
                    <h3 className="font-medium mb-3 flex items-center" style={{ color: type.color || '#6b7280' }}>
                      {displayName}
                      <span className="ml-2 text-xs bg-gray-200 text-gray-700 px-2 py-0.5 rounded-full">
                        {tasksInColumn.length}
                      </span>
                    </h3>
                    <Droppable droppableId={type.name}>
                      {(provided) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.droppableProps}
                          className="min-h-[200px]"
                        >
                          {tasksInColumn.map(renderTaskCard)}
                          {provided.placeholder}
                        </div>
                      )}
                    </Droppable>
                  </div>
                );
              })}
            </div>
          </DragDropContext>
        )}
      </CardContent>

      {canCreate && (
        <NovaTarefaDialog
          open={novaTarefaDialogOpen}
          onOpenChange={setNovaTarefaDialogOpen}
          clubId={clubId}
          initialCollaboratorId={
            selectedCollaboratorId !== "all" && selectedCollaboratorId
              ? parseInt(selectedCollaboratorId)
              : undefined
          }
        />
      )}

      {canEdit && (
        <EditarTarefaDialog
          open={editarTarefaDialogOpen}
          onOpenChange={setEditarTarefaDialogOpen}
          clubId={clubId}
          task={selectedTask}
        />
      )}

      {canDelete && (
        <ConfirmDialog
          open={excluirTarefaDialogOpen}
          onOpenChange={setExcluirTarefaDialogOpen}
          title="Excluir tarefa"
          description="Tem certeza que deseja excluir esta tarefa? Esta ação não pode ser desfeita."
          onConfirm={confirmDeleteTask}
        />
      )}

      {canEdit && (
        <GerenciarTiposTarefaDialog
          open={gerenciarTiposDialogOpen}
          onOpenChange={setGerenciarTiposDialogOpen}
          clubId={clubId}
          onSuccess={fetchTaskTypes}
        />
      )}
    </Card>
  );
}
