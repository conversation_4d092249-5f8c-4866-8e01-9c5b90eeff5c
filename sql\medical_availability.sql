-- Tabela de disponibilidade de profissionais médicos
CREATE TABLE IF NOT EXISTS medical_availability (
  id SERIAL PRIMARY KEY,
  club_id INTEGER NOT NULL REFERENCES club_info(id) ON DELETE CASCADE,
  professional_id INTEGER NOT NULL REFERENCES medical_professionals(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  capacity INTEGER NOT NULL DEFAULT 1,
  booked INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_medical_availability_club ON medical_availability(club_id);
CREATE INDEX IF NOT EXISTS idx_medical_availability_professional ON medical_availability(professional_id);
CREATE UNIQUE INDEX IF NOT EXISTS uniq_medical_availability_slot ON medical_availability(club_id, professional_id, date, start_time);

ALTER TABLE medical_availability ENABLE ROW LEVEL SECURITY;

CREATE POLICY medical_availability_select ON medical_availability
  FOR SELECT
  USING (club_id IN (SELECT club_id FROM club_members WHERE user_id = auth.uid()));

CREATE POLICY medical_availability_insert ON medical_availability
  FOR INSERT
  WITH CHECK (club_id IN (SELECT club_id FROM club_members WHERE user_id = auth.uid()));

CREATE POLICY medical_availability_update ON medical_availability
  FOR UPDATE
  USING (club_id IN (SELECT club_id FROM club_members WHERE user_id = auth.uid()));

CREATE POLICY medical_availability_delete ON medical_availability
  FOR DELETE
  USING (club_id IN (SELECT club_id FROM club_members WHERE user_id = auth.uid()));
