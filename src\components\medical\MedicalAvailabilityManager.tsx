import { useEffect, useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, DialogContent, DialogHeader, Di<PERSON>Title, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { addDays, addMonths, addWeeks, format, isAfter } from "date-fns";
import { parseISO } from "date-fns";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { useMedicalAvailabilityStore } from "@/store/useMedicalAvailabilityStore";
import { getMedicalProfessionalByUserId, getMedicalProfessionals, MedicalProfessional } from "@/api/api";
import { useToast } from "@/components/ui/use-toast";
import { ptBR } from "date-fns/locale";
import { usePermission } from "@/hooks/usePermission";

type Slot = {
  startTime: string;
  endTime: string;
  capacity: string;
};

export function MedicalAvailabilityManager() {
  const { toast } = useToast();
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const { role } = usePermission();
  const { availabilities, fetchAvailability, addAvailability, deleteAvailability, loading } = useMedicalAvailabilityStore();

  const [professional, setProfessional] = useState<MedicalProfessional | null>(null);
  const [professionals, setProfessionals] = useState<MedicalProfessional[]>([]);
  const [dialogOpen, setDialogOpen] = useState(false);

  const [startDate, setStartDate] = useState<Date | undefined>(new Date());
  const [endDate, setEndDate] = useState<Date | undefined>(new Date());
  const [frequency, setFrequency] = useState("once");
  const [slots, setSlots] = useState<Slot[]>([
    { startTime: "08:00", endTime: "09:00", capacity: "1" }
  ]);
  const daysOfWeek = ["Dom", "Seg", "Ter", "Qua", "Qui", "Sex", "Sáb"];
  const defaultWeek = () => daysOfWeek.map(() => [{ startTime: "08:00", endTime: "09:00", capacity: "1" } as Slot]);
  const [weekSlots, setWeekSlots] = useState<Slot[][]>(defaultWeek());

  useEffect(() => {
    if (clubId && user?.id && role) {
      loadProfessional();
    }
  }, [clubId, user?.id, role]);

  async function loadProfessional() {
    try {
      if (role === "medical") {
        const prof = await getMedicalProfessionalByUserId(clubId, user?.id || "");
        if (prof) {
          setProfessional(prof);
          const todayStr = format(new Date(), "yyyy-MM-dd");
          fetchAvailability(clubId, prof.id, todayStr);
        }
      } else {
        const list = await getMedicalProfessionals(clubId);
        setProfessionals(list);
        if (list.length > 0) {
          setProfessional(list[0]);
          const todayStr = format(new Date(), "yyyy-MM-dd");
          fetchAvailability(clubId, list[0].id, todayStr);
        }
      }
    } catch (err) {
      console.error(err);
    }
  }

  async function handleAdd() {
    if (!professional || !startDate) return;
    console.log('Salvar disponibilidade', professional.id);

    const rangeEnd = endDate && !isAfter(startDate, endDate) ? endDate : startDate;

    const dates: { date: Date; slots: Slot[] }[] = [];

    if (frequency === "weekly" || frequency === "monthly") {
      const weeks = frequency === "monthly" ? 4 : 1;
      for (let w = 0; w < weeks; w++) {
        for (let d = 0; d < 7; d++) {
          const day = addDays(startDate, w * 7 + d);
          if (isAfter(day, rangeEnd) || weekSlots[d].length === 0) continue;
          dates.push({ date: day, slots: weekSlots[d] });
        }
      }
    } else {
      for (let d = new Date(startDate); !isAfter(d, rangeEnd); ) {
        dates.push({ date: new Date(d), slots });
        if (frequency === "daily") d = addDays(d, 1);
        else break;
      }
    }

    try {
      for (const item of dates) {
        for (const slot of item.slots) {
          await addAvailability(clubId, user?.id || "", {
            professional_id: professional.id,
            date: format(item.date, "yyyy-MM-dd"),
            start_time: slot.startTime,
            end_time: slot.endTime,
            capacity: Number(slot.capacity),
          });
        }
      }
      toast({ title: "Horário(s) adicionados" });
      setDialogOpen(false);
      fetchAvailability(clubId, professional.id, format(new Date(), "yyyy-MM-dd"));
    } catch (err) {
      toast({
        title: "Erro",
        description: "Não foi possível adicionar o horário",
        variant: "destructive",
      });
    }
  }

  async function handleDelete(id: number) {
    if (!professional) return;
    await deleteAvailability(clubId, user?.id || "", id);
    const todayStr = format(new Date(), "yyyy-MM-dd");
    fetchAvailability(clubId, professional.id, todayStr);
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Disponibilidade</CardTitle>
      </CardHeader>
      <CardContent>
        {(role === "admin" || role === "president") && (
          <div className="mb-4 space-y-2">
            <Label>Médico</Label>
            <Select
              value={professional ? String(professional.id) : ""}
              onValueChange={async (val) => {
                const prof = professionals.find(p => p.id === Number(val)) || null;
                setProfessional(prof);
                if (prof) {
                  const todayStr = format(new Date(), "yyyy-MM-dd");
                  fetchAvailability(clubId, prof.id, todayStr);
                }
              }}
            >
              <SelectTrigger className="w-full">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {professionals.map(p => (
                  <SelectItem key={p.id} value={String(p.id)}>{p.name}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}
        <div className="mb-4">
          <Button onClick={() => setDialogOpen(true)}>Adicionar Horário</Button>
        </div>
        {loading ? (
          <p>Carregando...</p>
        ) : availabilities.length === 0 ? (
          <p>Nenhum horário cadastrado.</p>
        ) : (
          <ul className="space-y-2">
            {availabilities.map(a => (
              <li key={a.id} className="flex justify-between border p-2 rounded-md">
                <span>{format(parseISO(`${a.date}T00:00:00`), "dd/MM/yyyy")} - {a.start_time} às {a.end_time} ({a.booked}/{a.capacity})</span>
                <Button variant="outline" size="sm" onClick={() => handleDelete(a.id)}>Excluir</Button>
              </li>
            ))}
          </ul>
        )}
      </CardContent>

      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>Novo Horário</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label>Data inicial</Label>
              <div className="col-span-3">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-full justify-start text-left font-normal">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {startDate ? format(startDate, "PPP", { locale: ptBR }) : <span>Selecione</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar mode="single" selected={startDate} onSelect={setStartDate} locale={ptBR} />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label>Data final</Label>
              <div className="col-span-3">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-full justify-start text-left font-normal">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {endDate ? format(endDate, "PPP", { locale: ptBR }) : <span>Selecione</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar mode="single" selected={endDate} onSelect={setEndDate} locale={ptBR} />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label>Repetir</Label>
              <Select value={frequency} onValueChange={setFrequency}>
                <SelectTrigger className="col-span-3">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="once">Nenhuma</SelectItem>
                  <SelectItem value="daily">Diariamente</SelectItem>
                  <SelectItem value="weekly">Semanalmente</SelectItem>
                  <SelectItem value="monthly">Mensalmente</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {frequency === "weekly" || frequency === "monthly" ? (
              weekSlots.map((daySlots, dayIdx) => (
                <div key={dayIdx} className="border rounded-md p-2 space-y-2">
                  <div className="flex justify-between items-center">
                    <Label className="font-bold">{daysOfWeek[dayIdx]}</Label>
                    {daySlots.length > 0 && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() =>
                          setWeekSlots(weekSlots.map((d, di) => (di === dayIdx ? [] : d)))
                        }
                      >
                        Excluir dia
                      </Button>
                    )}
                  </div>
                  {daySlots.length === 0 ? (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        setWeekSlots(
                          weekSlots.map((d, di) =>
                            di === dayIdx ? [{ startTime: "08:00", endTime: "09:00", capacity: "1" }] : d
                          )
                        )
                      }
                    >
                      Adicionar horário
                    </Button>
                  ) : (
                    <>
                      {daySlots.map((slot, idx) => (
                        <div key={idx} className="grid grid-cols-4 items-center gap-4">
                          <Input
                            type="time"
                            value={slot.startTime}
                            onChange={e =>
                              setWeekSlots(
                                weekSlots.map((d, di) =>
                                  di === dayIdx
                                    ? d.map((s, i) => (i === idx ? { ...s, startTime: e.target.value } : s))
                                    : d
                                )
                              )
                            }
                            className="col-span-1"
                          />
                          <Input
                            type="time"
                            value={slot.endTime}
                            onChange={e =>
                              setWeekSlots(
                                weekSlots.map((d, di) =>
                                  di === dayIdx
                                    ? d.map((s, i) => (i === idx ? { ...s, endTime: e.target.value } : s))
                                    : d
                                )
                              )
                            }
                            className="col-span-1"
                          />
                          <Select
                            value={slot.capacity}
                            onValueChange={val =>
                              setWeekSlots(
                                weekSlots.map((d, di) =>
                                  di === dayIdx
                                    ? d.map((s, i) => (i === idx ? { ...s, capacity: val } : s))
                                    : d
                                )
                              )
                            }
                            className="col-span-2"
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {[1, 2, 3, 4, 5].map(n => (
                                <SelectItem key={n} value={String(n)}>
                                  {n} atendimentos
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      ))}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() =>
                          setWeekSlots(
                            weekSlots.map((d, di) =>
                              di === dayIdx
                                ? [...d, { startTime: "08:00", endTime: "09:00", capacity: "1" }]
                                : d
                            )
                          )
                        }
                      >
                        Adicionar horário
                      </Button>
                    </>
                  )}
                </div>
              ))
            ) : (
              <>
                {slots.map((slot, idx) => (
                  <div key={idx} className="border rounded-md p-2 space-y-2">
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label>Início</Label>
                      <Input
                        type="time"
                        value={slot.startTime}
                        onChange={e => setSlots(slots.map((s, i) => i === idx ? { ...s, startTime: e.target.value } : s))}
                        className="col-span-3"
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label>Fim</Label>
                      <Input
                        type="time"
                        value={slot.endTime}
                        onChange={e => setSlots(slots.map((s, i) => i === idx ? { ...s, endTime: e.target.value } : s))}
                        className="col-span-3"
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label>Capacidade</Label>
                      <Select
                        value={slot.capacity}
                        onValueChange={val => setSlots(slots.map((s, i) => i === idx ? { ...s, capacity: val } : s))}
                      >
                        <SelectTrigger className="col-span-3">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {[1,2,3,4,5].map(n => (
                            <SelectItem key={n} value={String(n)}>{n} atendimentos</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    {slots.length > 1 && (
                      <div className="text-right">
                        <Button variant="outline" size="sm" onClick={() => setSlots(slots.filter((_, i) => i !== idx))}>Remover</Button>
                      </div>
                    )}
                  </div>
                ))}
                <Button variant="outline" onClick={() => setSlots([...slots, { startTime: "08:00", endTime: "09:00", capacity: "1" }])}>Adicionar horário</Button>
              </>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDialogOpen(false)}>Cancelar</Button>
            <Button onClick={handleAdd}>Salvar</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
