import { create } from "zustand";
import { ClubTitle, getClubTitles, createClubTitle, updateClubTitle, deleteClubTitle } from "../api/api";

interface ClubTitlesState {
  titles: ClubTitle[];
  loading: boolean;
  error: string | null;
  fetchTitles: (clubId: number) => Promise<void>;
  addTitle: (clubId: number, title: Omit<ClubTitle, 'id' | 'club_id' | 'created_at' | 'updated_at'>) => Promise<void>;
  updateTitle: (clubId: number, id: number, updates: Partial<ClubTitle>) => Promise<void>;
  removeTitle: (clubId: number, id: number) => Promise<void>;
}

export const useClubTitlesStore = create<ClubTitlesState>((set) => ({
  titles: [],
  loading: false,
  error: null,

  fetchTitles: async (clubId: number): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const titles = await getClubTitles(clubId);
      set({ titles, loading: false });
    } catch (err: any) {
      set({ error: err.message || 'Erro ao buscar títulos', loading: false });
    }
  },

  addTitle: async (
    clubId: number,
    title: Omit<ClubTitle, 'id' | 'club_id' | 'created_at' | 'updated_at'>
  ): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const newTitle = await createClubTitle(clubId, title);
      set((state) => ({ titles: [...state.titles, newTitle], loading: false }));
    } catch (err: any) {
      set({ error: err.message || 'Erro ao adicionar título', loading: false });
    }
  },

  updateTitle: async (
    clubId: number,
    id: number,
    updates: Partial<ClubTitle>
  ): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const updated = await updateClubTitle(clubId, id, updates);
      set((state) => ({
        titles: state.titles.map(t => (t.id === id ? updated : t)),
        loading: false
      }));
    } catch (err: any) {
      set({ error: err.message || 'Erro ao atualizar título', loading: false });
    }
  },

  removeTitle: async (clubId: number, id: number): Promise<void> => {
    set({ loading: true, error: null });
    try {
      await deleteClubTitle(clubId, id);
      set((state) => ({
        titles: state.titles.filter(t => t.id !== id),
        loading: false
      }));
    } catch (err: any) {
      set({ error: err.message || 'Erro ao remover título', loading: false });
    }
  }
}));