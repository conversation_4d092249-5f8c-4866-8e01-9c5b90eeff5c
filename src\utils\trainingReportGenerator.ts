import { jsPDF } from "jspdf";
import autoTable from 'jspdf-autotable';
import { ClubInfo } from "@/api/api";
import { getClubPrimaryColorRgb } from '@/utils/themeUtils';
import type { Training, TrainingGoal, Exercise } from "@/api/api";

// Extend jsPDF type to include autoTable
interface jsPDFWithAutoTable extends jsPDF {
  autoTable: typeof autoTable;
}

export interface TrainingReportData {
  trainings: Training[];
  completedTrainings: Training[];
  goals: TrainingGoal[];
  exercises: Exercise[];
  statistics: {
    totalTrainings: number;
    completedTrainings: number;
    upcomingTrainings: number;
    typeDistribution: Record<string, number>;
    categoryDistribution: Record<string, number>;
  };
}

export interface TrainingReportOptions {
  includeStatistics: boolean;
  includeGoals: boolean;
  includeExercises: boolean;
  includeUpcoming: boolean;
  includeCompleted: boolean;
  includeDescription: boolean;
  includeMaterials: boolean;
  period?: {
    startDate?: string;
    endDate?: string;
  };
  categoryFilter?: string;
  typeFilter?: string;
}

/**
 * Gera um relatório completo de treinamentos em PDF
 * @param data Dados dos treinamentos
 * @param clubInfo Informações do clube
 * @param options Opções do relatório
 * @param filename Nome do arquivo PDF
 */
export async function generateTrainingReport(
  data: TrainingReportData,
  clubInfo: ClubInfo,
  options: TrainingReportOptions,
  filename: string = 'relatorio-treinamentos.pdf'
): Promise<void> {
  // Criar um novo documento PDF
  const doc = new jsPDF() as unknown as jsPDFWithAutoTable;

  // Configurações de página
  const pageWidth = doc.internal.pageSize.getWidth();
  const pageHeight = doc.internal.pageSize.getHeight();
  const margin = 15;
  let currentY = 20;

  // Função auxiliar para verificar se precisa de nova página
  const checkPageBreak = (neededHeight: number) => {
    if (currentY + neededHeight > pageHeight - 20) {
      doc.addPage();
      currentY = 20;
      addHeader();
    }
  };

  // Helper para converter horario (HH:MM) em minutos para comparacao
  const parseTimeToMinutes = (time?: string): number => {
    if (!time) return 0;
    const [h, m] = time.split(":" ).map(Number);
    return h * 60 + (m || 0);
  };

  
  // Função para adicionar cabeçalho
  const addHeader = () => {
    doc.setFontSize(18);
    doc.setFont("helvetica", "bold");
    doc.text('Relatório de Treinamentos', pageWidth / 2, currentY, { align: 'center' });
    currentY += 10;

    doc.setFontSize(12);
    doc.setFont("helvetica", "normal");
    doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, margin, currentY);
    currentY += 5;

    const periodText =
      options.period?.startDate && options.period?.endDate
        ? `Período: ${options.period.startDate} a ${options.period.endDate}`
        : 'Período: Todos os registros';
    doc.text(periodText, margin, currentY);
    currentY += 5;

    doc.text(`Gerado em: ${new Date().toLocaleDateString('pt-BR')}`, margin, currentY);
    currentY += 15;
  };

  // Adicionar cabeçalho inicial
  addHeader();

  // 1. Seção de Estatísticas
  if (options.includeStatistics) {
    checkPageBreak(60);

    doc.setFontSize(14);
    doc.setFont("helvetica", "bold");
    doc.text('Resumo Estatístico', margin, currentY);
    currentY += 10;

    // Estatísticas gerais
    const stats = data.statistics;
    doc.setFontSize(10);
    doc.setFont("helvetica", "normal");

    const statsData = [
      ['Total de Treinamentos', stats.totalTrainings.toString()],
      ['Treinamentos Concluídos', stats.completedTrainings.toString()],
      ['Treinamentos Agendados', stats.upcomingTrainings.toString()],
      ['Taxa de Conclusão', `${stats.totalTrainings > 0 ? Math.round((stats.completedTrainings / stats.totalTrainings) * 100) : 0}%`]
    ];

    autoTable(doc, {
      startY: currentY,
      head: [['Métrica', 'Valor']],
      body: statsData,
      theme: 'grid',
      headStyles: { fillColor: getClubPrimaryColorRgb() },
      margin: { left: margin, right: margin },
      tableWidth: 'auto'
    });

    currentY = (doc as any).lastAutoTable.finalY + 15;

    // Distribuição por tipo
    if (Object.keys(stats.typeDistribution).length > 0) {
      checkPageBreak(40);

      doc.setFontSize(12);
      doc.setFont("helvetica", "bold");
      doc.text('Distribuição por Tipo', margin, currentY);
      currentY += 8;

      const typeData = Object.entries(stats.typeDistribution).map(([type, count]) => [
        type.charAt(0).toUpperCase() + type.slice(1),
        count.toString(),
        `${stats.totalTrainings > 0 ? Math.round((count / stats.totalTrainings) * 100) : 0}%`
      ]);

      autoTable(doc, {
        startY: currentY,
        head: [['Tipo', 'Quantidade', 'Percentual']],
        body: typeData,
        theme: 'striped',
        headStyles: { fillColor: getClubPrimaryColorRgb() },
        margin: { left: margin, right: margin }
      });

      currentY = (doc as any).lastAutoTable.finalY + 15;
    }

    // Distribuição por categoria
    if (Object.keys(stats.categoryDistribution).length > 0) {
      checkPageBreak(40);

      doc.setFontSize(12);
      doc.setFont("helvetica", "bold");
      doc.text('Distribuição por Categoria', margin, currentY);
      currentY += 8;

      const categoryData = Object.entries(stats.categoryDistribution).map(([category, count]) => [
        category || 'Sem categoria',
        count.toString(),
        `${stats.totalTrainings > 0 ? Math.round((count / stats.totalTrainings) * 100) : 0}%`
      ]);

      autoTable(doc, {
        startY: currentY,
        head: [['Categoria', 'Quantidade', 'Percentual']],
        body: categoryData,
        theme: 'striped',
        headStyles: { fillColor: getClubPrimaryColorRgb() },
        margin: { left: margin, right: margin }
      });

      currentY = (doc as any).lastAutoTable.finalY + 15;
    }
  }

  // 2. Seção de Objetivos
  if (options.includeGoals && data.goals.length > 0) {
    checkPageBreak(60);

    doc.setFontSize(14);
    doc.setFont("helvetica", "bold");
    doc.text('Objetivos de Treinamento', margin, currentY);
    currentY += 10;

    const goalsData = data.goals.map(goal => [
      goal.name,
      goal.type.charAt(0).toUpperCase() + goal.type.slice(1),
      `${goal.current_value}/${goal.target_value}`,
      `${Math.round((goal.current_value / goal.target_value) * 100)}%`,
      goal.description || '-'
    ]);

    autoTable(doc, {
      startY: currentY,
      head: [['Objetivo', 'Tipo', 'Progresso', '%', 'Descrição']],
      body: goalsData,
      theme: 'striped',
      headStyles: { fillColor: getClubPrimaryColorRgb() },
      margin: { left: margin, right: margin },
      columnStyles: {
        4: { cellWidth: 40 }
      }
    });

    currentY = (doc as any).lastAutoTable.finalY + 15;
  }

  // 3. Seção de Treinamentos Agendados
  if (options.includeUpcoming && data.trainings.length > 0) {
    checkPageBreak(60);

    doc.setFontSize(14);
    doc.setFont("helvetica", "bold");
    doc.text('Treinamentos Agendados', margin, currentY);
    currentY += 10;

    let upcomingTrainings = data.trainings
      .filter(t => t.status !== 'concluído');

    // Aplicar filtros
    if (options.categoryFilter) {
      upcomingTrainings = upcomingTrainings.filter(t => t.category_name === options.categoryFilter);
    }
    if (options.period?.startDate && options.period?.endDate) {
      const start = new Date(options.period.startDate);
      const end = new Date(options.period.endDate);
      upcomingTrainings = upcomingTrainings.filter(t => {
        const d = new Date(t.date);
        return d >= start && d <= end;
      });
    }

    upcomingTrainings.sort((a, b) => {
      const dateDiff = new Date(a.date).getTime() - new Date(b.date).getTime();
      if (dateDiff !== 0) return dateDiff;
      const timeA = parseTimeToMinutes(a.start_time ?? a.time?.split('-')[0]);
      const timeB = parseTimeToMinutes(b.start_time ?? b.time?.split('-')[0]);
      return timeA - timeB;
    });
    
    if (upcomingTrainings.length > 0) {
      upcomingTrainings.forEach((training, index) => {
        // Adiciona a tabela de informações básicas
        const tableStartY = currentY + (index > 0 ? 5 : 0);
        
        autoTable(doc, {
          startY: tableStartY,
          head: [['Nome', 'Tipo', 'Categoria', 'Data', 'Horário', 'Local', 'Treinador', 'Status']],
          body: [[
            training.name,
            training.type.charAt(0).toUpperCase() + training.type.slice(1),
            training.category_name || 'Sem categoria',
            training.date,
            training.time || '-',
            training.location,
            training.coach,
            training.status.charAt(0).toUpperCase() + training.status.slice(1)
          ]],
          theme: 'striped',
          headStyles: { fillColor: getClubPrimaryColorRgb() },
          margin: { left: margin, right: margin },
          styles: { fontSize: 8, cellPadding: 2, overflow: 'linebreak' },
          tableWidth: 'auto',
          columnStyles: {
            0: { cellWidth: 30 }, // Nome
            1: { cellWidth: 20 }, // Tipo
            2: { cellWidth: 25 }, // Categoria
            3: { cellWidth: 18 }, // Data
            4: { cellWidth: 20 }, // Horário
            5: { cellWidth: 25 }, // Local
            6: { cellWidth: 25 }, // Treinador
            7: { cellWidth: 20 }  // Status
          },
          didParseCell: function(data) {
            if (data.section === 'head') {
              data.cell.styles.fillColor = getClubPrimaryColorRgb();
            }
          }
        });
        
        currentY = (doc as any).lastAutoTable.finalY + 5;
        
        // Adiciona descrição se habilitado e existir
        if (options.includeDescription && training.description) {
          checkPageBreak(20);
          doc.setFontSize(10);
          doc.setFont("helvetica", "bold");
          doc.text('Descrição:', margin, currentY);
          currentY += 5;
          
          doc.setFont("helvetica", "normal");
          const descriptionLines = doc.splitTextToSize(training.description, pageWidth - 2 * margin);
          doc.text(descriptionLines, margin, currentY);
          currentY += descriptionLines.length * 6 + 5; // Aproximadamente 6pt por linha
        }
        
        // Adiciona material necessário se habilitado e existir
        if (options.includeMaterials && training.required_materials) {
          checkPageBreak(20);
          doc.setFontSize(10);
          doc.setFont("helvetica", "bold");
          doc.text('Material Necessário:', margin, currentY);
          currentY += 5;
          
          doc.setFont("helvetica", "normal");
          const materialsLines = doc.splitTextToSize(training.required_materials, pageWidth - 2 * margin);
          doc.text(materialsLines, margin, currentY);
          currentY += materialsLines.length * 6 + 8; // Aproximadamente 6pt por linha
        } else {
          currentY += 6;
        }
        
        // Adiciona uma linha divisória entre treinamentos
        if (index < upcomingTrainings.length - 1) {
          doc.setDrawColor(200, 200, 200);
          doc.line(margin, currentY - 5, pageWidth - margin, currentY - 5);
        }
      });
      
      currentY += 6;
    } else {
      doc.setFontSize(10);
      doc.setFont("helvetica", "italic");
      doc.text('Nenhum treinamento agendado encontrado.', margin, currentY);
      currentY += 15;
    }
  }

  // 4. Seção de Treinamentos Concluídos
  if (options.includeCompleted && data.completedTrainings.length > 0) {
    checkPageBreak(60);

    doc.setFontSize(14);
    doc.setFont("helvetica", "bold");
    doc.text('Treinamentos Concluídos', margin, currentY);
    currentY += 10;

    let completed = data.completedTrainings;
    if (options.categoryFilter) {
      completed = completed.filter(t => t.category_name === options.categoryFilter);
    }
    if (options.period?.startDate && options.period?.endDate) {
      const start = new Date(options.period.startDate);
      const end = new Date(options.period.endDate);
      completed = completed.filter(t => {
        const d = new Date(t.date);
        return d >= start && d <= end;
      });
    }

    completed.sort((a, b) => {
      const dateDiff = new Date(a.date).getTime() - new Date(b.date).getTime();
      if (dateDiff !== 0) return dateDiff;
      const timeA = parseTimeToMinutes(a.start_time ?? a.time?.split('-')[0]);
      const timeB = parseTimeToMinutes(b.start_time ?? b.time?.split('-')[0]);
      return timeA - timeB;
    });
    
    completed.forEach((training, index) => {
      // Adiciona a tabela de informações básicas
      const tableStartY = currentY + (index > 0 ? 5 : 0);
      
      autoTable(doc, {
        startY: tableStartY,
        head: [['Nome', 'Tipo', 'Categoria', 'Data', 'Horário', 'Local', 'Treinador', 'Participantes']],
        body: [[
          training.name,
          training.type.charAt(0).toUpperCase() + training.type.slice(1),
          training.category_name || 'Sem categoria',
          training.date,
          training.time || '-',
          training.location,
          training.coach,
          training.participants?.toString() || '0'
        ]],
        theme: 'striped',
        headStyles: { fillColor: getClubPrimaryColorRgb() },
        margin: { left: margin, right: margin },
        styles: { fontSize: 8, cellPadding: 2, overflow: 'linebreak' },
        tableWidth: 'auto',
        columnStyles: {
          0: { cellWidth: 30 },
          1: { cellWidth: 20 },
          2: { cellWidth: 25 },
          3: { cellWidth: 18 },
          4: { cellWidth: 20 },
          5: { cellWidth: 25 },
          6: { cellWidth: 25 },
          7: { cellWidth: 20 }
        },
        didParseCell: function(data) {
          if (data.section === 'head') {
            data.cell.styles.fillColor = getClubPrimaryColorRgb();
          }
        }
      });
      
      currentY = (doc as any).lastAutoTable.finalY + 5;
      
      // Adiciona descrição se habilitado e existir
      if (options.includeDescription && training.description) {
        checkPageBreak(20);
        doc.setFontSize(10);
        doc.setFont("helvetica", "bold");
        doc.text('Descrição:', margin, currentY);
        currentY += 5;
        
        doc.setFont("helvetica", "normal");
        const descriptionLines = doc.splitTextToSize(training.description, pageWidth - 2 * margin);
        doc.text(descriptionLines, margin, currentY);
        currentY += descriptionLines.length * 6 + 5; // Aproximadamente 6pt por linha
      }
      
      // Adiciona material necessário se habilitado e existir
      if (options.includeMaterials && training.required_materials) {
        checkPageBreak(20);
        doc.setFontSize(10);
        doc.setFont("helvetica", "bold");
        doc.text('Material Necessário:', margin, currentY);
        currentY += 5;
        
        doc.setFont("helvetica", "normal");
        const materialsLines = doc.splitTextToSize(training.required_materials, pageWidth - 2 * margin);
        doc.text(materialsLines, margin, currentY);
        currentY += materialsLines.length * 6 + 4; // Aproximadamente 6pt por linha
      }
      
      // Adiciona resumo se existir
      if (training.summary) {
        checkPageBreak(20);
        doc.setFontSize(10);
        doc.setFont("helvetica", "bold");
        doc.text('Resumo do Treino:', margin, currentY);
        currentY += 5;
        
        doc.setFont("helvetica", "normal");
        const summaryLines = doc.splitTextToSize(training.summary, pageWidth - 2 * margin);
        doc.text(summaryLines, margin, currentY);
        currentY += summaryLines.length * 6 + 5; // Aproximadamente 6pt por linha
      }
      
      // Adiciona uma linha divisória entre treinamentos
      if (index < completed.length - 1) {
        doc.setDrawColor(200, 200, 200);
        doc.line(margin, currentY + 5, pageWidth - margin, currentY + 5);
        currentY += 10;
      } else {
        currentY += 6;
      }
    });
  }

  // 5. Seção de Exercícios Mais Utilizados
  // 5. Seção de Exercícios Mais Utilizados
  if (options.includeExercises && data.exercises.length > 0) {
    checkPageBreak(60);

    doc.setFontSize(14);
    doc.setFont("helvetica", "bold");
    doc.text('Banco de Exercícios', margin, currentY);
    currentY += 10;

    const exercisesData = data.exercises.slice(0, 20).map(exercise => [
      exercise.name,
      exercise.category || '-',
      exercise.difficulty || '-',
      exercise.description || '-'
    ]);

    autoTable(doc, {
      startY: currentY,
      head: [['Nome', 'Categoria', 'Dificuldade', 'Descrição']],
      body: exercisesData,
      theme: 'striped',
      headStyles: { fillColor: getClubPrimaryColorRgb() },
      margin: { left: margin, right: margin },
      columnStyles: {
        3: { cellWidth: 50 }
      },
      styles: { fontSize: 8 }
    });
    
    currentY = (doc as any).lastAutoTable.finalY + 15;
  }

  // Adicionar rodapé em todas as páginas
  const pageCount = (doc as any).internal.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(8);
    doc.setTextColor(100, 100, 100);
    doc.text(
      `© ${new Date().getFullYear()} ${clubInfo.name} - Relatório de Treinamentos`,
      pageWidth / 2,
      pageHeight - 10,
      { align: 'center' }
    );
    doc.text(
      `Página ${i} de ${pageCount}`,
      pageWidth - margin,
      pageHeight - 10,
      { align: 'right' }
    );
  }

  // Salvar o PDF
  doc.save(filename);
}

