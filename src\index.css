/* Import enhanced form styles */
@import './styles/enhanced-forms.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 40% 98%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 210 100% 35%;
    --primary-foreground: 210 40% 98%;

    --secondary: 142 80% 38%;
    --secondary-foreground: 210 40% 98%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 25 95% 53%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 210 100% 35%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
  }

  /* Animações personalizadas para a plataforma */
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideInFromLeft {
    from {
      opacity: 0;
      transform: translateX(-100px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideInFromRight {
    from {
      opacity: 0;
      transform: translateX(100px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  @keyframes glow {
    0%, 100% {
      box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
    }
    50% {
      box-shadow: 0 0 30px rgba(59, 130, 246, 0.6);
    }
  }

  @keyframes slideInFromBottom {
    from {
      opacity: 0;
      transform: translateY(100px) scale(0.8);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  @keyframes slideOutToTop {
    from {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
    to {
      opacity: 0;
      transform: translateY(-100px) scale(0.8);
    }
  }

  @keyframes flipInX {
    from {
      opacity: 0;
      transform: perspective(1000px) rotateX(-90deg) scale(0.9);
    }
    50% {
      transform: perspective(1000px) rotateX(-15deg) scale(1.05);
    }
    to {
      opacity: 1;
      transform: perspective(1000px) rotateX(0deg) scale(1);
    }
  }

  @keyframes flipOutX {
    from {
      opacity: 1;
      transform: perspective(1000px) rotateX(0deg) scale(1);
    }
    50% {
      transform: perspective(1000px) rotateX(15deg) scale(1.05);
    }
    to {
      opacity: 0;
      transform: perspective(1000px) rotateX(90deg) scale(0.9);
    }
  }

  @keyframes zoomInRotate {
    from {
      opacity: 0;
      transform: scale(0.3) rotate(-180deg);
    }
    50% {
      transform: scale(1.1) rotate(-90deg);
    }
    to {
      opacity: 1;
      transform: scale(1) rotate(0deg);
    }
  }

  @keyframes zoomOutRotate {
    from {
      opacity: 1;
      transform: scale(1) rotate(0deg);
    }
    50% {
      transform: scale(1.1) rotate(90deg);
    }
    to {
      opacity: 0;
      transform: scale(0.3) rotate(180deg);
    }
  }

  @keyframes slideInFromRight3D {
    from {
      opacity: 0;
      transform: perspective(1000px) translateX(100%) rotateY(-45deg) scale(0.8);
    }
    to {
      opacity: 1;
      transform: perspective(1000px) translateX(0) rotateY(0deg) scale(1);
    }
  }

  @keyframes slideOutToLeft3D {
    from {
      opacity: 1;
      transform: perspective(1000px) translateX(0) rotateY(0deg) scale(1);
    }
    to {
      opacity: 0;
      transform: perspective(1000px) translateX(-100%) rotateY(45deg) scale(0.8);
    }
  }

  @keyframes slideInFromLeft3D {
    from {
      opacity: 0;
      transform: perspective(1000px) translateX(-100%) rotateY(45deg) scale(0.8);
    }
    to {
      opacity: 1;
      transform: perspective(1000px) translateX(0) rotateY(0deg) scale(1);
    }
  }

  @keyframes slideOutToRight3D {
    from {
      opacity: 1;
      transform: perspective(1000px) translateX(0) rotateY(0deg) scale(1);
    }
    to {
      opacity: 0;
      transform: perspective(1000px) translateX(100%) rotateY(-45deg) scale(0.8);
    }
  }

  @keyframes morphIn {
    from {
      opacity: 0;
      transform: perspective(1000px) scale(0.5) rotateX(-30deg) rotateY(-30deg);
      filter: blur(10px);
    }
    50% {
      transform: perspective(1000px) scale(1.1) rotateX(-10deg) rotateY(-10deg);
      filter: blur(2px);
    }
    to {
      opacity: 1;
      transform: perspective(1000px) scale(1) rotateX(0deg) rotateY(0deg);
      filter: blur(0px);
    }
  }

  @keyframes morphOut {
    from {
      opacity: 1;
      transform: perspective(1000px) scale(1) rotateX(0deg) rotateY(0deg);
      filter: blur(0px);
    }
    50% {
      transform: perspective(1000px) scale(1.1) rotateX(10deg) rotateY(10deg);
      filter: blur(2px);
    }
    to {
      opacity: 0;
      transform: perspective(1000px) scale(0.5) rotateX(30deg) rotateY(30deg);
      filter: blur(10px);
    }
  }

  .animate-fade-in {
    animation: fadeIn 0.3s ease-out forwards;
  }

  .animate-slide-in-left {
    animation: slideInFromLeft 0.6s ease-out forwards;
  }

  .animate-slide-in-right {
    animation: slideInFromRight 0.6s ease-out forwards;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite;
  }

  .animate-slide-in-bottom {
    animation: slideInFromBottom 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
  }

  .animate-slide-out-top {
    animation: slideOutToTop 0.6s cubic-bezier(0.55, 0.085, 0.68, 0.53) forwards;
  }

  .animate-flip-in-x {
    animation: flipInX 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
  }

  .animate-flip-out-x {
    animation: flipOutX 0.6s cubic-bezier(0.55, 0.085, 0.68, 0.53) forwards;
  }

  .animate-zoom-in-rotate {
    animation: zoomInRotate 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
  }

  .animate-zoom-out-rotate {
    animation: zoomOutRotate 0.6s cubic-bezier(0.55, 0.085, 0.68, 0.53) forwards;
  }

  .animate-slide-in-right-3d {
    animation: slideInFromRight3D 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
  }

  .animate-slide-out-left-3d {
    animation: slideOutToLeft3D 0.6s cubic-bezier(0.55, 0.085, 0.68, 0.53) forwards;
  }

  .animate-slide-in-left-3d {
    animation: slideInFromLeft3D 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
  }

  .animate-slide-out-right-3d {
    animation: slideOutToRight3D 0.6s cubic-bezier(0.55, 0.085, 0.68, 0.53) forwards;
  }

  .animate-morph-in {
    animation: morphIn 1s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
  }

  .animate-morph-out {
    animation: morphOut 0.7s cubic-bezier(0.55, 0.085, 0.68, 0.53) forwards;
  }

  /* Perspective utilities for 3D effects */
  .perspective-1000 {
    perspective: 1000px;
  }

  .transform-gpu {
    transform: translateZ(0);
    backface-visibility: hidden;
    will-change: transform;
  }

  /* Enhanced hover effects for buttons */
  .btn-hover-glow:hover {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4), 0 0 40px rgba(59, 130, 246, 0.2);
  }

  .btn-hover-lift:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  }

  /* Animações para o dashboard */
  @keyframes slideInFromBottom {
    from {
      opacity: 0;
      transform: translateY(50px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes shimmer {
    0% {
      background-position: -200px 0;
    }
    100% {
      background-position: calc(200px + 100%) 0;
    }
  }

  .animate-slide-in-bottom {
    animation: slideInFromBottom 0.6s ease-out forwards;
  }

  .animate-scale-in {
    animation: scaleIn 0.5s ease-out forwards;
  }

  .animate-shimmer {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    background-size: 200px 100%;
    animation: shimmer 2s infinite;
  }

  /* Efeitos de hover para cards */
  .card-hover-effect {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .card-hover-effect:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }

  /* Responsividade global */
  .responsive-container {
    @apply w-full max-w-full overflow-hidden;
  }

  .responsive-grid {
    @apply grid gap-3 sm:gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
  }

  .responsive-text {
    @apply text-sm sm:text-base;
  }

  .responsive-heading {
    @apply text-xl sm:text-2xl lg:text-3xl;
  }

  .responsive-padding {
    @apply p-2 sm:p-4 lg:p-6;
  }

  .responsive-margin {
    @apply m-2 sm:m-4 lg:m-6;
  }

  /* Melhorias para tabelas responsivas */
  .table-responsive {
    @apply w-full overflow-x-auto;
  }

  .table-responsive table {
    @apply min-w-full;
  }

  /* Melhorias para formulários em mobile */
  .form-responsive input,
  .form-responsive select,
  .form-responsive textarea {
    @apply text-base; /* Evita zoom no iOS */
  }

  /* Melhorias para modais em mobile */
  .modal-responsive {
    @apply max-h-screen overflow-y-auto;
  }

  /* Scrollbar personalizada */
  .custom-scrollbar::-webkit-scrollbar {
    @apply w-2 h-2;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-gray-100 rounded;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded hover:bg-gray-400;
  }

  /* Melhorias para touch devices */
  @media (hover: none) and (pointer: coarse) {
    .hover-touch {
      @apply active:bg-gray-100;
    }
  }
}