import { supabase } from "@/integrations/supabase/client";

export type ClubTitle = {
  id: number;
  club_id: number;
  title: string;
  year?: number;
  created_at?: string;
  updated_at?: string;
};

export async function getClubTitles(clubId: number): Promise<ClubTitle[]> {
  const { data, error } = await supabase
    .from('club_titles')
    .select('*')
    .eq('club_id', clubId);

  if (error) {
    console.error('Erro ao buscar títulos do clube:', error);
    return [];
  }

  return data || [];
}

export async function createClubTitle(
  clubId: number,
  title: Omit<ClubTitle, 'id' | 'club_id' | 'created_at' | 'updated_at'>
): Promise<ClubTitle> {
  const { data, error } = await supabase
    .from('club_titles')
    .insert({
      club_id: clubId,
      title: title.title,
      year: title.year
    })
    .select('*')
    .single();

  if (error) {
    console.error('Erro ao criar título do clube:', error);
    throw new Error(`Erro ao criar título do clube: ${error.message}`);
  }

  return data as ClubTitle;
}

export async function updateClubTitle(
  clubId: number,
  id: number,
  updates: Partial<ClubTitle>
): Promise<ClubTitle> {
  const { data, error } = await supabase
    .from('club_titles')
    .update({
      ...updates,
      updated_at: new Date().toISOString()
    })
    .eq('club_id', clubId)
    .eq('id', id)
    .select('*')
    .single();

  if (error) {
    console.error('Erro ao atualizar título do clube:', error);
    throw new Error(`Erro ao atualizar título do clube: ${error.message}`);
  }

  return data as ClubTitle;
}

export async function deleteClubTitle(
  clubId: number,
  id: number
): Promise<boolean> {
  const { error } = await supabase
    .from('club_titles')
    .delete()
    .eq('club_id', clubId)
    .eq('id', id);

  if (error) {
    console.error('Erro ao excluir título do clube:', error);
    throw new Error(`Erro ao excluir título do clube: ${error.message}`);
  }

  return true;
}