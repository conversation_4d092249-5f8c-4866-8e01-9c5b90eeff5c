# Instruções para Implementação da Página de Mapeamento

## Resumo
Foi implementada uma nova página de **Mapeamento de Jogadores** no sistema, que permite criar mapeamentos visuais dos jogadores no campo de futebol por categoria, com funcionalidade de geração de PDF.

## Arquivos Criados/Modificados

### Novos Arquivos:
1. **`sql/create-category-mappings.sql`** - Script SQL para criar a tabela de mapeamentos
2. **`src/api/categoryMappings.ts`** - API para gerenciar mapeamentos
3. **`src/components/mapping/InteractiveFootballField.tsx`** - Campo de futebol interativo
4. **`src/components/mapping/PlayerSelectionModal.tsx`** - Modal para seleção de jogadores
5. **`src/pages/Mapeamento.tsx`** - Página principal de mapeamento

### Arquivos Modificados:
1. **`src/integrations/supabase/types.ts`** - Adicionado tipo para category_mappings
2. **`src/utils/pdfGenerator.ts`** - Adicionadas funções para gerar PDF do mapeamento
3. **`src/components/layout/Sidebar.tsx`** - Adicionado link para mapeamento com verificação customizada
4. **`src/App.tsx`** - Adicionada rota para mapeamento com guard específico
5. **`src/components/MappingPermissionGuard.tsx`** - Componente de proteção específico
6. **`src/components/mapping/PlayerSelectionModal.tsx`** - Filtros de posição melhorados

### Arquivos SQL Adicionais:
1. **`sql/fix-category-mappings-rls.sql`** - Correção das políticas RLS
2. **`sql/add-mapping-permissions-to-collaborators.sql`** - Gerenciamento de permissões

## Dependências Necessárias

Para que a funcionalidade de geração de PDF funcione corretamente, você precisa instalar as seguintes dependências:

```bash
npm install jspdf html2canvas
```

## Passos para Implementação

### 1. Executar Scripts SQL (IMPORTANTE - Execute na ordem)
```sql
-- 1. Primeiro, execute o script de criação da tabela:
-- sql/create-category-mappings.sql

-- 2. Depois, execute o script de correção do RLS:
-- sql/fix-category-mappings-rls.sql

-- 3. Opcionalmente, execute o script para adicionar permissões a colaboradores:
-- sql/add-mapping-permissions-to-collaborators.sql
```

### 2. Instalar Dependências
```bash
npm install jspdf html2canvas
```

### 3. Configurar Permissões
A página usa um sistema de permissões específico:
- **President/Admin**: Sempre têm acesso
- **Colaboradores**: Precisam da permissão `mapping.view`
- **Jogadores**: Nunca têm acesso

Para adicionar permissão a um colaborador específico:
```sql
SELECT add_mapping_permission_to_collaborator('<EMAIL>');
```

## Funcionalidades Implementadas

### Campo de Futebol Interativo
- **31 posições** no campo (4 goleiros + 27 posições de linha)
- **Cores por posição**: Amarelo (goleiros), Azul (defensores), Verde (meio-campistas), Vermelho (atacantes)
- **Clique para adicionar**: Círculos com '+' para adicionar jogadores
- **Informações do jogador**: Foto, nome/apelido, número, data de nascimento

### Sistema de Seleção
- **Filtro por posição**: Jogadores filtrados automaticamente por posição compatível
- **Filtro por status**: Apenas jogadores ativos (não emprestados ou inativos)
- **Busca**: Por nome, apelido, número ou posição
- **Informações completas**: Avatar, número, data de nascimento, status

### Persistência de Dados
- **Salvamento automático**: Mapeamentos salvos automaticamente por categoria
- **Múltiplos mapeamentos**: Suporte a vários mapeamentos por categoria
- **Histórico**: Mapeamentos ficam salvos no banco de dados

### Geração de PDF
- **Formato paisagem**: PDF em A4 paisagem
- **Campo visual**: Campo de futebol desenhado com jogadores posicionados
- **Legenda**: Lista de jogadores escalados com números e datas de nascimento
- **Informações do clube**: Nome do clube, categoria, data

## Estrutura do Banco de Dados

### Tabela `category_mappings`
```sql
- id (SERIAL PRIMARY KEY)
- club_id (INTEGER, FK para club_info)
- category_id (INTEGER, FK para categories)
- name (TEXT, nome do mapeamento)
- mapping (JSONB, dados do mapeamento)
- created_at (TIMESTAMP)
- updated_at (TIMESTAMP)
```

### Estrutura do Mapeamento (JSON)
```json
{
  "gk1": {
    "player_id": "uuid",
    "player_name": "Nome do Jogador",
    "player_nickname": "Apelido",
    "player_number": 1,
    "player_birthdate": "1990-01-01",
    "player_image": "url_da_foto"
  },
  "ld1": {
    // ... outros jogadores
  }
}
```

## Como Usar

1. **Acesse a página**: Menu lateral > Mapeamento
2. **Selecione categoria**: Escolha a categoria desejada
3. **Adicione jogadores**: Clique nos círculos '+' no campo
4. **Selecione jogadores**: Escolha jogadores disponíveis no modal
5. **Salve**: O mapeamento é salvo automaticamente
6. **Gere PDF**: Clique em "Gerar PDF" para exportar

## Posições Disponíveis

### Goleiros (4 posições)
- gk1, gk2, gk3, gk4

### Defensores (9 posições)
- **Laterais Direitos**: ld1, ld2, ld3
- **Zagueiros**: zag1, zag2, zag3
- **Laterais Esquerdos**: le1, le2, le3

### Meio-campistas (9 posições)
- **Meio Direita**: md1, md2, md3
- **Meio Centro**: mc1, mc2, mc3
- **Meio Esquerda**: me1, me2, me3

### Atacantes (9 posições)
- **Atacante Direita**: ad1, ad2, ad3
- **Atacante Centro**: ac1, ac2, ac3
- **Atacante Esquerda**: ae1, ae2, ae3

## Observações Importantes

1. **Dependências**: Certifique-se de instalar `jspdf` e `html2canvas`
2. **Scripts SQL**: Execute os scripts na ordem correta (create → fix-rls → permissions)
3. **Permissões**: Sistema específico (president/admin sempre, colaboradores com permissão, jogadores nunca)
4. **Status dos jogadores**: Apenas jogadores ativos são mostrados
5. **Filtro de posições**: Melhorado para incluir variações como "lateral", "lateral-direito", etc.
6. **Backup**: Execute os scripts SQL em ambiente de teste primeiro
7. **Performance**: O sistema filtra automaticamente jogadores já selecionados

## Possíveis Melhorias Futuras

1. **Formações táticas**: Adicionar formações pré-definidas (4-4-2, 4-3-3, etc.)
2. **Drag & Drop**: Arrastar jogadores entre posições
3. **Histórico de mapeamentos**: Visualizar mapeamentos anteriores
4. **Exportação**: Outros formatos além de PDF
5. **Compartilhamento**: Enviar mapeamentos por email

## Problemas Corrigidos

### ✅ Erro de RLS (Row Level Security)
**Problema:** `new row violates row-level security policy for table "category_mappings"`
**Solução:** Criadas novas políticas RLS que usam a função `get_current_club_id()` e verificam através da tabela `club_members`.

### ✅ Filtro de Posições
**Problema:** Jogadores com posição "lateral" não apareciam para seleção
**Solução:** Expandido o filtro para incluir variações como "lateral", "lateral-direito", "lateral-esquerdo", etc.

### ✅ Sistema de Permissões
**Problema:** Permissões não específicas para mapeamento
**Solução:** Criado sistema específico com `MappingPermissionGuard` e verificação customizada na sidebar.

## Suporte

Se encontrar problemas:
1. Verifique se as dependências estão instaladas: `npm install jspdf html2canvas`
2. Confirme se os scripts SQL foram executados na ordem correta
3. Verifique as permissões do usuário com: `SELECT * FROM list_collaborators_mapping_permissions();`
4. Consulte o console do navegador para erros
5. Teste as políticas RLS com as queries de verificação nos scripts SQL
