import React from "react";
import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, X } from "lucide-react";
import { MappingPosition } from "@/api/categoryMappings";

interface InteractiveFootballFieldProps {
  mapping: { [positionKey: string]: MappingPosition | null };
  onPositionClick: (positionKey: string) => void;
  onRemovePlayer: (positionKey: string) => void;
}

// Definição das posições no campo
const FIELD_POSITIONS = {
  // Goleiros (4 posições)
  gk1: { x: 42.5, y: 90, label: "GOL", type: "goalkeeper" },
  gk2: { x: 30, y: 90, label: "GOL", type: "goalkeeper" },
  gk3: { x: 70, y: 90, label: "GOL", type: "goalkeeper" },
  gk4: { x: 55, y: 90, label: "GO<PERSON>", type: "goalkeeper" },

  // Defensores (3 posições por lado)
  ld1: { x: 80, y: 75, label: "LD", type: "defender" },
  ld2: { x: 85, y: 75, label: "LD", type: "defender" },
  ld3: { x: 90, y: 75, label: "LD", type: "defender" },

  zag1: { x: 65, y: 75, label: "ZAG", type: "defender" },
  zag2: { x: 58, y: 75, label: "ZAG", type: "defender" },
  zag3: { x: 50, y: 75, label: "ZAG", type: "defender" },
  zag4: { x: 42, y: 75, label: "ZAG", type: "defender" },
  zag5: { x: 35, y: 75, label: "ZAG", type: "defender" },

  le1: { x: 20, y: 75, label: "LE", type: "defender" },
  le2: { x: 15, y: 75, label: "LE", type: "defender" },
  le3: { x: 10, y: 75, label: "LE", type: "defender" },

  // Meio-campistas (3 posições por lado)
  md1: { x: 80, y: 50, label: "MD", type: "midfielder" },
  md2: { x: 85, y: 50, label: "MD", type: "midfielder" },
  md3: { x: 90, y: 50, label: "MD", type: "midfielder" },

  mc1: { x: 65, y: 50, label: "MC", type: "midfielder" },
  mc2: { x: 55, y: 50, label: "MC", type: "midfielder" },
  mc3: { x: 45, y: 50, label: "MC", type: "midfielder" },
  mc4: { x: 35, y: 50, label: "MC", type: "midfielder" },

  me1: { x: 20, y: 50, label: "ME", type: "midfielder" },
  me2: { x: 15, y: 50, label: "ME", type: "midfielder" },
  me3: { x: 10, y: 50, label: "ME", type: "midfielder" },

  // Atacantes (3 posições por lado)
  ad1: { x: 80, y: 25, label: "EX", type: "attacker" },
  ad2: { x: 85, y: 25, label: "EX", type: "attacker" },
  ad3: { x: 90, y: 25, label: "EX", type: "attacker" },

  ac1: { x: 60, y: 25, label: "AT", type: "attacker" },
  ac2: { x: 50, y: 25, label: "AT", type: "attacker" },
  ac3: { x: 40, y: 25, label: "AT", type: "attacker" },

  ae1: { x: 20, y: 25, label: "EX", type: "attacker" },
  ae2: { x: 15, y: 25, label: "EX", type: "attacker" },
  ae3: { x: 10, y: 25, label: "EX", type: "attacker" },
};

// Cores por tipo de posição
const POSITION_COLORS = {
  goalkeeper: "bg-yellow-500 border-yellow-600",
  defender: "bg-blue-500 border-blue-600",
  midfielder: "bg-green-500 border-green-600",
  attacker: "bg-red-500 border-red-600",
};

const InteractiveFootballField: React.FC<InteractiveFootballFieldProps> = ({
  mapping,
  onPositionClick,
  onRemovePlayer,
}) => {
  const renderPosition = (positionKey: string, position: any) => {
    const player = mapping[positionKey];
    const colorClass = POSITION_COLORS[position.type as keyof typeof POSITION_COLORS];

    return (
      <div
        key={positionKey}
        className="absolute transform -translate-x-1/2 -translate-y-1/2"
        style={{ left: `${position.x}%`, top: `${position.y}%` }}
      >
        {player ? (
          // Jogador selecionado
          <div className="relative group">
            <div className="flex flex-col items-center">
              <Avatar className="h-12 w-12 border-2 border-white shadow-lg">
                <AvatarImage src={player.player_image || ""} />
                <AvatarFallback className={`${colorClass} text-white text-xs font-bold`}>
                  {player.player_number}
                </AvatarFallback>
              </Avatar>
              <div className="mt-1 text-center">
                <div className="text-xs font-medium text-white bg-black bg-opacity-50 px-1 rounded">
                  {player.player_nickname || player.player_name.split(" ")[0]}
                </div>
                <div className="text-[10px] text-white bg-black bg-opacity-50 px-1 rounded">
                  {position.label}
                </div>
              </div>
            </div>
            
            {/* Botão para remover jogador */}
            <Button
              size="sm"
              variant="destructive"
              className="absolute -top-2 -right-2 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={(e) => {
                e.stopPropagation();
                onRemovePlayer(positionKey);
              }}
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        ) : (
          // Posição vazia
          <Button
            variant="outline"
            size="sm"
            className={`h-12 w-12 rounded-full border-2 border-dashed ${colorClass} hover:scale-110 transition-transform`}
            onClick={() => onPositionClick(positionKey)}
          >
            <Plus className="h-6 w-6 text-white" />
          </Button>
        )}
      </div>
    );
  };

  return (
    <div className="relative w-full h-[600px] bg-green-600 rounded-lg overflow-hidden shadow-lg">
      {/* Campo de futebol */}
      <div className="absolute inset-0 bg-gradient-to-b from-green-500 to-green-700">
        {/* Linhas do campo */}
        <svg className="absolute inset-0 w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
          {/* Linha lateral */}
          <rect x="5" y="5" width="90" height="90" fill="none" stroke="white" strokeWidth="0.3" />
          
          {/* Linha central */}
          <line x1="5" y1="50" x2="95" y2="50" stroke="white" strokeWidth="0.3" />
          
          {/* Círculo central */}
          <circle cx="50" cy="50" r="8" fill="none" stroke="white" strokeWidth="0.3" />
          
          {/* Área do goleiro (superior) */}
          <rect x="35" y="5" width="30" height="15" fill="none" stroke="white" strokeWidth="0.3" />
          <rect x="42" y="5" width="16" height="8" fill="none" stroke="white" strokeWidth="0.3" />
          
          {/* Área do goleiro (inferior) */}
          <rect x="35" y="80" width="30" height="15" fill="none" stroke="white" strokeWidth="0.3" />
          <rect x="42" y="87" width="16" height="8" fill="none" stroke="white" strokeWidth="0.3" />
        </svg>

        {/* Posições dos jogadores */}
        {Object.entries(FIELD_POSITIONS).map(([positionKey, position]) =>
          renderPosition(positionKey, position)
        )}
      </div>

      {/* Legenda */}
      <div className="absolute top-4 left-4 bg-black bg-opacity-50 text-white p-2 rounded text-xs">
        <div className="font-bold mb-1">Posições:</div>
        <div className="flex items-center gap-1 mb-1">
          <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
          <span>Goleiros (4)</span>
        </div>
        <div className="flex items-center gap-1 mb-1">
          <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
          <span>Defensores (9)</span>
        </div>
        <div className="flex items-center gap-1 mb-1">
          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
          <span>Meio-campistas (9)</span>
        </div>
        <div className="flex items-center gap-1">
          <div className="w-3 h-3 bg-red-500 rounded-full"></div>
          <span>Atacantes (9)</span>
        </div>
      </div>
    </div>
  );
};

export default InteractiveFootballField;
