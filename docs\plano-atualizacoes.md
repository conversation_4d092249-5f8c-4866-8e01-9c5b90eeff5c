<PERSON><PERSON><PERSON>, funcionou tudo corretamente, você é fera!. Agora, precisamos atualizar o sistema para adicionar os seguintes campos ao cadastro / edição de jogadores (campos com '_' são obrigatórios):
Data de entrada_, data de saída, inscrição campeonato, categoria*, apelido*, P/A (profissional/amador)_, numero RG, numero CPF_, peso*, altura*, pai, mae, indicado por, telefone\*, endereço, CEP, cidade, estado, email.

# Plano de Atualizações para o Game Day Nexus Platform

Este documento detalha o plano de implementação para as atualizações solicitadas no sistema Game Day Nexus Platform. O plano abrange modificações no banco de dados, backend e frontend para atender a todos os requisitos.

## Sumário das Atualizações

1. [Sistema de Alojamento (Hotéis/Apartamentos)](#1-sistema-de-alojamento)
2. [Separação de Jogadores por Campeonato/Categoria](#2-separação-de-jogadores-por-campeonatocategoria)
3. [Modificação Facilitada de Peso/Altura dos Jogadores](#3-modificação-facilitada-de-pesoaltura-dos-jogadores)
4. [Sistema de Hierarquia de Usuários](#4-sistema-de-hierarquia-de-usuários)
5. [Contas para Jogadores](#5-contas-para-jogadores)
6. [Cards com Fotos e Informações dos Jogadores](#6-cards-com-fotos-e-informações-dos-jogadores)
7. [Verificação de Cartões e Lesões na Escalação](#7-verificação-de-cartões-e-lesões-na-escalação)
8. [Atualização de Jogos em Tempo Real](#8-atualização-de-jogos-em-tempo-real)

## 1. Sistema de Alojamento

### Banco de Dados

1. Criar nova tabela `accommodations`:

```sql
CREATE TABLE accommodations (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id),
  name TEXT NOT NULL,
  type TEXT NOT NULL, -- 'hotel' ou 'apartment'
  address TEXT,
  capacity INTEGER,
  cost NUMERIC,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

2. Criar tabela de associação entre jogadores e alojamentos:

```sql
CREATE TABLE player_accommodations (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id),
  player_id UUID REFERENCES players(id),
  accommodation_id INTEGER REFERENCES accommodations(id),
  room_number TEXT,
  check_in_date DATE,
  check_out_date DATE,
  status TEXT, -- 'active', 'scheduled', 'completed'
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Backend (API)

1. Criar arquivo `src/api/accommodations.ts` com funções CRUD:

   - `getAccommodations(clubId)`
   - `getAccommodationById(clubId, id)`
   - `createAccommodation(clubId, accommodation)`
   - `updateAccommodation(clubId, id, accommodation)`
   - `deleteAccommodation(clubId, id)`
   - `assignPlayerToAccommodation(clubId, playerId, accommodationId, details)`
   - `removePlayerFromAccommodation(clubId, playerId, accommodationId)`
   - `getPlayerAccommodations(clubId, playerId)`

2. Exportar funções e tipos em `src/api/api.ts`

### Frontend

1. Criar nova página `src/pages/Alojamentos.tsx`
2. Adicionar rota em `src/App.tsx`
3. Adicionar item no menu lateral em `src/components/layout/Sidebar.tsx`
4. Criar componentes:
   - `src/components/modals/AccommodationDialog.tsx` (criar/editar alojamentos)
   - `src/components/modals/PlayerAccommodationDialog.tsx` (atribuir jogadores)
5. Criar store Zustand `src/store/useAccommodationsStore.ts`

## 2. Separação de Jogadores por Campeonato/Categoria

### Banco de Dados

1. Criar tabela `categories`:

```sql
CREATE TABLE categories (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id),
  name TEXT NOT NULL,
  type TEXT NOT NULL, -- 'championship', 'age_group', 'custom'
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

2. Criar tabela de associação entre jogadores e categorias:

```sql
CREATE TABLE player_categories (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id),
  player_id UUID REFERENCES players(id),
  category_id INTEGER REFERENCES categories(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

3. Adicionar campo `category_id` à tabela `matches` (opcional, para associar partidas a categorias)

### Backend (API)

1. Criar arquivo `src/api/categories.ts` com funções CRUD:

   - `getCategories(clubId)`
   - `getCategoryById(clubId, id)`
   - `createCategory(clubId, category)`
   - `updateCategory(clubId, id, category)`
   - `deleteCategory(clubId, id)`
   - `assignPlayerToCategory(clubId, playerId, categoryId)`
   - `removePlayerFromCategory(clubId, playerId, categoryId)`
   - `getPlayerCategories(clubId, playerId)`
   - `getCategoryPlayers(clubId, categoryId)`

2. Exportar funções e tipos em `src/api/api.ts`

### Frontend

1. Atualizar página `src/pages/Elenco.tsx` para incluir filtro por categoria
2. Criar componentes:
   - `src/components/modals/CategoryDialog.tsx` (criar/editar categorias)
   - `src/components/modals/PlayerCategoryDialog.tsx` (atribuir jogadores)
3. Criar store Zustand `src/store/useCategoriesStore.ts`
4. Adicionar seção de categorias em `src/pages/ConfiguracoesClube.tsx`

## 3. Modificação Facilitada de Peso/Altura dos Jogadores

### Frontend

1. Atualizar `src/components/modals/PlayerDialog.tsx` para melhorar a interface de edição de peso/altura
2. Adicionar botão de edição rápida na lista de jogadores em `src/pages/Elenco.tsx`
3. Criar componente `src/components/modals/QuickEditPlayerDialog.tsx` para edição rápida
4. Adicionar opção de edição em massa para múltiplos jogadores

## 4. Sistema de Hierarquia de Usuários

### Banco de Dados

1. Modificar tabela `club_members` para incluir permissões mais detalhadas:

```sql
ALTER TABLE club_members
ADD COLUMN permissions JSONB DEFAULT '{}';
```

2. Criar tabela de convites para novos usuários:

```sql
CREATE TABLE user_invitations (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id),
  email TEXT NOT NULL,
  role TEXT NOT NULL, -- 'admin', 'coach', 'doctor', 'player'
  permissions JSONB DEFAULT '{}',
  token TEXT NOT NULL,
  status TEXT DEFAULT 'pending', -- 'pending', 'accepted', 'expired'
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE
);
```

### Backend (API)

1. Criar arquivo `src/api/permissions.ts` com funções:

   - `getUserPermissions(clubId, userId)`
   - `updateUserPermissions(clubId, userId, permissions)`
   - `createUserInvitation(clubId, email, role, permissions)`
   - `acceptUserInvitation(token, userData)`
   - `cancelUserInvitation(clubId, invitationId)`
   - `getClubInvitations(clubId)`

2. Modificar `src/api/auth.ts` para incluir verificação de permissões
3. Criar middleware de verificação de permissões

### Frontend

1. Criar página `src/pages/GerenciarUsuarios.tsx`
2. Adicionar rota em `src/App.tsx`
3. Adicionar item no menu lateral para administradores
4. Criar componentes:
   - `src/components/modals/InviteUserDialog.tsx`
   - `src/components/modals/UserPermissionsDialog.tsx`
5. Criar página `src/pages/AcceptInvitation.tsx` para aceitar convites
6. Implementar verificação de permissões em componentes existentes
7. Criar store Zustand `src/store/usePermissionsStore.ts`

## 5. Contas para Jogadores

### Banco de Dados

1. Criar tabela para associar jogadores a contas de usuário:

```sql
CREATE TABLE player_accounts (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id),
  player_id UUID REFERENCES players(id),
  user_id UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

2. Criar tabela para atualizações de jogadores:

```sql
CREATE TABLE player_updates (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id),
  player_id UUID REFERENCES players(id),
  user_id UUID REFERENCES auth.users(id),
  type TEXT NOT NULL, -- 'weight', 'height', 'pain', 'observation'
  value TEXT NOT NULL,
  status TEXT DEFAULT 'pending', -- 'pending', 'approved', 'rejected'
  reviewed_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE
);
```

### Backend (API)

1. Criar arquivo `src/api/playerAccounts.ts` com funções:

   - `createPlayerAccount(clubId, playerId, userId)`
   - `getPlayerByUserId(clubId, userId)`
   - `getUserByPlayerId(clubId, playerId)`
   - `submitPlayerUpdate(clubId, playerId, type, value)`
   - `getPlayerUpdates(clubId, playerId)`
   - `reviewPlayerUpdate(clubId, updateId, status, reviewerId)`

2. Exportar funções e tipos em `src/api/api.ts`

### Frontend

1. Criar página `src/pages/PerfilJogadorPessoal.tsx` para jogadores logados
2. Adicionar rota em `src/App.tsx`
3. Modificar `src/components/layout/Sidebar.tsx` para mostrar menu diferente para jogadores
4. Criar componentes:
   - `src/components/player/UpdateWeightHeightForm.tsx`
   - `src/components/player/ReportPainForm.tsx`
   - `src/components/player/ObservationsForm.tsx`
5. Adicionar seção em `src/pages/Medico.tsx` para revisar atualizações pendentes
6. Criar store Zustand `src/store/usePlayerUpdatesStore.ts`

## 6. Cards com Fotos e Informações dos Jogadores

### Frontend

1. Criar componente `src/components/player/PlayerCard.tsx`
2. Adicionar visualização em cards em `src/pages/Elenco.tsx`
3. Melhorar componente de perfil em `src/pages/PerfilJogador.tsx`
4. Adicionar opção para imprimir cards de jogadores
5. Implementar funcionalidade de exportar cards como PDF ou imagem

## 7. Verificação de Cartões e Lesões na Escalação

### Banco de Dados

1. Criar tabela para cartões:

```sql
CREATE TABLE cards (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id),
  match_id UUID REFERENCES matches(id),
  player_id UUID REFERENCES players(id),
  type TEXT NOT NULL, -- 'yellow', 'red'
  minute INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Backend (API)

1. Criar arquivo `src/api/cards.ts` com funções:

   - `getPlayerCards(clubId, playerId, seasonId)`
   - `getMatchCards(clubId, matchId)`
   - `addCard(clubId, matchId, playerId, type, minute)`
   - `removeCard(clubId, cardId)`
   - `checkPlayerAvailability(clubId, playerId)` - verifica cartões e lesões

2. Exportar funções e tipos em `src/api/api.ts`

### Frontend

1. Atualizar `src/pages/EscalacaoJogadores.tsx` para verificar disponibilidade
2. Adicionar indicadores visuais para jogadores indisponíveis
3. Criar componente `src/components/player/AvailabilityBadge.tsx`
4. Adicionar alertas ao tentar escalar jogadores indisponíveis
5. Criar store Zustand `src/store/usePlayerAvailabilityStore.ts`

## 8. Atualização de Jogos em Tempo Real

### Banco de Dados

1. Criar tabela para eventos de jogo:

```sql
CREATE TABLE match_events (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id),
  match_id UUID REFERENCES matches(id),
  type TEXT NOT NULL, -- 'goal', 'card', 'substitution', 'injury', 'other'
  minute INTEGER,
  player_id UUID REFERENCES players(id),
  player2_id UUID REFERENCES players(id), -- para substituições
  details JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Backend (API)

1. Criar arquivo `src/api/matchEvents.ts` com funções:

   - `getMatchEvents(clubId, matchId)`
   - `addMatchEvent(clubId, matchId, event)`
   - `updateMatchEvent(clubId, eventId, event)`
   - `deleteMatchEvent(clubId, eventId)`
   - `updateMatchLive(clubId, matchId, status, score)`

2. Exportar funções e tipos em `src/api/api.ts`

### Frontend

1. Criar página `src/pages/PartidaAoVivo.tsx`
2. Adicionar rota em `src/App.tsx`
3. Adicionar botão "Iniciar Partida" em `src/pages/Partidas.tsx`
4. Criar componentes:
   - `src/components/match/LiveMatchControls.tsx`
   - `src/components/match/EventTimeline.tsx`
   - `src/components/modals/AddEventDialog.tsx`
5. Criar store Zustand `src/store/useLiveMatchStore.ts`

## Cronograma de Implementação

1. **Fase 1 (Semana 1-2)**

   - Sistema de Hierarquia de Usuários
   - Contas para Jogadores
   - Modificação Facilitada de Peso/Altura

2. **Fase 2 (Semana 3-4)**

   - Sistema de Alojamento
   - Separação de Jogadores por Campeonato/Categoria
   - Cards com Fotos e Informações

3. **Fase 3 (Semana 5-6)**
   - Verificação de Cartões e Lesões na Escalação
   - Atualização de Jogos em Tempo Real

## Considerações Técnicas

- Utilizar Supabase para autenticação e armazenamento
- Implementar verificações de permissões em todas as operações
- Garantir responsividade em todas as novas interfaces
- Manter compatibilidade com o sistema existente
- Implementar testes para novas funcionalidades
- Documentar APIs e componentes novos
