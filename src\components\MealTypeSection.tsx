import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Trash2, MapPin, Clock } from "lucide-react";
import { MealParticipantWithDetails } from "@/api/api";

interface AccommodationUser {
  id: string;
  name: string;
  nickname?: string;
  role?: string;
  type: 'player' | 'collaborator';
  image?: string;
}

interface SessionMealType {
  id: number;
  name?: string;
  location?: string;
  address?: string;
  start_time?: string | null;
  end_time?: string | null;
}

interface MealTypeSectionProps {
  mealType: SessionMealType;
  participants: MealParticipantWithDetails[];
  availableUsers: AccommodationUser[];
  onDragOver: (e: React.DragEvent) => void;
  onDrop: (e: React.DragEvent, mealTypeId: number) => void;
  onRemoveParticipant: (participant: MealParticipantWithDetails) => void;
  sessionDate?: string;
}

export function MealTypeSection({
  mealType,
  participants,
  availableUsers,
  onDragOver,
  onDrop,
  onRemoveParticipant,
  sessionDate,
}: MealTypeSectionProps) {
  return (
    <Card className="mb-6">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              {mealType.name}
              <Badge variant="outline">{participants.length} participantes</Badge>
            </CardTitle>
            <div className="flex flex-wrap items-center gap-x-4 gap-y-1 mt-2 text-sm text-gray-600">
              {mealType.location && (
                <div className="flex items-center gap-1">
                  <MapPin className="h-4 w-4" />
                  <span>{mealType.location}</span>
                </div>
              )}
              {(mealType.start_time || mealType.end_time) && (
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  <span>
                    {mealType.start_time || '--'} - {mealType.end_time || '--'}
                  </span>
                </div>
              )}
            </div>
            {mealType.address && (
              <p className="text-sm text-gray-500 mt-1">{mealType.address}</p>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent
        onDragOver={onDragOver}
        onDrop={(e) => onDrop(e, mealType.id)}
        className="min-h-[200px] border-2 border-dashed border-gray-200 rounded-lg p-4"
      >
        {participants.length === 0 ? (
          <div className="flex items-center justify-center h-full text-gray-500">
            <div className="text-center">
              <p>Nenhum participante neste tipo de refeição</p>
              <p className="text-sm">Arraste usuários para cá</p>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {participants.map((participant) => (
              <div
                key={participant.id}
                className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
              >
                <div className="flex items-center">
                  <Avatar className="h-8 w-8 mr-3">
                    <AvatarFallback>
                      {participant.participant_name?.charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">{participant.participant_name}</p>
                    {participant.participant_nickname && (
                      <p className="text-sm text-gray-500">"{participant.participant_nickname}"</p>
                    )}
                    {participant.participant_role && (
                      <p className="text-sm text-gray-500">{participant.participant_role}</p>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={participant.participant_type === 'player' ? 'default' : 'secondary'}>
                    {participant.participant_type === 'player' ? 'Jogador' : 'Colaborador'}
                  </Badge>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onRemoveParticipant(participant)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
