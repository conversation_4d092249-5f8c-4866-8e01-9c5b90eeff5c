import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Users,
  Search,
  Filter,
  UserPlus,
  Star,
  Shield,
  Zap,
  Target
} from 'lucide-react';
import { usePlayersStore } from '@/store/usePlayersStore';
import { useCategoriesStore } from '@/store/useCategoriesStore';
import type { Player } from '@/api/api';

interface PlayerSelectorProps {
  clubId: number;
  onPlayerSelect: (player: Player) => void;
}

export function PlayerSelector({ clubId, onPlayerSelect }: PlayerSelectorProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedPosition, setSelectedPosition] = useState<string>('all');
  const [selectedPlayers, setSelectedPlayers] = useState<string[]>([]);

  const { players, loading, error, fetchPlayers } = usePlayersStore();
  const { categories, fetchCategories } = useCategoriesStore();

  useEffect(() => {
    fetchPlayers(clubId);
    fetchCategories(clubId);
  }, [clubId, fetchPlayers, fetchCategories]);

  // Filtrar jogadores
  const filteredPlayers = players.filter(player => {
    const matchesSearch = player.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         player.number.toString().includes(searchTerm);
    
    const matchesCategory = selectedCategory === 'all' || 
                           player.category_id?.toString() === selectedCategory;
    
    const matchesPosition = selectedPosition === 'all' || 
                           player.position === selectedPosition;
    
    return matchesSearch && matchesCategory && matchesPosition && player.status === 'ativo';
  });

  // Agrupar jogadores por posição
  const playersByPosition = filteredPlayers.reduce((acc, player) => {
    const position = player.position || 'outros';
    if (!acc[position]) acc[position] = [];
    acc[position].push(player);
    return acc;
  }, {} as Record<string, Player[]>);

  const positions = [
    { value: 'all', label: 'Todas as Posições' },
    { value: 'goleiro', label: 'Goleiro' },
    { value: 'zagueiro', label: 'Zagueiro' },
    { value: 'lateral-direito', label: 'Lateral Direito' },
    { value: 'lateral-esquerdo', label: 'Lateral Esquerdo' },
    { value: 'volante', label: 'Volante' },
    { value: 'meia', label: 'Meia' },
    { value: 'atacante', label: 'Atacante' },
    { value: 'extremo-direito', label: 'Ponta Direita' },
    { value: 'extremo-esquerdo', label: 'Ponta Esquerda' },
  ];

  const getPositionIcon = (position: string) => {
    switch (position) {
      case 'goleiro':
        return <Shield className="h-3 w-3" />;
      case 'zagueiro':
      case 'lateral-direito':
      case 'lateral-esquerdo':
        return <Shield className="h-3 w-3" />;
      case 'volante':
      case 'meia':
        return <Zap className="h-3 w-3" />;
      case 'atacante':
      case 'extremo-direito':
      case 'extremo-esquerdo':
        return <Target className="h-3 w-3" />;
      default:
        return <Users className="h-3 w-3" />;
    }
  };

  const getPositionColor = (position: string) => {
    switch (position) {
      case 'goleiro':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'zagueiro':
      case 'lateral-direito':
      case 'lateral-esquerdo':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'volante':
      case 'meia':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'atacante':
      case 'extremo-direito':
      case 'extremo-esquerdo':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const handlePlayerClick = (player: Player) => {
    onPlayerSelect(player);
  };

  const handleSelectMultiple = (playerId: string) => {
    setSelectedPlayers(prev => 
      prev.includes(playerId) 
        ? prev.filter(id => id !== playerId)
        : [...prev, playerId]
    );
  };

  const handleAddSelectedPlayers = () => {
    selectedPlayers.forEach(playerId => {
      const player = players.find(p => p.id === playerId);
      if (player) {
        onPlayerSelect(player);
      }
    });
    setSelectedPlayers([]);
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <Users className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
          <p className="text-sm text-muted-foreground">Carregando jogadores...</p>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-sm text-red-500">{error}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm flex items-center gap-2">
          <Users className="h-4 w-4" />
          Seletor de Jogadores
        </CardTitle>
        <CardDescription className="text-xs">
          Adicione jogadores ao campo de treinamento
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Filtros */}
        <div className="space-y-2">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-3 w-3 text-muted-foreground" />
            <Input
              placeholder="Buscar jogador..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-7 text-xs"
            />
          </div>
          
          <div className="grid grid-cols-1 gap-2">
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="text-xs">
                <SelectValue placeholder="Categoria" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todas as Categorias</SelectItem>
                {categories.map(category => (
                  <SelectItem key={category.id} value={category.id.toString()}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Select value={selectedPosition} onValueChange={setSelectedPosition}>
              <SelectTrigger className="text-xs">
                <SelectValue placeholder="Posição" />
              </SelectTrigger>
              <SelectContent>
                {positions.map(position => (
                  <SelectItem key={position.value} value={position.value}>
                    {position.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Ações em lote */}
        {selectedPlayers.length > 0 && (
          <div className="flex items-center justify-between p-2 bg-primary/10 rounded-lg">
            <span className="text-xs font-medium">
              {selectedPlayers.length} selecionados
            </span>
            <Button size="sm" onClick={handleAddSelectedPlayers}>
              <UserPlus className="h-3 w-3 mr-1" />
              Adicionar Todos
            </Button>
          </div>
        )}

        {/* Lista de jogadores */}
        <ScrollArea className="h-64">
          <div className="space-y-3">
            {Object.entries(playersByPosition).map(([position, positionPlayers]) => (
              <div key={position}>
                <div className="flex items-center gap-2 mb-2">
                  {getPositionIcon(position)}
                  <span className="text-xs font-medium capitalize">
                    {position} ({positionPlayers.length})
                  </span>
                </div>
                
                <div className="space-y-1 ml-4">
                  {positionPlayers.map(player => (
                    <div
                      key={player.id}
                      className={`
                        flex items-center gap-2 p-2 rounded-lg border cursor-pointer
                        transition-all hover:shadow-sm
                        ${selectedPlayers.includes(player.id) 
                          ? 'border-primary bg-primary/5' 
                          : 'border-muted hover:border-primary/50'
                        }
                      `}
                      onClick={() => handlePlayerClick(player)}
                      onContextMenu={(e) => {
                        e.preventDefault();
                        handleSelectMultiple(player.id);
                      }}
                    >
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={player.image || ''} />
                        <AvatarFallback className="text-xs">
                          {player.number}
                        </AvatarFallback>
                      </Avatar>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <span className="text-xs font-medium truncate">
                            {player.name}
                          </span>
                          <Badge 
                            variant="outline" 
                            className={`text-xs ${getPositionColor(player.position)}`}
                          >
                            #{player.number}
                          </Badge>
                        </div>
                        
                        <div className="flex items-center gap-1 mt-1">
                          <Badge 
                            variant="outline" 
                            className={`text-xs ${getPositionColor(player.position)}`}
                          >
                            {getPositionIcon(player.position)}
                            <span className="ml-1 capitalize">{player.position}</span>
                          </Badge>
                          
                          {player.age && (
                            <span className="text-xs text-muted-foreground">
                              {player.age} anos
                            </span>
                          )}
                        </div>
                      </div>
                      
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleSelectMultiple(player.id);
                        }}
                      >
                        <Star 
                          className={`h-3 w-3 ${
                            selectedPlayers.includes(player.id) 
                              ? 'fill-primary text-primary' 
                              : 'text-muted-foreground'
                          }`} 
                        />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>

        {filteredPlayers.length === 0 && (
          <div className="text-center py-6">
            <Users className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">
              Nenhum jogador encontrado
            </p>
            <p className="text-xs text-muted-foreground mt-1">
              Tente ajustar os filtros de busca
            </p>
          </div>
        )}

        {/* Estatísticas */}
        <div className="p-3 bg-muted/50 rounded-lg">
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div>
              <span className="font-medium">Total:</span>
              <span className="ml-1">{players.length} jogadores</span>
            </div>
            <div>
              <span className="font-medium">Filtrados:</span>
              <span className="ml-1">{filteredPlayers.length} jogadores</span>
            </div>
            <div>
              <span className="font-medium">Ativos:</span>
              <span className="ml-1">{players.filter(p => p.status === 'ativo').length}</span>
            </div>
            <div>
              <span className="font-medium">Categorias:</span>
              <span className="ml-1">{categories.length}</span>
            </div>
          </div>
        </div>

        {/* Dicas */}
        <div className="p-3 bg-muted/50 rounded-lg">
          <p className="text-xs text-muted-foreground">
            💡 <strong>Dicas:</strong> Clique para adicionar um jogador. 
            Clique com botão direito para seleção múltipla.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
