-- Script para adicionar permissões de mapeamento a colaboradores específicos
-- Execute este arquivo após o fix-category-mappings-rls.sql

-- 1. Função para adicionar permissão de mapeamento a um colaborador específico
-- Substitua '<EMAIL>' pelo email real do colaborador
-- Exemplo de uso:
-- SELECT add_mapping_permission_to_collaborator('<EMAIL>');

CREATE OR REPLACE FUNCTION add_mapping_permission_to_collaborator(collaborator_email TEXT)
RETURNS TEXT AS $$
DECLARE
  user_record RECORD;
  updated_count INTEGER := 0;
BEGIN
  -- Buscar o usuário pelo email
  SELECT au.id, au.email INTO user_record
  FROM auth.users au
  WHERE au.email = collaborator_email;
  
  IF NOT FOUND THEN
    RETURN 'Usuário não encontrado com o email: ' || collaborator_email;
  END IF;
  
  -- Atualizar as permissões do colaborador em todos os clubes onde ele é membro
  UPDATE club_members 
  SET permissions = COALESCE(permissions, '{}'::jsonb) || '{"mapping.view": true, "mapping.create": true, "mapping.edit": true, "mapping.delete": true}'::jsonb
  WHERE user_id = user_record.id 
  AND role = 'collaborator'
  AND status = 'ativo';
  
  GET DIAGNOSTICS updated_count = ROW_COUNT;
  
  IF updated_count > 0 THEN
    RETURN 'Permissões de mapeamento adicionadas com sucesso para ' || collaborator_email || ' em ' || updated_count || ' clube(s).';
  ELSE
    RETURN 'Nenhum registro de colaborador ativo encontrado para ' || collaborator_email;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- 2. Função para remover permissão de mapeamento de um colaborador
CREATE OR REPLACE FUNCTION remove_mapping_permission_from_collaborator(collaborator_email TEXT)
RETURNS TEXT AS $$
DECLARE
  user_record RECORD;
  updated_count INTEGER := 0;
BEGIN
  -- Buscar o usuário pelo email
  SELECT au.id, au.email INTO user_record
  FROM auth.users au
  WHERE au.email = collaborator_email;
  
  IF NOT FOUND THEN
    RETURN 'Usuário não encontrado com o email: ' || collaborator_email;
  END IF;
  
  -- Remover as permissões de mapeamento
  UPDATE club_members 
  SET permissions = permissions - 'mapping.view' - 'mapping.create' - 'mapping.edit' - 'mapping.delete'
  WHERE user_id = user_record.id 
  AND role = 'collaborator'
  AND status = 'ativo';
  
  GET DIAGNOSTICS updated_count = ROW_COUNT;
  
  IF updated_count > 0 THEN
    RETURN 'Permissões de mapeamento removidas com sucesso para ' || collaborator_email || ' em ' || updated_count || ' clube(s).';
  ELSE
    RETURN 'Nenhum registro de colaborador ativo encontrado para ' || collaborator_email;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- 3. Função para listar colaboradores e suas permissões de mapeamento
CREATE OR REPLACE FUNCTION list_collaborators_mapping_permissions()
RETURNS TABLE(
  email TEXT,
  name TEXT,
  club_name TEXT,
  role TEXT,
  has_mapping_view BOOLEAN,
  has_mapping_create BOOLEAN,
  has_mapping_edit BOOLEAN,
  has_mapping_delete BOOLEAN
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    au.email,
    COALESCE(au.raw_user_meta_data->>'name', au.email) as name,
    ci.name as club_name,
    cm.role,
    COALESCE((cm.permissions->>'mapping.view')::boolean, false) as has_mapping_view,
    COALESCE((cm.permissions->>'mapping.create')::boolean, false) as has_mapping_create,
    COALESCE((cm.permissions->>'mapping.edit')::boolean, false) as has_mapping_edit,
    COALESCE((cm.permissions->>'mapping.delete')::boolean, false) as has_mapping_delete
  FROM club_members cm
  JOIN auth.users au ON au.id = cm.user_id
  JOIN club_info ci ON ci.id = cm.club_id
  WHERE cm.role IN ('collaborator', 'medical')
  AND cm.status = 'ativo'
  ORDER BY ci.name, au.email;
END;
$$ LANGUAGE plpgsql;

-- 4. Exemplos de uso (descomente e modifique conforme necessário):

-- Para adicionar permissão a um colaborador específico:
-- SELECT add_mapping_permission_to_collaborator('<EMAIL>');

-- Para remover permissão de um colaborador:
-- SELECT remove_mapping_permission_from_collaborator('<EMAIL>');

-- Para listar todos os colaboradores e suas permissões:
-- SELECT * FROM list_collaborators_mapping_permissions();

-- 5. Script para adicionar permissões a TODOS os colaboradores de um clube específico
-- (Use com cuidado - substitua o ID do clube)
/*
UPDATE club_members 
SET permissions = COALESCE(permissions, '{}'::jsonb) || '{"mapping.view": true, "mapping.create": true, "mapping.edit": true, "mapping.delete": true}'::jsonb
WHERE club_id = 21  -- Substitua pelo ID do seu clube
AND role = 'collaborator'
AND status = 'ativo';
*/

-- 6. Verificar se as permissões foram aplicadas corretamente
-- Esta query mostra todos os usuários com permissões de mapeamento:
/*
SELECT 
  au.email,
  cm.role,
  ci.name as club_name,
  cm.permissions
FROM club_members cm
JOIN auth.users au ON au.id = cm.user_id
JOIN club_info ci ON ci.id = cm.club_id
WHERE cm.permissions ? 'mapping.view'
AND cm.status = 'ativo'
ORDER BY ci.name, cm.role, au.email;
*/

-- 7. Comentários para documentação
COMMENT ON FUNCTION add_mapping_permission_to_collaborator(TEXT) 
IS 'Adiciona permissões de mapeamento a um colaborador específico pelo email';

COMMENT ON FUNCTION remove_mapping_permission_from_collaborator(TEXT) 
IS 'Remove permissões de mapeamento de um colaborador específico pelo email';

COMMENT ON FUNCTION list_collaborators_mapping_permissions() 
IS 'Lista todos os colaboradores e suas permissões de mapeamento';

-- 8. Limpeza das funções (descomente se quiser remover as funções depois)
/*
DROP FUNCTION IF EXISTS add_mapping_permission_to_collaborator(TEXT);
DROP FUNCTION IF EXISTS remove_mapping_permission_from_collaborator(TEXT);
DROP FUNCTION IF EXISTS list_collaborators_mapping_permissions();
*/
