-- Cria a tabela de funções de colaboradores
CREATE TABLE IF NOT EXISTS public.collaborator_roles (
  id SERIAL PRIMARY KEY,
  club_id INTEGER NOT NULL REFERENCES public.club_info(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  role_type TEXT NOT NULL DEFAULT 'other',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  is_active BOOLEAN DEFAULT TRUE,
  
  -- <PERSON><PERSON><PERSON> que não há funções duplicadas no mesmo clube
  CONSTRAINT unique_role_name_per_club UNIQUE (club_id, name)
);

-- Cria um índice para melhorar consultas por clube
CREATE INDEX IF NOT EXISTS idx_collaborator_roles_club_id ON public.collaborator_roles(club_id);

-- Adiciona comentários para documentação
COMMENT ON TABLE public.collaborator_roles IS '<PERSON><PERSON>ena as funções personalizadas dos colaboradores de cada clube';
COMMENT ON COLUMN public.collaborator_roles.role_type IS 'Tipo da função: technical, assistant_technical, administrative, other';

-- Adiciona trigger para atualizar o campo updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_collaborator_roles_updated_at
BEFORE UPDATE ON public.collaborator_roles
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Insere funções padrão para novos clubes
DO $$
DECLARE
  club_record RECORD;
  role_types TEXT[] := ARRAY['technical', 'assistant_technical', 'other'];
  
  -- Funções técnicas
  technical_roles TEXT[] := ARRAY[
    'Técnico', 'Preparador de goleiro', 'Preparador físico', 'Fisioterapeuta',
    'Fisiologista', 'Massagista', 'Massa terapeuta'
  ];
  
  -- Funções de assistência técnica
  assistant_roles TEXT[] := ARRAY[
    'Auxiliar técnico', 'Auxiliar de limpeza', 'Auxiliar de cozinha'
  ];
  
  -- Outras funções
  other_roles TEXT[] := ARRAY[
    'Supervisor', 'Psicóloga', 'Nutricionista', 'Coordenador', 'Motorista',
    'Roupeiro', 'Gerente de futebol', 'CEO', 'Gerente administrativo',
    'Cozinheira', 'Cozinheiro', 'Gerente de operações', 'Presidente',
    'Vice presidente', 'Sócio', 'Fotógrafo', 'Diretor', 'Comunicação',
    'Serviço geral', 'Jardineiro', 'Lavadora de roupas', 'Assistente social'
  ];
  
  role_name TEXT;
  role_type TEXT;
  role_record RECORD;
  role_id_val INTEGER;
  
BEGIN
  -- Para cada clube
  FOR club_record IN SELECT id FROM public.club_info LOOP
    -- Inserir funções técnicas
    FOREACH role_name IN ARRAY technical_roles LOOP
      INSERT INTO public.collaborator_roles 
        (club_id, name, role_type, created_at, is_active)
      VALUES 
        (club_record.id, role_name, 'technical', NOW(), TRUE)
      ON CONFLICT (club_id, name) DO NOTHING;
    END LOOP;
    
    -- Inserir funções de assistência técnica
    FOREACH role_name IN ARRAY assistant_roles LOOP
      INSERT INTO public.collaborator_roles 
        (club_id, name, role_type, created_at, is_active)
      VALUES 
        (club_record.id, role_name, 'assistant_technical', NOW(), TRUE)
      ON CONFLICT (club_id, name) DO NOTHING;
    END LOOP;
    
    -- Inserir outras funções
    FOREACH role_name IN ARRAY other_roles LOOP
      INSERT INTO public.collaborator_roles 
        (club_id, name, role_type, created_at, is_active)
      VALUES 
        (club_record.id, role_name, 'other', NOW(), TRUE)
      ON CONFLICT (club_id, name) DO NOTHING;
    END LOOP;
  END LOOP;
END $$;

-- Adiciona uma coluna de função_id na tabela de colaboradores para referenciar a função
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'collaborators' AND column_name = 'role_id') THEN
    ALTER TABLE public.collaborators 
    ADD COLUMN role_id INTEGER REFERENCES public.collaborator_roles(id);
  END IF;
END $$;

-- Atualiza as funções existentes para usar a nova tabela
DO $$
DECLARE
  club_record RECORD;
  role_record RECORD;
  role_id_val INTEGER;
BEGIN
  -- Para cada clube
  FOR club_record IN SELECT id FROM public.club_info LOOP
    -- Para cada função distinta no clube
    FOR role_record IN 
      SELECT DISTINCT role, role_type 
      FROM public.collaborators 
      WHERE club_id = club_record.id AND role IS NOT NULL
    LOOP
      -- Tenta encontrar a função na tabela de funções
      SELECT id INTO role_id_val 
      FROM public.collaborator_roles 
      WHERE club_id = club_record.id AND name = role_record.role;
      
      -- Se não existir, cria uma nova entrada
      IF role_id_val IS NULL THEN
        INSERT INTO public.collaborator_roles (club_id, name, role_type, created_at, is_active)
        VALUES (club_record.id, role_record.role, role_record.role_type, NOW(), TRUE)
        RETURNING id INTO role_id_val;
      END IF;
      
      -- Atualiza os colaboradores com a nova referência
      UPDATE public.collaborators
      SET role_id = role_id_val
      WHERE club_id = club_record.id 
        AND role = role_record.role
        AND (role_id IS NULL OR role_id != role_id_val);
    END LOOP;
  END LOOP;
END $$;
