import { useState, useEffect, useMemo } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { OficiosTab } from "@/components/administrativo/OficiosTab";
import { TarefasKanbanTab } from "@/components/administrativo/TarefasKanbanTab";
import { LembretesTab } from "@/components/administrativo/LembretesTab";
import { ColaboradoresTab } from "@/components/administrativo/ColaboradoresTab";
import { FornecedoresTab } from "@/components/administrativo/FornecedoresTab";
import { useCurrentClubId } from "@/context/ClubContext";
import { usePermission } from "@/hooks/usePermission";
import { PermissionControl } from "@/components/PermissionControl";
import { ADMINISTRATIVE_PERMISSIONS } from "@/constants/permissions";
import { useUser } from "@/context/UserContext";
import { useAdministrativeDocumentsStore } from "@/store/useAdministrativeDocumentsStore";
import { useAdministrativeTasksStore } from "@/store/useAdministrativeTasksStore";
import { useAdministrativeRemindersStore } from "@/store/useAdministrativeRemindersStore";
import { FileText, Kanban, Calendar, Users, Building } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { getCollaboratorByUserId } from "@/api/api";

export default function Administrativo() {
  const { role, permissions, can, isLoaded } = usePermission();
  const { user } = useUser();

  const allTabs = [
    "oficios",
    "tarefas",
    "lembretes",
    "colaboradores",
    "fornecedores",
  ] as const;

  const tabPermissionMap = {
    oficios: ADMINISTRATIVE_PERMISSIONS.TABS.DOCUMENTS,
    tarefas: ADMINISTRATIVE_PERMISSIONS.TABS.TASKS,
    lembretes: ADMINISTRATIVE_PERMISSIONS.TABS.REMINDERS,
    colaboradores: ADMINISTRATIVE_PERMISSIONS.TABS.COLLABORATORS,
    fornecedores: ADMINISTRATIVE_PERMISSIONS.TABS.SUPPLIERS,
  } as const;

  const allowedTabs = useMemo(() => {
    if (!isLoaded) return [] as string[];
    return allTabs.filter((tab) => can(tabPermissionMap[tab]));
  }, [isLoaded, permissions, role]);

  const [activeTab, setActiveTab] = useState<string>("");
  const clubId = useCurrentClubId();

  const [documentsPage, setDocumentsPage] = useState(1);
  const documentsPageSize = 10;

  // Stores
  const {
    documents,
    total,
    loading: loadingDocuments,
    error: documentsError,
    fetchDocuments,
  } = useAdministrativeDocumentsStore();

  const {
    tasks,
    loading: loadingTasks,
    error: tasksError,
    fetchTasks,
  } = useAdministrativeTasksStore();

  const [currentCollaboratorId, setCurrentCollaboratorId] = useState<number | null>(null);

  const {
    reminders,
    loading: loadingReminders,
    error: remindersError,
    fetchReminders,
  } = useAdministrativeRemindersStore();

  // State for collaborators and suppliers
  const [collaborators, setCollaborators] = useState<any[]>([]);
  const [suppliers, setSuppliers] = useState<any[]>([]);
  const [loadingCollaborators, setLoadingCollaborators] = useState(false);
  const [loadingSuppliers, setLoadingSuppliers] = useState(false);
  const [collaboratorsError, setCollaboratorsError] = useState<string | null>(
    null,
  );
  const [suppliersError, setSuppliersError] = useState<string | null>(null);

  // Load collaborator info for logged user
  useEffect(() => {
    const loadCollaborator = async () => {
      if (!user?.id || !clubId) return;
      try {
        const collab = await getCollaboratorByUserId(clubId, user.id);
        setCurrentCollaboratorId(collab ? collab.id : null);
      } catch (err) {
        console.error('Erro ao buscar colaborador atual:', err);
        setCurrentCollaboratorId(null);
      }
    };
    loadCollaborator();
  }, [user?.id, clubId]);

  // Garantir que a aba ativa seja válida quando as permissões mudarem
  useEffect(() => {
    if (allowedTabs.length > 0 && !allowedTabs.includes(activeTab)) {
      setActiveTab(allowedTabs[0]);
    }
  }, [allowedTabs, activeTab]);

  // Fetch data when component mounts
  useEffect(() => {
    if (!clubId) return;
    fetchDocuments(clubId, documentsPage, documentsPageSize);
    const ownOnly =
      !can("administrative.tasks.view") && can("administrative.tasks.view_own");
    if (ownOnly && currentCollaboratorId === null) {
      return;
    }
    fetchTasks(clubId, ownOnly ? currentCollaboratorId ?? undefined : undefined, ownOnly);
    fetchReminders(clubId);
    fetchCollaborators();
    fetchSuppliers();
  }, [
    clubId,
    documentsPage,
    documentsPageSize,
    fetchDocuments,
    fetchTasks,
    fetchReminders,
    role,
    isLoaded,
    user?.id,
    currentCollaboratorId,
  ]);

  // Fetch collaborators
  const fetchCollaborators = async () => {
    try {
      setLoadingCollaborators(true);
      setCollaboratorsError(null);

      // Usar o Supabase diretamente para evitar problemas de tipagem
      const { data, error } = await supabase
        .from("collaborators")
        .select("*")
        .eq("club_id", clubId)
        .order("full_name");

      if (error) {
        throw new Error(`Error fetching collaborators: ${error.message}`);
      }

      setCollaborators(data || []);
    } catch (err: any) {
      console.error("Error fetching collaborators:", err);
      setCollaboratorsError(err.message || "Error fetching collaborators");
    } finally {
      setLoadingCollaborators(false);
    }
  };

  // Fetch suppliers
  const fetchSuppliers = async () => {
    try {
      setLoadingSuppliers(true);
      setSuppliersError(null);

      // Usar o Supabase diretamente para evitar problemas de tipagem
      const { data, error } = await supabase
        .from("suppliers")
        .select("*")
        .eq("club_id", clubId)
        .order("company_name");

      if (error) {
        throw new Error(`Error fetching suppliers: ${error.message}`);
      }

      setSuppliers(data || []);
    } catch (err: any) {
      console.error("Error fetching suppliers:", err);
      setSuppliersError(err.message || "Error fetching suppliers");
    } finally {
      setLoadingSuppliers(false);
    }
  };

  return (
    <PermissionControl permission={ADMINISTRATIVE_PERMISSIONS.VIEW}>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Administrativo</h1>
          <p className="text-muted-foreground">
            Gerencie documentos, tarefas e lembretes administrativos
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-4 flex gap-2">
            {allowedTabs.includes("oficios") && (
              <TabsTrigger value="oficios" className="flex items-center gap-1">
                <FileText className="h-4 w-4" />
                <span>Ofícios e Memorandos</span>
              </TabsTrigger>
            )}
            {allowedTabs.includes("tarefas") && (
              <TabsTrigger value="tarefas" className="flex items-center gap-1">
                <Kanban className="h-4 w-4" />
                <span>Tarefas Diárias (Kanban)</span>
              </TabsTrigger>
            )}
            {allowedTabs.includes("lembretes") && (
              <TabsTrigger
                value="lembretes"
                className="flex items-center gap-1"
              >
                <Calendar className="h-4 w-4" />
                <span>Agenda e Lembretes</span>
              </TabsTrigger>
            )}
            {allowedTabs.includes("colaboradores") && (
              <TabsTrigger
                value="colaboradores"
                className="flex items-center gap-1"
              >
                <Users className="h-4 w-4" />
                <span>Colaboradores</span>
              </TabsTrigger>
            )}
            {allowedTabs.includes("fornecedores") && (
              <TabsTrigger
                value="fornecedores"
                className="flex items-center gap-1"
              >
                <Building className="h-4 w-4" />
                <span>Fornecedores</span>
              </TabsTrigger>
            )}
          </TabsList>

          {allowedTabs.includes("oficios") && (
            <TabsContent value="oficios">
              <OficiosTab
                documents={documents}
                loading={loadingDocuments}
                error={documentsError}
                clubId={clubId}
                total={total}
                page={documentsPage}
                pageSize={documentsPageSize}
                onPageChange={setDocumentsPage}
              />
            </TabsContent>
          )}

          {allowedTabs.includes("tarefas") && (
            <TabsContent value="tarefas">
              <TarefasKanbanTab
                tasks={tasks}
                loading={loadingTasks}
                error={tasksError}
                clubId={clubId}
                currentCollaboratorId={currentCollaboratorId}
              />
            </TabsContent>
          )}

          {allowedTabs.includes("lembretes") && (
            <TabsContent value="lembretes">
              <LembretesTab
                reminders={reminders}
                loading={loadingReminders}
                error={remindersError}
                clubId={clubId}
              />
            </TabsContent>
          )}

          {allowedTabs.includes("colaboradores") && (
            <TabsContent value="colaboradores">
              <ColaboradoresTab
                collaborators={collaborators}
                loading={loadingCollaborators}
                error={collaboratorsError}
                clubId={clubId}
                onRefresh={fetchCollaborators}
              />
            </TabsContent>
          )}

          {allowedTabs.includes("fornecedores") && (
            <TabsContent value="fornecedores">
              <FornecedoresTab
                suppliers={suppliers}
                loading={loadingSuppliers}
                error={suppliersError}
                clubId={clubId}
                onRefresh={fetchSuppliers}
              />
            </TabsContent>
          )}
        </Tabs>
      </div>
    </PermissionControl>
  );
}