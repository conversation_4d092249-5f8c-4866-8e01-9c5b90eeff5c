import axios from "axios";

const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

export interface PlayerOccurrence {
  id: string;
  player_id: string;
  type: "divergence" | "punishment";
  title: string;
  description: string;
  severity: "low" | "medium" | "high";
  status: "active" | "resolved" | "archived";
  resolution_notes?: string;
  created_at: string;
  updated_at: string;
}

export async function getPlayerOccurrences(playerId: string): Promise<PlayerOccurrence[]> {
  const response = await api.get(`/players/${playerId}/occurrences`);
  return response.data;
}

export async function createPlayerOccurrence(data: Omit<PlayerOccurrence, "id" | "created_at" | "updated_at">): Promise<PlayerOccurrence> {
  const response = await api.post("/occurrences", data);
  return response.data;
}

export async function updatePlayerOccurrence(id: string, data: Partial<PlayerOccurrence>): Promise<PlayerOccurrence> {
  const response = await api.put(`/occurrences/${id}`, data);
  return response.data;
}

export async function deletePlayerOccurrence(id: string): Promise<void> {
  await api.delete(`/occurrences/${id}`);
} 