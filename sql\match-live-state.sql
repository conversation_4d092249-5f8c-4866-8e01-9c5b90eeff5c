-- Sistema de Estado em Tempo Real das Partidas
-- Este arquivo deve ser executado no Supabase SQL Editor

-- 1. <PERSON><PERSON><PERSON> tabela para estado em tempo real das partidas
CREATE TABLE IF NOT EXISTS match_live_state (
  id SERIAL PRIMARY KEY,
  club_id INTEGER NOT NULL,
  match_id UUID NOT NULL,
  
  -- Estado do jogo
  is_live BOOLEAN NOT NULL DEFAULT FALSE,
  is_paused BOOLEAN NOT NULL DEFAULT FALSE,
  is_finished BOOLEAN NOT NULL DEFAULT FALSE,
  
  -- Controle de tempo (usando timestamps do servidor)
  match_start_time TIMESTAMP WITH TIME ZONE, -- Quando o jogo começou
  total_elapsed_seconds INTEGER NOT NULL DEFAULT 0, -- Tempo total jogado em segundos
  pause_start_time TIMESTAMP WITH TIME ZONE, -- Quando foi pausado (se pausado)
  
  -- P<PERSON>car atual
  score_home INTEGER NOT NULL DEFAULT 0,
  score_away INTEGER NOT NULL DEFAULT 0,
  
  -- <PERSON><PERSON><PERSON>
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Garantir que só existe um estado por partida
  UNIQUE(club_id, match_id)
);

-- 2. Criar função para calcular tempo atual da partida
CREATE OR REPLACE FUNCTION get_match_current_time(
  p_match_start_time TIMESTAMP WITH TIME ZONE,
  p_total_elapsed_seconds INTEGER,
  p_is_paused BOOLEAN,
  p_pause_start_time TIMESTAMP WITH TIME ZONE
) RETURNS INTEGER AS $$
BEGIN
  -- Se o jogo não começou ainda
  IF p_match_start_time IS NULL THEN
    RETURN 0;
  END IF;
  
  -- Se o jogo está pausado
  IF p_is_paused AND p_pause_start_time IS NOT NULL THEN
    RETURN p_total_elapsed_seconds;
  END IF;
  
  -- Se o jogo está rodando
  RETURN p_total_elapsed_seconds + EXTRACT(EPOCH FROM (NOW() - p_match_start_time))::INTEGER;
END;
$$ LANGUAGE plpgsql;

-- 3. Criar função para iniciar partida
CREATE OR REPLACE FUNCTION start_match_live(
  p_club_id INTEGER,
  p_match_id UUID
) RETURNS JSONB AS $$
DECLARE
  result JSONB;
BEGIN
  -- Inserir ou atualizar estado da partida
  INSERT INTO match_live_state (
    club_id, 
    match_id, 
    is_live, 
    is_paused, 
    is_finished,
    match_start_time,
    total_elapsed_seconds,
    pause_start_time
  ) VALUES (
    p_club_id,
    p_match_id,
    TRUE,
    FALSE,
    FALSE,
    NOW(),
    0,
    NULL
  )
  ON CONFLICT (club_id, match_id) 
  DO UPDATE SET
    is_live = TRUE,
    is_paused = FALSE,
    is_finished = FALSE,
    match_start_time = NOW(),
    total_elapsed_seconds = 0,
    pause_start_time = NULL,
    updated_at = NOW()
  RETURNING to_jsonb(match_live_state.*) INTO result;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql;

-- 4. Criar função para pausar partida
CREATE OR REPLACE FUNCTION pause_match_live(
  p_club_id INTEGER,
  p_match_id UUID
) RETURNS JSONB AS $$
DECLARE
  current_state RECORD;
  total_elapsed INTEGER;
  result JSONB;
BEGIN
  -- Buscar estado atual
  SELECT * INTO current_state
  FROM match_live_state
  WHERE club_id = p_club_id AND match_id = p_match_id;

  IF NOT FOUND THEN
    RAISE EXCEPTION 'Match state not found';
  END IF;

  -- Calcular tempo total decorrido
  total_elapsed := get_match_current_time(
    current_state.match_start_time,
    current_state.total_elapsed_seconds,
    current_state.is_paused,
    current_state.pause_start_time
  );

  -- Atualizar estado para pausado
  UPDATE match_live_state SET
    is_paused = TRUE,
    pause_start_time = NOW(),
    total_elapsed_seconds = total_elapsed,
    updated_at = NOW()
  WHERE club_id = p_club_id AND match_id = p_match_id
  RETURNING to_jsonb(match_live_state.*) INTO result;

  RETURN result;
END;
$$ LANGUAGE plpgsql;

-- 5. Criar função para retomar partida
CREATE OR REPLACE FUNCTION resume_match_live(
  p_club_id INTEGER,
  p_match_id UUID
) RETURNS JSONB AS $$
DECLARE
  result JSONB;
BEGIN
  -- Atualizar estado para rodando
  UPDATE match_live_state SET
    is_paused = FALSE,
    match_start_time = NOW(), -- Novo timestamp de início
    pause_start_time = NULL,
    updated_at = NOW()
  WHERE club_id = p_club_id AND match_id = p_match_id
  RETURNING to_jsonb(match_live_state.*) INTO result;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql;

-- 6. Criar função para finalizar partida
CREATE OR REPLACE FUNCTION finish_match_live(
  p_club_id INTEGER,
  p_match_id UUID
) RETURNS JSONB AS $$
DECLARE
  current_state RECORD;
  final_elapsed INTEGER;
  result JSONB;
BEGIN
  -- Buscar estado atual
  SELECT * INTO current_state 
  FROM match_live_state 
  WHERE club_id = p_club_id AND match_id = p_match_id;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Match state not found';
  END IF;
  
  -- Calcular tempo final
  final_elapsed := get_match_current_time(
    current_state.match_start_time,
    current_state.total_elapsed_seconds,
    current_state.is_paused,
    current_state.pause_start_time
  );
  
  -- Finalizar partida
  UPDATE match_live_state SET
    is_live = FALSE,
    is_paused = FALSE,
    is_finished = TRUE,
    total_elapsed_seconds = final_elapsed,
    match_start_time = NULL,
    pause_start_time = NULL,
    updated_at = NOW()
  WHERE club_id = p_club_id AND match_id = p_match_id
  RETURNING to_jsonb(match_live_state.*) INTO result;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql;

-- 7. Criar função para atualizar placar
CREATE OR REPLACE FUNCTION update_match_score(
  p_club_id INTEGER,
  p_match_id UUID,
  p_score_home INTEGER,
  p_score_away INTEGER
) RETURNS JSONB AS $$
DECLARE
  result JSONB;
BEGIN
  -- Atualizar placar
  UPDATE match_live_state SET
    score_home = p_score_home,
    score_away = p_score_away,
    updated_at = NOW()
  WHERE club_id = p_club_id AND match_id = p_match_id
  RETURNING to_jsonb(match_live_state.*) INTO result;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql;

-- 8. Criar função para obter estado atual com tempo calculado
CREATE OR REPLACE FUNCTION get_match_live_state(
  p_club_id INTEGER,
  p_match_id UUID
) RETURNS JSONB AS $$
DECLARE
  state RECORD;
  calculated_time INTEGER;
  result JSONB;
BEGIN
  -- Buscar estado
  SELECT * INTO state
  FROM match_live_state
  WHERE club_id = p_club_id AND match_id = p_match_id;

  IF NOT FOUND THEN
    -- Retornar estado inicial se não existir
    RETURN jsonb_build_object(
      'exists', false,
      'is_live', false,
      'is_paused', false,
      'is_finished', false,
      'current_time_seconds', 0,
      'score_home', 0,
      'score_away', 0
    );
  END IF;

  -- Calcular tempo atual
  calculated_time := get_match_current_time(
    state.match_start_time,
    state.total_elapsed_seconds,
    state.is_paused,
    state.pause_start_time
  );

  -- Construir resposta
  result := to_jsonb(state) || jsonb_build_object(
    'exists', true,
    'current_time_seconds', calculated_time
  );

  RETURN result;
END;
$$ LANGUAGE plpgsql;

-- 9. Criar índices para performance
CREATE INDEX IF NOT EXISTS idx_match_live_state_club_match 
ON match_live_state(club_id, match_id);

CREATE INDEX IF NOT EXISTS idx_match_live_state_live 
ON match_live_state(is_live) WHERE is_live = true;

-- 10. Criar trigger para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_match_live_state_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_match_live_state_updated_at
  BEFORE UPDATE ON match_live_state
  FOR EACH ROW
  EXECUTE FUNCTION update_match_live_state_updated_at();

-- 11. Comentários para documentação
COMMENT ON TABLE match_live_state IS 'Estado em tempo real das partidas para persistir tempo, placar e status';
COMMENT ON FUNCTION get_match_current_time IS 'Calcula o tempo atual da partida baseado em timestamps do servidor';
COMMENT ON FUNCTION start_match_live IS 'Inicia uma partida e cria/atualiza seu estado';
COMMENT ON FUNCTION pause_match_live IS 'Pausa uma partida e salva o tempo decorrido';
COMMENT ON FUNCTION resume_match_live IS 'Retoma uma partida pausada';
COMMENT ON FUNCTION finish_match_live IS 'Finaliza uma partida e salva o tempo final';
COMMENT ON FUNCTION update_match_score IS 'Atualiza o placar da partida';
COMMENT ON FUNCTION get_match_live_state IS 'Obtém o estado atual da partida com tempo calculado';
