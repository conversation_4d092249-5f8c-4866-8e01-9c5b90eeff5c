import { useState, useEffect } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal } from "lucide-react";
import type { Player, Category } from "@/api/api";
import { getPlayerCategories } from "@/api/api";
import { useCurrentClubId } from "@/context/ClubContext";
import { parseISO } from "date-fns";
import "@/styles/player-card.css";

interface PlayerCardProps {
  player: Player;
  onClick?: () => void;
  onEdit?: () => void;
  onStatusChange?: (status: string) => void;
  onDelete?: (player: Player) => void;
  canEditStatus?: boolean;
}

export function PlayerCard({ player, onClick, onEdit, onStatusChange, onDelete, canEditStatus = false }: PlayerCardProps) {
  const clubId = useCurrentClubId();
  const [playerCategory, setPlayerCategory] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);

  // Buscar a categoria do jogador
  useEffect(() => {
    const fetchPlayerCategory = async () => {
      if (!player.id || !clubId) return;

      setLoading(true);
      try {
        const categories = await getPlayerCategories(clubId, player.id);
        if (categories && categories.length > 0) {
          setPlayerCategory(categories[0].name);
        }
      } catch (error) {
        console.error("Erro ao buscar categoria do jogador:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchPlayerCategory();
  }, [player.id, clubId]);

  // Formatar o status para exibição
  const formatStatus = (status: string) => {
    switch (status) {
      case "disponivel":
        return "Disponível";
      case "lesionado":
        return "Lesionado";
      case "suspenso":
        return "Suspenso";
      case "em recuperacao":
      case "em recuperação":
        return "Em recuperação";
      case "inativo":
        return "Inativo";
      case "emprestado":
        return "Emprestado";
      case "em avaliacao":
        return "Em Avaliação";
      default:
        return status;
    }
  };

  // Formatar a data de fim de contrato
  const formatContractEndDate = () => {
    if (!player.contract_end_date) return "Não informado";

    try {
      // Use parseISO to avoid timezone issues with date strings in YYYY-MM-DD format
      const date = parseISO(player.contract_end_date);
      const month = date.toLocaleString('pt-BR', { month: 'short' });
      const year = date.getFullYear();
      return `${month} ${year}`;
    } catch (error) {
      console.error("Erro ao formatar data de fim de contrato:", error);
      return "Não informado";
    }
  };

  return (
    <div className="player-card border rounded-lg overflow-hidden shadow-sm bg-white relative group">
      {/* Botão de editar que aparece ao passar o mouse */}
      {onEdit && (
        <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity z-10">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="secondary" size="icon" className="h-8 w-8 rounded-full bg-white shadow-md">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={(e) => {
                e.stopPropagation();
                onClick && onClick();
              }}>Ver perfil</DropdownMenuItem>
              <DropdownMenuItem onClick={(e) => {
                e.stopPropagation();
                onEdit && onEdit();
              }}>Editar</DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={(e) => {
                e.stopPropagation();
                window.location.href = `/jogador/${player.id}/historico-medico`;
              }}>Histórico médico</DropdownMenuItem>
              <DropdownMenuItem onClick={(e) => {
                e.stopPropagation();
                window.location.href = `/jogador/${player.id}/estatisticas`;
              }}>Estatísticas</DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-red-600" onClick={(e) => {
                e.stopPropagation();
                if (confirm(`Tem certeza que deseja remover ${player.name} do elenco?`)) {
                  onDelete && onDelete(player);
                }
              }}>
                Remover do elenco
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      )}

      {/* Conteúdo principal do card */}
      <div className="bg-[var(--color-primary)] p-4 relative" onClick={onClick} style={{
        backgroundColor: 'var(--color-primary)'
      }}>
        {/* Status com opção de mudar */}
        {onStatusChange && canEditStatus ? (
          <select
            value={player.status || "disponivel"}
            onChange={(e) => {
              e.stopPropagation();
              const newStatus = e.target.value;

              // Se o status for alterado para 'inativo', mostrar confirmação
              if (newStatus === 'inativo') {
                const confirmed = window.confirm(
                  `Tem certeza que deseja alterar o status de ${player.name} para "Inativo"?\n\n` +
                  'Esta ação irá remover o jogador de:\n' +
                  '• Todas as categorias\n' +
                  '• Alojamentos ativos\n' +
                  '• Salários ativos\n' +
                  '• Treinamentos futuros\n' +
                  '• Convocações futuras\n' +
                  '• Agenda de eventos\n' +
                  '• Registros médicos pendentes\n\n' +
                  'O histórico será preservado para consulta.'
                );

                if (!confirmed) {
                  // Reverter a seleção se o usuário cancelar
                  e.target.value = player.status || "disponivel";
                  return;
                }
              }

              onStatusChange(newStatus);
            }}
            className="absolute top-2 left-2 text-xs border rounded px-2 py-1 z-10"
            style={{
              backgroundColor: player.status === "disponivel" ? "#d1fae5" :
                            player.status === "lesionado" ? "#fee2e2" :
                            player.status === "suspenso" ? "#fef3c7" :
                            player.status === "inativo" ? "#e5e7eb" :
                            player.status === "emprestado" ? "#dbeafe" : "#ffedd5",
              color: player.status === "disponivel" ? "#065f46" :
                     player.status === "lesionado" ? "#991b1b" :
                     player.status === "suspenso" ? "#92400e" :
                     player.status === "inativo" ? "#374151" :
                     player.status === "emprestado" ? "#1e40af" : "#9a3412",
              borderColor: "transparent"
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <option value="disponivel">Disponível</option>
            <option value="lesionado">Lesionado</option>
            <option value="suspenso">Suspenso</option>
            <option value="em recuperacao">Em recuperação</option>
            <option value="inativo">Inativo</option>
            <option value="emprestado">Emprestado</option>
          </select>
        ) : (
          <div className={`player-status status-${player.status}`}>
            {formatStatus(player.status)}
          </div>
        )}

        {/* Número de cadastro */}
        {player.registration_number && (
          <div className="registration-number">
            Cadastro: {player.registration_number}
          </div>
        )}

        <div className="flex justify-center mt-8">
          <div className="avatar-container">
            <Avatar className="h-20 w-20 player-avatar">
              <AvatarImage src={player.image || ""} alt={player.name} />
              <AvatarFallback 
                style={{
                  backgroundColor: 'var(--color-primary)',
                  color: 'white',
                  fontSize: '1.25rem',
                  fontWeight: 'bold'
                }}
              >
                {player.name.charAt(0)}
              </AvatarFallback>
            </Avatar>
          </div>
        </div>
      </div>

      <div className="p-4" onClick={onClick}>
        <h3 className="font-bold text-lg text-center mb-1">{player.name}</h3>
        <p className="text-center text-sm text-gray-600 mb-1">
          {player.position} • {player.age} anos
        </p>
        {playerCategory && (
          <p className="text-center text-xs bg-gray-100 rounded-full py-1 px-2 mx-auto w-fit mb-3">
            {playerCategory}
          </p>
        )}

        <div className="grid grid-cols-2 gap-2 text-sm">
          <div>
            <p className="text-gray-500">Nacionalidade:</p>
            <p className="font-medium">{player.nationality || "—"}</p>
          </div>
          <div>
            <p className="text-gray-500">Contrato até:</p>
            <p className="font-medium">{formatContractEndDate()}</p>
          </div>
        </div>
      </div>
    </div>
  );
}
