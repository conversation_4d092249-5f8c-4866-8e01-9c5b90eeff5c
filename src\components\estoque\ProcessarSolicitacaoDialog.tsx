import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { Loader2, Check } from "lucide-react";
import { useUser } from "@/context/UserContext";
import {
  processInventoryRequestWithAutoSignature,
  getInventoryRequestItems,
  InventoryRequestItem
} from "@/api/api";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";

interface ProcessarSolicitacaoDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  requestId: number | null;
  clubId: number;
  onSuccess?: () => void;
}

export function ProcessarSolicitacaoDialog({
  open,
  onOpenChange,
  requestId,
  clubId,
  onSuccess
}: ProcessarSolicitacaoDialogProps) {
  const [loading, setLoading] = useState(false);
  const [items, setItems] = useState<InventoryRequestItem[]>([]);
  const [deliveryNotes, setDeliveryNotes] = useState("");
  const { user } = useUser();
  const { toast } = useToast();

  // Load items when dialog opens
  useEffect(() => {
    if (open && requestId) {
      loadItems();
    }
  }, [open, requestId]);

  // Load request items
  const loadItems = async () => {
    if (!requestId) return;

    try {
      setLoading(true);
      const data = await getInventoryRequestItems(clubId, requestId);
      setItems(data);
    } catch (error) {
      console.error("Error loading request items:", error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao carregar os itens da solicitação.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Process request with automatic signature
  const handleProcess = async () => {
    if (!requestId) return;

    setLoading(true);

    try {
      // Process the request with automatic signature
      const result = await processInventoryRequestWithAutoSignature(
        clubId,
        requestId,
        deliveryNotes,
        user?.id
      );

      if (result) {
        toast({
          title: "Solicitação processada",
          description: "A solicitação foi processada e assinada automaticamente com sucesso.",
        });

        if (onSuccess) {
          onSuccess();
        }

        onOpenChange(false);
      } else {
        toast({
          title: "Erro ao processar solicitação",
          description: "Não foi possível processar a solicitação.",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error processing request:", error);
      toast({
        title: "Erro ao processar solicitação",
        description: "Ocorreu um erro ao processar a solicitação.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Processar Solicitação</DialogTitle>
        </DialogHeader>

        <div className="py-4">
          <h3 className="text-sm font-medium mb-2">Itens da Solicitação</h3>

          <div className="border rounded-md mb-4">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Produto</TableHead>
                  <TableHead>Departamento</TableHead>
                  <TableHead>Quantidade</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {items.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={3} className="text-center py-4">
                      Nenhum item encontrado
                    </TableCell>
                  </TableRow>
                ) : (
                  items.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell>{item.product_name}</TableCell>
                      <TableCell>{item.product_department}</TableCell>
                      <TableCell>{item.quantity}</TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          <div className="mb-4">
            <Label htmlFor="delivery-notes">Observações de Entrega</Label>
            <Textarea
              id="delivery-notes"
              placeholder="Observações sobre a entrega dos itens (opcional)"
              value={deliveryNotes}
              onChange={(e) => setDeliveryNotes(e.target.value)}
              className="mt-1"
            />
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
            <p className="text-sm text-blue-800">
              <strong>Assinatura Digital Automática:</strong> Ao processar esta solicitação, uma assinatura digital será gerada automaticamente com seus dados e horário atual, conforme MP 2.200-2/2001.
            </p>
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button
            onClick={handleProcess}
            disabled={loading}
            className="gap-2"
          >
            {loading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Check className="h-4 w-4" />
            )}
            Processar Solicitação
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
