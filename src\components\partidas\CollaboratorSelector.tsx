import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Search, Users, Briefcase, Shield } from "lucide-react";
import { getCollaborators, type Collaborator } from "@/api/api";

interface CollaboratorSelectorProps {
  clubId: number;
  onCollaboratorSelect: (collaborator: Collaborator, role: 'technical_staff' | 'staff' | 'executive') => void;
  title?: string;
  subtitle?: string;
}

export function CollaboratorSelector({
  clubId,
  onCollaboratorSelect,
  title = "Selecionar Colaboradores",
  subtitle = "Escolha colaboradores para adicionar à partida"
}: CollaboratorSelectorProps) {
  const [collaborators, setCollaborators] = useState<Collaborator[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState<string>("");
  const [selectedRole, setSelectedRole] = useState<'technical_staff' | 'staff' | 'executive'>('technical_staff');

  useEffect(() => {
    loadCollaborators();
  }, [clubId]);

  const loadCollaborators = async () => {
    if (!clubId) return;
    
    setLoading(true);
    try {
      const data = await getCollaborators(clubId);
      // Filtrar apenas colaboradores ativos
      const activeCollaborators = data.filter(c => 
        c.status !== 'inativo' && c.status !== 'demitido'
      );
      setCollaborators(activeCollaborators);
    } catch (error) {
      console.error("Erro ao carregar colaboradores:", error);
    } finally {
      setLoading(false);
    }
  };

  // Filtrar colaboradores baseado na busca e filtros
  const filteredCollaborators = collaborators.filter(collaborator => {
    const matchesSearch = collaborator.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         collaborator.role.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesRole = !roleFilter || collaborator.role_type === roleFilter;
    
    return matchesSearch && matchesRole;
  });

  // Obter tipos de função únicos
  const uniqueRoleTypes = Array.from(new Set(collaborators.map(c => c.role_type).filter(Boolean))).sort();

  // Determinar o papel baseado no tipo de função do colaborador
  const getMatchRole = (collaborator: Collaborator): 'technical_staff' | 'staff' | 'executive' => {
    const roleType = collaborator.role_type?.toLowerCase() || '';
    const role = collaborator.role?.toLowerCase() || '';

    // Comissão técnica - termos mais abrangentes
    if (roleType.includes('técnic') || roleType.includes('tecnic') || roleType.includes('esport') ||
        role.includes('técnic') || role.includes('tecnic') || role.includes('treinad') ||
        role.includes('prepar') || role.includes('fisio') || role.includes('médic') ||
        role.includes('medic') || role.includes('nutri') || role.includes('massag') ||
        role.includes('auxiliar técnic') || role.includes('comissão') ||
        roleType.includes('comissão') || roleType.includes('comissao')) {
      return 'technical_staff';
    }

    // Diretoria executiva - termos mais abrangentes
    if (roleType.includes('diret') || roleType.includes('execut') || roleType.includes('presid') ||
        role.includes('diret') || role.includes('presid') || role.includes('vice') ||
        role.includes('execut') || role.includes('conselho') || role.includes('administr') ||
        roleType.includes('administr') || roleType.includes('conselho')) {
      return 'executive';
    }

    // Staff geral
    return 'staff';
  };

  const getRoleIcon = (role: 'technical_staff' | 'staff' | 'executive') => {
    switch (role) {
      case 'technical_staff':
        return <Briefcase className="h-4 w-4" />;
      case 'executive':
        return <Shield className="h-4 w-4" />;
      default:
        return <Users className="h-4 w-4" />;
    }
  };

  const getRoleLabel = (role: 'technical_staff' | 'staff' | 'executive') => {
    switch (role) {
      case 'technical_staff':
        return 'Comissão Técnica';
      case 'executive':
        return 'Diretoria';
      default:
        return 'Staff';
    }
  };

  const getRoleColor = (role: 'technical_staff' | 'staff' | 'executive') => {
    switch (role) {
      case 'technical_staff':
        return 'bg-blue-100 text-blue-800';
      case 'executive':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-green-100 text-green-800';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {subtitle && <p className="text-sm text-muted-foreground">{subtitle}</p>}
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Seletor de papel na partida */}
        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
          <label className="block text-sm font-medium text-blue-900 mb-2">
            Papel na partida:
          </label>
          <div className="grid grid-cols-3 gap-2">
            <label className={`flex items-center gap-2 p-3 border rounded-md cursor-pointer transition-colors ${
              selectedRole === 'technical_staff'
                ? 'bg-blue-100 border-blue-500 text-blue-900'
                : 'bg-white hover:bg-gray-50'
            }`}>
              <input
                type="radio"
                name="match-role"
                value="technical_staff"
                checked={selectedRole === 'technical_staff'}
                onChange={(e) => setSelectedRole(e.target.value as 'technical_staff')}
                className="h-4 w-4"
              />
              <Briefcase className="h-4 w-4" />
              <span className="text-sm font-medium">Comissão Técnica</span>
            </label>

            <label className={`flex items-center gap-2 p-3 border rounded-md cursor-pointer transition-colors ${
              selectedRole === 'staff'
                ? 'bg-green-100 border-green-500 text-green-900'
                : 'bg-white hover:bg-gray-50'
            }`}>
              <input
                type="radio"
                name="match-role"
                value="staff"
                checked={selectedRole === 'staff'}
                onChange={(e) => setSelectedRole(e.target.value as 'staff')}
                className="h-4 w-4"
              />
              <Users className="h-4 w-4" />
              <span className="text-sm font-medium">Staff</span>
            </label>

            <label className={`flex items-center gap-2 p-3 border rounded-md cursor-pointer transition-colors ${
              selectedRole === 'executive'
                ? 'bg-purple-100 border-purple-500 text-purple-900'
                : 'bg-white hover:bg-gray-50'
            }`}>
              <input
                type="radio"
                name="match-role"
                value="executive"
                checked={selectedRole === 'executive'}
                onChange={(e) => setSelectedRole(e.target.value as 'executive')}
                className="h-4 w-4"
              />
              <Shield className="h-4 w-4" />
              <span className="text-sm font-medium">Diretoria</span>
            </label>
          </div>
        </div>

        {/* Filtros */}
        <div className="flex gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Buscar por nome ou função..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {uniqueRoleTypes.length > 1 && (
            <select
              value={roleFilter}
              onChange={(e) => setRoleFilter(e.target.value)}
              className="border rounded-md px-3 py-2 min-w-[150px]"
            >
              <option value="">Todos os tipos</option>
              {uniqueRoleTypes.map(roleType => (
                <option key={roleType} value={roleType}>
                  {roleType}
                </option>
              ))}
            </select>
          )}
        </div>

        {/* Lista de colaboradores */}
        {loading ? (
          <div className="text-center py-8">Carregando colaboradores...</div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-[400px] overflow-y-auto">
            {filteredCollaborators.length === 0 ? (
              <div className="col-span-full text-center py-8 text-gray-500">
                {searchTerm || roleFilter ? 
                  "Nenhum colaborador encontrado com os filtros aplicados" : 
                  "Nenhum colaborador disponível"
                }
              </div>
            ) : (
              filteredCollaborators.map(collaborator => {
                return (
                  <CollaboratorCard
                    key={collaborator.id}
                    collaborator={collaborator}
                    matchRole={selectedRole}
                    onSelect={() => onCollaboratorSelect(collaborator, selectedRole)}
                    getRoleIcon={getRoleIcon}
                    getRoleLabel={getRoleLabel}
                    getRoleColor={getRoleColor}
                  />
                );
              })
            )}
          </div>
        )}

        {/* Estatísticas */}
        <div className="text-sm text-gray-500 border-t pt-3">
          Mostrando {filteredCollaborators.length} de {collaborators.length} colaboradores
        </div>
      </CardContent>
    </Card>
  );
}

interface CollaboratorCardProps {
  collaborator: Collaborator;
  matchRole: 'technical_staff' | 'staff' | 'executive';
  onSelect: () => void;
  getRoleIcon: (role: 'technical_staff' | 'staff' | 'executive') => JSX.Element;
  getRoleLabel: (role: 'technical_staff' | 'staff' | 'executive') => string;
  getRoleColor: (role: 'technical_staff' | 'staff' | 'executive') => string;
}

function CollaboratorCard({ 
  collaborator, 
  matchRole, 
  onSelect, 
  getRoleIcon, 
  getRoleLabel, 
  getRoleColor 
}: CollaboratorCardProps) {
  return (
    <div
      className="border rounded-lg p-3 cursor-pointer transition-all hover:shadow-md hover:bg-blue-50 hover:border-blue-300"
      onClick={onSelect}
    >
      <div className="flex items-center gap-3">
        <Avatar className="w-10 h-10">
          <AvatarImage src={collaborator.image || ""} />
          <AvatarFallback className="bg-blue-600 text-white font-bold">
            {collaborator.full_name.split(' ').map(n => n.charAt(0)).join('').substring(0, 2)}
          </AvatarFallback>
        </Avatar>
        
        <div className="flex-1 min-w-0">
          <div className="font-medium truncate">{collaborator.full_name}</div>
          <div className="text-sm text-gray-500 truncate">{collaborator.role}</div>
          
          {/* Badge do papel na partida */}
          <div className="flex items-center gap-1 mt-1">
            <Badge className={`text-xs ${getRoleColor(matchRole)}`}>
              {getRoleIcon(matchRole)}
              <span className="ml-1">{getRoleLabel(matchRole)}</span>
            </Badge>
          </div>
        </div>
      </div>
      
      {/* Status do colaborador */}
      <div className="mt-2 pt-2 border-t">
        <div className="flex items-center gap-1">
          <div className={`w-2 h-2 rounded-full ${getStatusColor(collaborator.status)}`} />
          <span className="text-xs text-gray-500 capitalize">{collaborator.status || 'ativo'}</span>
        </div>
      </div>
    </div>
  );
}

// Função para obter a cor do status do colaborador
function getStatusColor(status: string | null): string {
  switch (status) {
    case 'ativo':
    case null:
      return 'bg-green-500';
    case 'inativo':
      return 'bg-gray-500';
    case 'demitido':
      return 'bg-red-500';
    case 'afastado':
      return 'bg-yellow-500';
    default:
      return 'bg-gray-400';
  }
}
