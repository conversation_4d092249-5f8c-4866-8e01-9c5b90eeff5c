import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { PlayerEvaluation } from "@/api/api";
import { formatDate } from "@/utils/formatters";
import { CheckCircle, Clock, XCircle, Calendar, User, FileText } from "lucide-react";

interface ViewEvaluationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  evaluation: PlayerEvaluation;
}

export function ViewEvaluationDialog({
  open,
  onOpenChange,
  evaluation,
}: ViewEvaluationDialogProps) {
  // Renderizar status com badge
  const renderStatus = (status: string) => {
    switch (status) {
      case "pending":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-200">
            <Clock className="h-3 w-3 mr-1" />
            Pendente
          </Badge>
        );
      case "approved":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
            <CheckCircle className="h-3 w-3 mr-1" />
            Aprovado
          </Badge>
        );
      case "rejected":
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800 border-red-200">
            <XCircle className="h-3 w-3 mr-1" />
            Reprovado
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="bg-gray-100 text-gray-800 border-gray-200">
            {status}
          </Badge>
        );
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Detalhes do Pré Cadastro</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Cabeçalho com informações do atleta */}
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-3">
              <Avatar className="h-12 w-12">
                {evaluation.player_image ? (
                  <AvatarImage src={evaluation.player_image} alt={evaluation.player_name} />
                ) : null}
                <AvatarFallback 
                  className="text-white"
                  style={{ backgroundColor: 'var(--color-primary)' }}
                >
                  {evaluation.player_name?.slice(0, 2).toUpperCase() || "AT"}
                </AvatarFallback>
              </Avatar>
              <div>
                <h3 className="font-semibold text-lg">{evaluation.player_name}</h3>
                <p className="text-sm text-muted-foreground">
                  {evaluation.position || "Posição não informada"}
                </p>
              </div>
            </div>
            {renderStatus(evaluation.status)}
          </div>

          <Separator />

          {/* Informações da avaliação */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="text-sm font-medium mb-2">Informações Gerais</h4>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Data: {formatDate(evaluation.evaluation_date)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Responsável: {evaluation.evaluator_name || "Não informado"}</span>
                </div>
                {evaluation.approved_by && (
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">Aprovado por: {evaluation.approved_by_name || evaluation.approved_by}</span>
                  </div>
                )}
                {evaluation.approval_date && (
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">Data de aprovação: {formatDate(evaluation.approval_date)}</span>
                  </div>
                )}
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium mb-2">Características Físicas</h4>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <p className="text-xs text-muted-foreground">Altura</p>
                  <p className="text-sm">{evaluation.height ? `${evaluation.height} cm` : "Não informado"}</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Peso</p>
                  <p className="text-sm">{evaluation.weight ? `${evaluation.weight} kg` : "Não informado"}</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Pé dominante</p>
                  <p className="text-sm">{evaluation.dominant_foot || "Não informado"}</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Idade</p>
                  <p className="text-sm">{evaluation.age ? `${evaluation.age} anos` : "Não informado"}</p>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Avaliação técnica */}
          <div>
            <h4 className="text-sm font-medium mb-2">Avaliação de Pré Cadastro</h4>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
              {evaluation.technical_evaluation && Object.entries(evaluation.technical_evaluation).map(([key, value]) => (
                <div key={key}>
                  <p className="text-xs text-muted-foreground capitalize">{key.replace(/_/g, ' ')}</p>
                  <p className="text-sm font-medium">{value}/10</p>
                </div>
              ))}
            </div>
          </div>

          {/* Observações */}
          <div>
            <h4 className="text-sm font-medium mb-2">Observações</h4>
            <div className="bg-muted/50 p-3 rounded-md">
              <p className="text-sm whitespace-pre-wrap">{evaluation.notes || "Sem observações"}</p>
            </div>
          </div>

          {/* Assinaturas */}
          {(evaluation.evaluator_signature_url || evaluation.approval_signature_url) && (
            <>
              <Separator />
              <div>
                <h4 className="text-sm font-medium mb-2">Assinaturas</h4>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {evaluation.evaluator_signature_url && (
                    <div>
                      <p className="text-xs text-muted-foreground mb-1">Avaliador</p>
                      <img
                        src={evaluation.evaluator_signature_url}
                        alt="Assinatura do avaliador"
                        className="max-h-16 border rounded-md p-1"
                      />
                    </div>
                  )}
                  {evaluation.approval_signature_url && (
                    <div>
                      <p className="text-xs text-muted-foreground mb-1">Aprovador</p>
                      <img
                        src={evaluation.approval_signature_url}
                        alt="Assinatura do aprovador"
                        className="max-h-16 border rounded-md p-1"
                      />
                    </div>
                  )}
                </div>
              </div>
            </>
          )}

          {/* Botões de ação */}
          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Fechar
            </Button>
            {evaluation.pdf_url && (
              <Button
                variant="default"
                onClick={() => window.open(evaluation.pdf_url, '_blank')}
              >
                <FileText className="h-4 w-4 mr-2" />
                Ver PDF
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
