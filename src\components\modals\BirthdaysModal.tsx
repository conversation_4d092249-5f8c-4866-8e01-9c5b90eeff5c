import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Gift } from "lucide-react";
import { useMonthlyBirthdays } from "@/hooks/useMonthlyBirthdays";
import { useCurrentClubId } from "@/context/ClubContext";
import { useCategoriesStore } from "@/store/useCategoriesStore";
import { formatDateUTC } from "@/lib/utils";

interface BirthdaysModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedCategoryId?: number;
}

export function BirthdaysModal({ open, onOpenChange, selectedCategoryId }: BirthdaysModalProps) {
  const clubId = useCurrentClubId();
  const { categories } = useCategoriesStore();
  const { birthdays, loading } = useMonthlyBirthdays(clubId, selectedCategoryId);

  const selectedCategory = selectedCategoryId ? categories.find(c => c.id === selectedCategoryId) : null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Gift className="h-5 w-5" />
            Aniversariantes do Mês
            {selectedCategory && (
              <Badge variant="outline">{selectedCategory.name}</Badge>
            )}
          </DialogTitle>
        </DialogHeader>

        {loading ? (
          <div className="py-8 text-center text-muted-foreground">Carregando...</div>
        ) : birthdays.length === 0 ? (
          <div className="py-8 text-center text-muted-foreground">Nenhum aniversariante encontrado.</div>
        ) : (
          <div className="space-y-2">
            {birthdays.map(person => (
              <div key={`${person.type}-${person.id}`} className="flex items-center justify-between p-2 bg-muted/20 rounded-lg">
                <span className="font-medium">{person.name}</span>
                <span className="text-sm text-muted-foreground">
                  {formatDateUTC(person.birthdate)}
                </span>
              </div>
            ))}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}