import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers':
    'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

const BREVO_API_KEY = Deno.env.get('BREVO_API_KEY') ?? ''
const BREVO_API_URL = 'https://api.brevo.com/v3/smtp/email'
const SENDER_NAME = 'clubeFut'
const SENDER_EMAIL = '<EMAIL>'

const SUPABASE_URL = Deno.env.get('SUPABASE_URL') ?? ''
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
const SITE_URL = Deno.env.get('SITE_URL') ?? 'http://localhost:3000'

function generateToken(length = 40) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let token = ''
  for (let i = 0; i < length; i++) {
    token += chars[Math.floor(Math.random() * chars.length)]
  }
  return token
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    console.log('CORS preflight for password-reset-request')
    return new Response('ok', { status: 200, headers: corsHeaders })
  }

  const missingEnv = []
  if (!SUPABASE_URL) missingEnv.push('SUPABASE_URL')
  if (!SUPABASE_SERVICE_ROLE_KEY) missingEnv.push('SUPABASE_SERVICE_ROLE_KEY')
  if (!BREVO_API_KEY) missingEnv.push('BREVO_API_KEY')
  if (missingEnv.length) {
    console.log('Missing env vars', missingEnv.join(','))
    return new Response(
      JSON.stringify({ error: 'Configuração do servidor incompleta' }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  }

  try {
    const { email } = await req.json().catch(() => ({}))
    console.log('Password reset request for', email)

    if (!email) {
      return new Response(JSON.stringify({ error: 'Email é obrigatório' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    if (!BREVO_API_KEY) {
      console.log('Missing BREVO_API_KEY')
      return new Response(
        JSON.stringify({ error: 'Configuração de email ausente' }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    }

    const supabaseAdmin = createClient(
      SUPABASE_URL,
      SUPABASE_SERVICE_ROLE_KEY,
      { auth: { autoRefreshToken: false, persistSession: false } }
    )

    const { data: userData, error: userError } = await supabaseAdmin.auth.admin.getUserByEmail(email)
    if (userError || !userData?.user) {
      return new Response(JSON.stringify({ error: 'Usuário não encontrado' }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    const userId = userData.user.id
    const token = generateToken()
    const expiresAt = new Date(Date.now() + 60 * 60 * 1000).toISOString()

    const { error: insertError } = await supabaseAdmin
      .from('password_reset_tokens')
      .insert({ user_id: userId, token, expires_at: expiresAt })

    if (insertError) {
      return new Response(JSON.stringify({ error: insertError.message }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    const SITE_URL = Deno.env.get('SITE_URL') ?? 'http://localhost:3000'
    const resetUrl = `${SITE_URL}/update-password?token=${token}`

    const emailBody = `
      <h2>Redefinição de senha</h2>
      <p>Para redefinir sua senha, clique no link abaixo:</p>
      <p><a href="${resetUrl}">Redefinir Senha</a></p>
      <p>Se você não solicitou, ignore este email.</p>
    `

    let response
    try {
      response = await fetch(BREVO_API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'api-key': BREVO_API_KEY,
        },
        body: JSON.stringify({
          sender: { name: SENDER_NAME, email: SENDER_EMAIL },
          to: [{ email }],
          subject: 'Redefinição de Senha',
          htmlContent: emailBody,
        }),
      })
    } catch (err) {
      console.log('Fetch error', err)
      return new Response(JSON.stringify({ error: 'Erro ao enviar email' }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    if (!response.ok) {
      const data = await response.text()
      console.log('Brevo error', data)
      return new Response(JSON.stringify({ error: 'Erro ao enviar email' }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    return new Response(JSON.stringify({ success: true }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })
  }
})