import { create } from "zustand";
import { Player } from "../api/api";
import { getPlayers, createPlayer, updatePlayer, deletePlayer } from "../api/api";

interface PlayersState {
  players: Player[];
  loading: boolean;
  error: string | null;
  fetchPlayers: (clubId: number) => Promise<void>;
  addPlayer: (clubId: number, player: Omit<Player, "id">, userId: string) => Promise<void>;
  updatePlayer: (clubId: number, id: string, player: Partial<Player>, userId: string) => Promise<void>;
  deletePlayer: (clubId: number, id: string, userId: string) => Promise<void>;
}

export const usePlayersStore = create<PlayersState>((set) => ({
  players: [],
  loading: false,
  error: null,

  fetchPlayers: async (clubId: number, includeInactive: boolean = true): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const players = await getPlayers(clubId, undefined, { includeInactive });
      set({ players, loading: false });
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao buscar jogadores", loading: false });
    } finally {
      set((state) => ({ ...state, loading: false }));
    }
  },

  addPlayer: async (clubId: number, player: Omit<Player, "id">, userId: string): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const newPlayer = await createPlayer(clubId, player, userId);
      set((state) => ({ players: [...state.players, newPlayer], loading: false }));
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao adicionar jogador", loading: false });
    }
  },

  updatePlayer: async (clubId: number, id: string, player: Partial<Player>, userId: string): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const updated = await updatePlayer(clubId, id, player, userId);
      if (updated) {
        set((state) => ({ players: state.players.map(p => p.id === id ? updated : p), loading: false }));
      } else {
        set({ error: "Jogador não encontrado", loading: false });
      }
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao atualizar jogador", loading: false });
    }
  },

  deletePlayer: async (clubId: number, id: string, userId: string): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const ok = await deletePlayer(clubId, id, userId);
      if (ok) {
        set((state) => ({ players: state.players.filter(p => p.id !== id), loading: false }));
      } else {
        set({ error: "Jogador não encontrado", loading: false });
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : "Erro ao deletar jogador";
      console.error("Erro no store ao deletar jogador:", err);
      set({ error: errorMessage, loading: false });
      throw err; // Re-throw para que o componente possa tratar o erro
    }
  },
}));
