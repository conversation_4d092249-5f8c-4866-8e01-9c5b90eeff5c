CREATE TABLE IF NOT EXISTS meal_locations (
    id SERIAL PRIMARY KEY,
    club_id INTEGER NOT NULL REFERENCES club_info(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    address TEXT,
    number VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

ALTER TABLE meal_sessions
  ADD COLUMN IF NOT EXISTS location_id INTEGER REFERENCES meal_locations(id);

CREATE INDEX IF NOT EXISTS idx_meal_locations_club_id ON meal_locations(club_id);
