import { useState, useEffect } from "react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Calendar } from "@/components/ui/calendar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CalendarIcon, Clock, Trash2 } from "lucide-react";
import { useAgendaStore } from "@/store/useAgendaStore";
import type { AgendaEvent } from "@/api/api";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

// Função para combinar data e hora
function combineDateAndTime(date: Date, time: string): string {
  const [hours, minutes] = time.split(':').map(Number);
  const combined = new Date(date);
  combined.setHours(hours, minutes, 0, 0);
  return combined.toISOString();
}

// Função para gerar opções de horário
function generateTimeOptions() {
  const options = [];
  for (let hour = 0; hour < 24; hour++) {
    for (let minute = 0; minute < 60; minute += 30) {
      const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
      options.push(timeString);
    }
  }
  return options;
}

// Função para extrair hora de uma string ISO
function extractTimeFromISO(isoString: string): string {
  const date = new Date(isoString);
  return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
}

interface AgendaEventEditDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: number;
  event: AgendaEvent;
}

export function AgendaEventEditDialog({ open, onOpenChange, clubId, event }: AgendaEventEditDialogProps) {
  const [date, setDate] = useState<Date | undefined>(new Date());
  const [startTime, setStartTime] = useState("09:00");
  const [endTime, setEndTime] = useState("10:30");
  const [title, setTitle] = useState("");
  const [location, setLocation] = useState("");
  const [description, setDescription] = useState("");
  const [participants, setParticipants] = useState("");
  const [eventType, setEventType] = useState("outro");
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [error, setError] = useState({
    title: "",
    date: "",
    startTime: "",
    endTime: "",
  });

  const { updateAgendaEvent, deleteAgendaEvent, loading } = useAgendaStore();

  // Preencher campos quando o evento for carregado
  useEffect(() => {
    if (event && open) {
      setTitle(event.title);
      setLocation(event.location || "");
      setDescription(event.description || "");
      setParticipants(event.participants?.join(", ") || "");
      setEventType(event.type || "outro");
      
      // Configurar data e horários
      const eventDate = new Date(event.date);
      setDate(eventDate);
      setStartTime(extractTimeFromISO(event.date));
      setEndTime(extractTimeFromISO(event.endTime));
    }
  }, [event, open]);

  // Limpar erros quando o dialog fechar
  useEffect(() => {
    if (!open) {
      setError({ title: "", date: "", startTime: "", endTime: "" });
    }
  }, [open]);

  const timeOptions = generateTimeOptions();

  const handleSave = async () => {
    // Validações
    if (!title.trim()) {
      setError({ ...error, title: "Título é obrigatório" });
      return;
    }
    if (!date) {
      setError({ ...error, date: "Data é obrigatória" });
      return;
    }
    if (!startTime) {
      setError({ ...error, startTime: "Início é obrigatório" });
      return;
    }
    if (!endTime) {
      setError({ ...error, endTime: "Término é obrigatório" });
      return;
    }

    try {
      await updateAgendaEvent(clubId, event.id, {
        title,
        date: combineDateAndTime(date, startTime),
        time: startTime,
        endTime: combineDateAndTime(date, endTime),
        type: eventType,
        location,
        description,
        participants: participants.split(",").map(p => p.trim()).filter(Boolean),
      });
      onOpenChange(false);
    } catch (err) {
      console.error("Erro ao atualizar evento:", err);
    }
  };

  const handleDelete = async () => {
    try {
      await deleteAgendaEvent(clubId, event.id);
      setShowDeleteDialog(false);
      onOpenChange(false);
    } catch (err) {
      console.error("Erro ao excluir evento:", err);
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Editar evento</DialogTitle>
            <DialogDescription>
              Modifique os dados do evento na agenda
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 py-4">
            <div className="space-y-1.5">
              <Label htmlFor="title">Título do evento</Label>
              <Input 
                id="title" 
                value={title} 
                onChange={(e) => setTitle(e.target.value)} 
                placeholder="Título do evento" 
              />
              {error.title && <div className="text-red-500 text-sm">{error.title}</div>}
            </div>
            
            <div className="space-y-1.5">
              <Label>Tipo de evento</Label>
              <Select value={eventType} onValueChange={setEventType}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o tipo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Tipos de evento</SelectLabel>
                    <SelectItem value="treino">Treino</SelectItem>
                    <SelectItem value="jogo">Jogo</SelectItem>
                    <SelectItem value="reuniao">Reunião</SelectItem>
                    <SelectItem value="evento">Evento</SelectItem>
                    <SelectItem value="outro">Outro</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-1.5">
              <Label>Data</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {date ? format(date, "dd/MM/yyyy", { locale: ptBR }) : <span>Selecione a data</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={date}
                    onSelect={setDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              {error.date && <div className="text-red-500 text-sm">{error.date}</div>}
            </div>
            
            <div className="space-y-1.5">
              <Label htmlFor="location">Local</Label>
              <Input 
                id="location" 
                value={location} 
                onChange={(e) => setLocation(e.target.value)} 
                placeholder="Local do evento" 
              />
            </div>
            
            <div className="space-y-1.5">
              <Label>Início</Label>
              <Select value={startTime} onValueChange={setStartTime}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o horário" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Horário de início</SelectLabel>
                    {timeOptions.map((time) => (
                      <SelectItem key={time} value={time}>
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4" />
                          {time}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
              {error.startTime && <div className="text-red-500 text-sm">{error.startTime}</div>}
            </div>
            
            <div className="space-y-1.5">
              <Label>Término</Label>
              <Select value={endTime} onValueChange={setEndTime}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o horário" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Horário de término</SelectLabel>
                    {timeOptions.map((time) => (
                      <SelectItem key={time} value={time}>
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4" />
                          {time}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
              {error.endTime && <div className="text-red-500 text-sm">{error.endTime}</div>}
            </div>
            
            <div className="space-y-1.5">
              <Label htmlFor="participants">Participantes</Label>
              <Input 
                id="participants" 
                value={participants} 
                onChange={(e) => setParticipants(e.target.value)} 
                placeholder="Participantes (separados por vírgula)" 
              />
            </div>
            
            <div className="space-y-1.5 sm:col-span-2">
              <Label htmlFor="description">Descrição</Label>
              <Textarea 
                id="description" 
                value={description} 
                onChange={(e) => setDescription(e.target.value)} 
                placeholder="Descreva os detalhes do evento" 
                className="min-h-24 resize-none" 
              />
            </div>
          </div>
          
          <DialogFooter className="flex justify-between">
            <Button 
              variant="destructive" 
              onClick={() => setShowDeleteDialog(true)}
              disabled={loading}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Excluir
            </Button>
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => onOpenChange(false)}>
                Cancelar
              </Button>
              <Button type="submit" onClick={handleSave} disabled={loading}>
                Salvar alterações
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog de confirmação de exclusão */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirmar exclusão</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir o evento "{event?.title}"? Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Excluir evento
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
