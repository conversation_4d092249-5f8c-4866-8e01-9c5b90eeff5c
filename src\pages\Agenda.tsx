import { useState, useEffect } from "react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Calendar } from "@/components/ui/calendar";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { PermissionControl } from "@/components/PermissionControl";
import { AGENDA_PERMISSIONS } from "@/constants/permissions";
import { CalendarDays, List, Plus, Users } from "lucide-react";
import { AgendaEventList } from "@/components/agenda/AgendaEventList";
import { AgendaEventDialog } from "@/components/agenda/AgendaEventDialog";
import { AgendaEventEditDialog } from "@/components/agenda/AgendaEventEditDialog";
import { AgendaDayView } from "@/components/agenda/AgendaDayView";
import { useAgendaStore } from "@/store/useAgendaStore";
import { useCurrentClubId } from "@/context/ClubContext";
import type { AgendaEvent } from "@/api/api";

export default function Agenda() {
  const [date, setDate] = useState<Date>(new Date());
  const { agenda: events, loading, error, fetchAgenda, addAgendaEvent, updateAgendaEvent, deleteAgendaEvent } = useAgendaStore();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<AgendaEvent | null>(null);
  const [view, setView] = useState<"calendar" | "list">("calendar");

  const clubId = useCurrentClubId();

  useEffect(() => {
    fetchAgenda(clubId);
  }, [fetchAgenda, clubId]);

  // Função para editar evento
  const handleEditEvent = (event: typeof filteredEventsAsEvent[number]) => {
    try {
      // Converter o evento para o formato AgendaEvent com tratamento seguro de datas
      const agendaEvent: AgendaEvent = {
        id: event.id,
        club_id: event.club_id,
        title: event.title,
        type: event.type,
        date: event.date instanceof Date && !isNaN(event.date.getTime())
          ? event.date.toISOString()
          : new Date().toISOString(),
        time: event.time || "",
        endTime: event.endTime instanceof Date && !isNaN(event.endTime.getTime())
          ? event.endTime.toISOString()
          : new Date().toISOString(),
        location: event.location,
        participants: event.participants,
        description: event.description,
      };
      setSelectedEvent(agendaEvent);
      setIsEditDialogOpen(true);
    } catch (error) {
      console.error("Erro ao preparar evento para edição:", error);
      // Fallback: buscar o evento original do store
      const originalEvent = events.find(e => e.id === event.id);
      if (originalEvent) {
        setSelectedEvent(originalEvent);
        setIsEditDialogOpen(true);
      }
    }
  };
  
  // Filtrar eventos para a data selecionada
  const filteredEvents = events.filter(
    (event) => {
      try {
        const eventDate = typeof event.date === 'string' ? new Date(event.date) : event.date;
        // Verificar se a data é válida
        if (isNaN(eventDate.getTime())) {
          return false;
        }
        return (
          eventDate.getDate() === date.getDate() &&
          eventDate.getMonth() === date.getMonth() &&
          eventDate.getFullYear() === date.getFullYear()
        );
      } catch (error) {
        console.error("Erro ao filtrar evento:", error, event);
        return false;
      }
    }
  );
  
  // Transformar eventos para o formato exigido, sempre incluindo club_id
  const filteredEventsAsEvent = filteredEvents.map(event => {
    // Função auxiliar para converter data de forma segura
    const safeDate = (dateValue: string | Date): Date => {
      if (dateValue instanceof Date) {
        return isNaN(dateValue.getTime()) ? new Date() : dateValue;
      }
      const parsed = new Date(dateValue);
      return isNaN(parsed.getTime()) ? new Date() : parsed;
    };

    return {
      ...event,
      club_id: event.club_id, // Garante que club_id está presente
      date: safeDate(event.date),
      endTime: safeDate(event.endTime),
    };
  });
  
  // Função para determinar os dias que têm eventos
  const getEventDates = () => {
    return events.reduce((dates: Record<string, boolean>, event) => {
      try {
        const eventDate = typeof event.date === 'string' ? new Date(event.date) : event.date;
        // Verificar se a data é válida antes de formatar
        if (!isNaN(eventDate.getTime())) {
          const dateKey = format(eventDate, 'yyyy-MM-dd');
          dates[dateKey] = true;
        }
      } catch (error) {
        console.error("Erro ao processar data do evento:", error, event);
      }
      return dates;
    }, {});
  };
  
  const eventDates = getEventDates();
  
  // Formatar o mês para exibição
  const formattedMonth = format(date, 'MMMM yyyy', { locale: ptBR });

  return (
    <div className="container mx-auto p-4 space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <div>
          <h1 className="text-3xl font-bold text-team-blue">Agenda</h1>
          <p className="text-muted-foreground">Gerencie eventos, treinos e compromissos do time</p>
        </div>
        
        <div className="flex items-center gap-4">
          <Tabs value={view} onValueChange={(v) => setView(v as "calendar" | "list")} className="hidden sm:flex">
            <TabsList>
              <TabsTrigger value="calendar" className="flex items-center gap-1.5">
                <CalendarDays className="h-4 w-4" />
                <span className="hidden sm:inline-block">Calendário</span>
              </TabsTrigger>
              <TabsTrigger value="list" className="flex items-center gap-1.5">
                <List className="h-4 w-4" />
                <span className="hidden sm:inline-block">Lista</span>
              </TabsTrigger>
            </TabsList>
          </Tabs>
          
          <PermissionControl permission={AGENDA_PERMISSIONS.CREATE}>
            <Button onClick={() => setIsDialogOpen(true)} className="flex items-center gap-1.5">
              <Plus className="h-4 w-4" />
              <span className="hidden sm:inline-block">Novo Evento</span>
            </Button>
          </PermissionControl>
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="capitalize">{formattedMonth}</span>
            </CardTitle>
            <CardDescription>Selecione uma data para ver os eventos</CardDescription>
          </CardHeader>
          <CardContent>
            <Calendar
              mode="single"
              selected={date}
              onSelect={(newDate) => newDate && setDate(newDate)}
              className="border rounded-md p-3"
              modifiers={{
                eventDay: (day) => {
                  const dateKey = format(day, 'yyyy-MM-dd');
                  return eventDates[dateKey] || false;
                }
              }}
              modifiersClassNames={{
                eventDay: "relative after:absolute after:bottom-0 after:left-1/2 after:h-1 after:w-1 after:-translate-x-1/2 after:rounded-full after:bg-primary"
              }}
            />
          </CardContent>
        </Card>
        
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Eventos de {format(date, 'dd/MM/yyyy')}</span>
              <Badge variant="outline" className="font-normal">
                {filteredEvents.length} evento{filteredEvents.length !== 1 ? 's' : ''}
              </Badge>
            </CardTitle>
            <CardDescription>
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4 text-muted-foreground" />
                <span>Planeje compromissos para a equipe</span>
              </div>
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex flex-col items-center justify-center h-60 text-center">
                <div className="text-muted-foreground mb-2">
                  <CalendarDays className="h-12 w-12 mx-auto mb-2 opacity-50" />
                  <p>Carregando eventos...</p>
                </div>
              </div>
            ) : (
              <>
                {view === "calendar" ? (
                  <AgendaDayView events={filteredEventsAsEvent} onEditEvent={handleEditEvent} />
                ) : (
                  <ScrollArea className="h-[500px] pr-4">
                    <AgendaEventList events={filteredEventsAsEvent} />
                  </ScrollArea>
                )}
                
                {filteredEvents.length === 0 && (
                  <div className="flex flex-col items-center justify-center h-60 text-center">
                    <div className="text-muted-foreground mb-2">
                      <CalendarDays className="h-12 w-12 mx-auto mb-2 opacity-50" />
                      <p>Não há eventos programados para esta data</p>
                    </div>
                    <Button 
                      variant="outline" 
                      onClick={() => setIsDialogOpen(true)}
                      className="mt-4"
                    >
                      Adicionar evento
                    </Button>
                  </div>
                )}
              </>
            )}
          </CardContent>
        </Card>
      </div>
      
      <PermissionControl permission={AGENDA_PERMISSIONS.CREATE}>
        <AgendaEventDialog open={isDialogOpen} onOpenChange={setIsDialogOpen} clubId={clubId} />
      </PermissionControl>

      {/* Modal de edição */}
      {selectedEvent && (
        <AgendaEventEditDialog
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          clubId={clubId}
          event={selectedEvent}
        />
      )}
    </div>
  );
}
