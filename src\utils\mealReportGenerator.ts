import { jsPDF } from "jspdf";
import 'jspdf-autotable';
import { ClubInfo } from "@/api/api";
import autoTable from "jspdf-autotable";
import { getClubPrimaryColorRgb } from '@/utils/themeUtils';
import { MealSessionWithDetails, MealParticipantWithDetails } from "@/api/meals";

// Tipo para jsPDF com autoTable
type jsPDFWithAutoTable = jsPDF & {
  autoTable: typeof autoTable;
  lastAutoTable: {
    finalY: number;
  };
};

/**
 * Gera um relatório de alimentação por alojamento em PDF
 * @param mealSession Dados da sessão de refeição
 * @param participants Lista de participantes
 * @param clubInfo Informações do clube
 * @param filename Nome do arquivo PDF
 */
export async function generateMealReport(
  mealSession: MealSessionWithDetails,
  participants: MealParticipantWithDetails[],
  clubInfo: ClubInfo,
  filename: string = 'relatorio-alimentacao.pdf'
): Promise<Blob> {
  // Criar um novo documento PDF
  const doc = new jsPDF() as unknown as jsPDFWithAutoTable;

  // Adicionar título
  const title = 'Relatório de Alimentação por Alojamento';
  doc.setFontSize(18);
  doc.text(title, 14, 22);

  // Adicionar informações do clube
  doc.setFontSize(12);
  doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, 14, 30);

  if (clubInfo.address) {
    doc.text(`Endereço: ${clubInfo.address}`, 14, 36);
  }

  if (clubInfo.phone) {
    doc.text(`Telefone: ${clubInfo.phone}`, 14, 42);
  }

  // Tentar adicionar o logo do clube, se disponível
  if (clubInfo.logo_url) {
    try {
      const img = new Image();

      // Criar uma promessa para lidar com o carregamento da imagem
      const loadImage = new Promise<void>((resolve, reject) => {
        img.onload = () => {
          try {
            // Calcular proporção para manter as proporções
            const imgWidth = 30;
            const imgHeight = (img.height * imgWidth) / img.width;

            // Adicionar a imagem ao PDF (alinhada à direita)
            doc.addImage(img, 'PNG', 170, 15, imgWidth, imgHeight);
            resolve();
          } catch (error) {
            reject(error);
          }
        };
        img.onerror = reject;
      });

      // Iniciar o carregamento da imagem
      img.src = clubInfo.logo_url;

      // Aguardar o carregamento e processamento da imagem
      await loadImage;
    } catch (logoError) {
      console.error("Erro ao adicionar logo ao PDF:", logoError);
    }
  }

  // Adicionar data e informações da refeição
  const reportDate = new Date().toLocaleDateString('pt-BR');
  const mealDate = new Date(mealSession.date).toLocaleDateString('pt-BR');
  
  doc.setFontSize(10);
  doc.text(`Data do Relatório: ${reportDate}`, 170, 50, { align: 'right' });

  doc.setFontSize(12);
  let yPosition = 50;
  
  doc.text(`Tipo de Refeição: ${mealSession.meal_type_name || 'Não informado'}`, 14, yPosition);
  yPosition += 6;
  
  doc.text(`Data da Refeição: ${mealDate}`, 14, yPosition);
  yPosition += 6;
  
  if (mealSession.time) {
    doc.text(`Horário: ${mealSession.time}`, 14, yPosition);
    yPosition += 6;
  }
  
  doc.text(`Alojamento: ${mealSession.accommodation_name || 'Não informado'}`, 14, yPosition);
  yPosition += 6;
  
  if (mealSession.meal_types?.location) {
    doc.text(`Local: ${mealSession.meal_types.location}`, 14, yPosition);
    yPosition += 6;
  }
  
  if (mealSession.meal_types?.address) {
    doc.text(`Endereço: ${mealSession.meal_types.address}`, 14, yPosition);
    yPosition += 6;
  }

  if (mealSession.notes) {
    doc.text(`Observações: ${mealSession.notes}`, 14, yPosition);
    yPosition += 6;
  }

  yPosition += 10;

  // Separar participantes por tipo
  const players = participants.filter(p => p.participant_type === 'player');
  const collaborators = participants.filter(p => p.participant_type === 'collaborator');

  // Ordenar participantes alfabeticamente por nome
  const sortedPlayers = [...players].sort((a, b) => {
    const nameA = a.participant_name || '-';
    const nameB = b.participant_name || '-';
    return nameA.localeCompare(nameB, 'pt-BR', { sensitivity: 'base' });
  });

  const sortedCollaborators = [...collaborators].sort((a, b) => {
    const nameA = a.participant_name || '-';
    const nameB = b.participant_name || '-';
    return nameA.localeCompare(nameB, 'pt-BR', { sensitivity: 'base' });
  });

  // Preparar cabeçalhos da tabela
  const headers = ['Nome', 'Tipo', 'Assinatura'];

  // Preparar dados para a tabela
  const tableData: string[][] = [];

  // Adicionar jogadores ordenados
  sortedPlayers.forEach(player => {
    tableData.push([
      player.participant_name || '-',
      'Jogador',
      '' // Espaço para assinatura
    ]);
  });

  // Adicionar colaboradores ordenados
  sortedCollaborators.forEach(collaborator => {
    const role = collaborator.participant_role ? ` (${collaborator.participant_role})` : '';
    tableData.push([
      `${collaborator.participant_name || '-'}${role}`,
      'Colaborador',
      '' // Espaço para assinatura
    ]);
  });

  // Se não há participantes, adicionar uma linha informativa
  if (tableData.length === 0) {
    tableData.push(['Nenhum participante registrado', '', '']);
  }

  // Adicionar a tabela ao PDF
  autoTable(doc, {
    startY: yPosition,
    head: [headers],
    body: tableData,
    theme: 'grid',
    headStyles: { 
      fillColor: getClubPrimaryColorRgb(), 
      textColor: [255, 255, 255], 
      fontStyle: 'bold' 
    },
    columnStyles: {
      0: { cellWidth: 80 },
      1: { cellWidth: 40 },
      2: { cellWidth: 60 }
    },
    styles: {
      cellPadding: 5,
      minCellHeight: 15
    },
    didDrawPage: (data) => {
      // Adicionar cabeçalho em cada página
      if (data.pageNumber > 1) {
        doc.setFontSize(10);
        doc.text(title, 14, 10);
        doc.setFontSize(8);
        doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, 14, 15);
        doc.text(`Data: ${mealDate}`, 14, 20);
        doc.text(`Tipo de Refeição: ${mealSession.meal_type_name || 'Não informado'}`, 14, 25);
      }
    }
  });

  // Adicionar resumo
  const docWithTable = doc as jsPDFWithAutoTable;
  let finalY = docWithTable.lastAutoTable.finalY + 20;

  doc.setFontSize(12);
  doc.text("Resumo:", 14, finalY);
  finalY += 8;

  doc.setFontSize(10);
  doc.text(`Total de Participantes: ${participants.length}`, 14, finalY);
  finalY += 5;
  doc.text(`Jogadores: ${players.length}`, 14, finalY);
  finalY += 5;
  doc.text(`Colaboradores: ${collaborators.length}`, 14, finalY);
  finalY += 15;

  // Adicionar campo para assinatura do responsável
  doc.setFontSize(12);
  doc.text("Assinatura do Responsável:", 14, finalY);
  finalY += 5;

  // Desenhar linha para assinatura
  doc.setDrawColor(0);
  doc.line(14, finalY + 15, 100, finalY + 15);

  // Adicionar campo para data
  doc.text("Data:", 120, finalY);

  // Desenhar linha para data
  doc.line(120, finalY + 15, 180, finalY + 15);

  // Adicionar rodapé
  const pageCount = doc.internal.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(8);
    doc.text(
      `Relatório gerado em ${reportDate} - Página ${i} de ${pageCount}`,
      14,
      doc.internal.pageSize.height - 10
    );
  }

  // Retornar o PDF como um Blob
  return doc.output('blob');
}

/**
 * Gera um relatório consolidado de alimentação com múltiplas sessões
 * @param mealSessions Lista de sessões de refeição
 * @param clubInfo Informações do clube
 * @param filename Nome do arquivo PDF
 */
export async function generateConsolidatedMealReport(
  mealSessions: MealSessionWithDetails[],
  clubInfo: ClubInfo,
  filename: string = 'relatorio-alimentacao-consolidado.pdf'
): Promise<Blob> {
  // Criar um novo documento PDF
  const doc = new jsPDF() as unknown as jsPDFWithAutoTable;

  // Adicionar título
  const title = 'Relatório Consolidado de Alimentação';
  doc.setFontSize(18);
  doc.text(title, 14, 22);

  // Adicionar informações do clube
  doc.setFontSize(12);
  doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, 14, 30);

  if (clubInfo.address) {
    doc.text(`Endereço: ${clubInfo.address}`, 14, 36);
  }

  // Tentar adicionar o logo do clube, se disponível
  if (clubInfo.logo_url) {
    try {
      const img = new Image();
      const loadImage = new Promise<void>((resolve, reject) => {
        img.onload = () => {
          try {
            const imgWidth = 30;
            const imgHeight = (img.height * imgWidth) / img.width;
            doc.addImage(img, 'PNG', 170, 15, imgWidth, imgHeight);
            resolve();
          } catch (error) {
            reject(error);
          }
        };
        img.onerror = reject;
      });

      img.src = clubInfo.logo_url;
      await loadImage;
    } catch (logoError) {
      console.error("Erro ao adicionar logo ao PDF:", logoError);
    }
  }

  // Adicionar data do relatório
  const reportDate = new Date().toLocaleDateString('pt-BR');
  doc.setFontSize(10);
  doc.text(`Data do Relatório: ${reportDate}`, 170, 50, { align: 'right' });

  // Preparar dados para a tabela consolidada
  const headers = ['Data', 'Tipo de Refeição', 'Alojamento', 'Participantes'];
  const tableData = mealSessions.map(session => [
    new Date(session.date).toLocaleDateString('pt-BR'),
    session.meal_type_name || 'Não informado',
    session.accommodation_name || 'Não informado',
    session.participant_count?.toString() || '0'
  ]);

  // Adicionar a tabela ao PDF
  autoTable(doc, {
    startY: 60,
    head: [headers],
    body: tableData,
    theme: 'grid',
    headStyles: { 
      fillColor: getClubPrimaryColorRgb(), 
      textColor: [255, 255, 255], 
      fontStyle: 'bold' 
    },
    columnStyles: {
      0: { cellWidth: 30 },
      1: { cellWidth: 60 },
      2: { cellWidth: 60 },
      3: { cellWidth: 30, halign: 'center' }
    },
    styles: {
      cellPadding: 5,
      minCellHeight: 10
    }
  });

  // Adicionar resumo
  const docWithTable = doc as jsPDFWithAutoTable;
  let finalY = docWithTable.lastAutoTable.finalY + 20;

  doc.setFontSize(12);
  doc.text("Resumo Geral:", 14, finalY);
  finalY += 8;

  const totalSessions = mealSessions.length;
  const totalParticipants = mealSessions.reduce((sum, session) => sum + (session.participant_count || 0), 0);

  doc.setFontSize(10);
  doc.text(`Total de Sessões: ${totalSessions}`, 14, finalY);
  finalY += 5;
  doc.text(`Total de Participações: ${totalParticipants}`, 14, finalY);

  // Adicionar rodapé
  const pageCount = doc.internal.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(8);
    doc.text(
      `Relatório gerado em ${reportDate} - Página ${i} de ${pageCount}`,
      14,
      doc.internal.pageSize.height - 10
    );
  }

  return doc.output('blob');
}
