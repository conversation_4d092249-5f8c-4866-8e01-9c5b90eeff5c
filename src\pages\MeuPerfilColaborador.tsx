import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { useUser } from "@/context/UserContext";
import { useCurrentClubId } from "@/context/ClubContext";
import { Collaborator, getCollaboratorByUserId } from "@/api/api";
import { ColaboradorDocumentosDialog } from "@/components/administrativo/ColaboradorDocumentosDialog";

export default function MeuPerfilColaborador() {
  const { user } = useUser();
  const clubId = useCurrentClubId();
  const [collaborator, setCollaborator] = useState<Collaborator | null>(null);
  const [loading, setLoading] = useState(true);
  const [documentsOpen, setDocumentsOpen] = useState(false);

  useEffect(() => {
    const load = async () => {
     // Só realiza a busca se tivermos clubId e user.id disponíveis
     if (!user?.id || !clubId) return;      try {
        setLoading(true);
        const data = await getCollaboratorByUserId(clubId, user.id);
        setCollaborator(data);
      } catch (err) {
        console.error("Erro ao carregar colaborador:", err);
      } finally {
        setLoading(false);
      }
    };
    load();
  }, [clubId, user?.id]);

  if (loading) {
    return <div className="py-8 text-center">Carregando...</div>;
  }

  if (!collaborator) {
    return <div className="py-8 text-center">Colaborador não encontrado.</div>;
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Meu Perfil</h1>
        <p className="text-muted-foreground">Informações do seu cadastro</p>
      </div>
      <Separator />
      <Card>
        <CardHeader>
          <CardTitle>{collaborator.full_name}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <p><strong>Função:</strong> {collaborator.role}</p>
          {collaborator.email && <p><strong>Email:</strong> {collaborator.email}</p>}
          {collaborator.phone && <p><strong>Telefone:</strong> {collaborator.phone}</p>}
          <Button onClick={() => setDocumentsOpen(true)}>Enviar Documentos</Button>
        </CardContent>
      </Card>

      <ColaboradorDocumentosDialog
        open={documentsOpen}
        onOpenChange={setDocumentsOpen}
        clubId={clubId}
        collaborator={collaborator}
      />
    </div>
  );
}