import { supabase } from "@/integrations/supabase/client";

export interface BirthdayPerson {
  id: string | number;
  name: string;
  birthdate: string;
  type: "player" | "collaborator";
}

/**
 * Fetch players and collaborators with birthdays in the current month
 * @param clubId Club ID
 * @param categoryId Optional category ID to filter players
 */
export async function getMonthlyBirthdays(
  clubId: number,
  categoryId?: number
): Promise<BirthdayPerson[]> {
  try {
    const today = new Date();
    const currentMonth = today.getMonth() + 1;

    // Buscar jogadores
    let { data: playersData, error: playersError } = await supabase
      .from("players")
      .select("id, name, birthdate, status, player_categories(category_id)")
      .eq("club_id", clubId);

    if (playersError) {
      console.error("Error fetching players birthdays:", playersError);
      throw new Error(`Error fetching players birthdays: ${playersError.message}`);
    }

    let players =
      (playersData as any[])?.filter(
        (p) =>
          p.birthdate &&
          new Date(p.birthdate).getMonth() + 1 === currentMonth &&
          p.status !== "inativo"
      ) || [];

    if (categoryId && categoryId > 0) {
      players = players.filter((p) =>
        (p.player_categories || []).some(
          (pc: any) => pc.category_id === categoryId
        )
      );
    }

    const playerBirthdays: BirthdayPerson[] = players.map((p) => ({
      id: p.id,
      name: p.name,
      birthdate: p.birthdate,
      type: "player",
    }));

    // Buscar colaboradores
    const { data: collabData, error: collabError } = await supabase
      .from("collaborators")
      .select("id, full_name, birth_date")
      .eq("club_id", clubId);

    if (collabError) {
      console.error("Error fetching collaborators birthdays:", collabError);
      throw new Error(
        `Error fetching collaborators birthdays: ${collabError.message}`
      );
    }

    const collaboratorBirthdays: BirthdayPerson[] =
      (collabData as any[])?.filter(
        (c) =>
          c.birth_date &&
          new Date(c.birth_date).getMonth() + 1 === currentMonth
      ).map((c) => ({
        id: c.id,
        name: c.full_name,
        birthdate: c.birth_date,
        type: "collaborator",
      })) || [];

    const combined = [...playerBirthdays, ...collaboratorBirthdays];
    combined.sort(
      (a, b) =>
        new Date(a.birthdate).getDate() - new Date(b.birthdate).getDate()
    );

    return combined;
  } catch (error) {
    console.error("Error fetching monthly birthdays:", error);
    throw error;
  }
}