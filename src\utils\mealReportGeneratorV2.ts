import { jsPDF } from 'jspdf';
import autoTable from 'jspdf-autotable';
import { MealSessionWithDetails, MealParticipantWithDetails } from '@/api/meals';
import { getClubPrimaryColorRgb } from '@/utils/themeUtils';

interface SessionMealType {
  id: number;
  name?: string;
  location?: string;
  address?: string;
  start_time?: string | null;
  end_time?: string | null;
}

interface MealReportData {
  session: MealSessionWithDetails;
  participantsByType: Record<number, MealParticipantWithDetails[]>;
  mealTypes: SessionMealType[];
  clubInfo: {
    name: string;
    logo?: string;
  };
}

export async function generateMealReportV2(data: MealReportData): Promise<void> {
  const { session, participantsByType, mealTypes, clubInfo } = data;

  const doc = new jsPDF();
  const pageWidth = doc.internal.pageSize.getWidth();
  const margin = 20;
  let yPosition = 20;

  // Cabeçalho
  doc.setFontSize(18);
  doc.setFont('helvetica', 'bold');
  doc.text(clubInfo.name, margin, yPosition);

  if (clubInfo.logo) {
    try {
      const img = new Image();
      const loadImage = new Promise<void>((resolve, reject) => {
        img.onload = () => {
          try {
            const imgWidth = 30;
            const imgHeight = (img.height * imgWidth) / img.width;
            doc.addImage(img, 'PNG', pageWidth - margin - imgWidth, yPosition - 5, imgWidth, imgHeight);
            resolve();
          } catch (error) {
            reject(error);
          }
        };
        img.onerror = reject;
      });
      img.src = clubInfo.logo;
      await loadImage;
    } catch (logoError) {
      console.error('Erro ao adicionar logo ao PDF:', logoError);
    }
  }
  yPosition += 10;
  doc.setFontSize(14);
  doc.text('Relatório de Alimentação', 20, yPosition);

  yPosition += 15;
  doc.setFontSize(12);
  doc.setFont('helvetica', 'normal');

  // Informações da sessão
  doc.text(`Data: ${new Date(session.date).toLocaleDateString('pt-BR')}`, 20, yPosition);
  yPosition += 7;
  doc.text(`Horário: ${session.time || 'Não especificado'}`, 20, yPosition);
  yPosition += 15;

  // Para cada tipo de refeição, criar uma tabela separada
  for (const mealType of mealTypes) {
    const participants = participantsByType[mealType.id] || [];

    if (participants.length === 0) continue;

    // Verificar se há espaço suficiente na página
    if (yPosition > 250) {
      doc.addPage();
      yPosition = 20;
    }

    // Título do tipo de refeição
    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.text(`${mealType.name}`, 20, yPosition);
    yPosition += 5;

    // Informações do tipo de refeição
    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    if (mealType.location) {
      doc.text(`Local: ${mealType.location}`, 20, yPosition);
      yPosition += 5;
    }
    if (mealType.address) {
      doc.text(`Endereço: ${mealType.address}`, 20, yPosition);
      yPosition += 5;
    }

    yPosition += 5;

    // Ordenar participantes alfabeticamente por nome
    const sortedParticipants = [...participants].sort((a, b) => {
      const nameA = a.participant_name || 'Nome não disponível';
      const nameB = b.participant_name || 'Nome não disponível';
      return nameA.localeCompare(nameB, 'pt-BR', { sensitivity: 'base' });
    });

    // Preparar dados da tabela
    const tableData = sortedParticipants.map((participant, index) => [
      (index + 1).toString(),
      participant.participant_name || 'Nome não disponível',
      participant.participant_type === 'player' ? 'Jogador' : 'Colaborador',
      participant.participant_nickname || participant.participant_role || '-',
      '' // Coluna para assinatura
    ]);

    // Criar tabela
    autoTable(doc, {
      startY: yPosition,
      head: [['#', 'Nome', 'Tipo', 'Apelido', 'Assinatura']],
      body: tableData,
      theme: 'grid',
      styles: {
        fontSize: 9,
        cellPadding: 2,
      },
      headStyles: {
        fillColor: getClubPrimaryColorRgb(),
        textColor: 255,
        fontStyle: 'bold',
      },
      columnStyles: {
        0: { halign: 'center', cellWidth: 15 },
        1: { cellWidth: 60 },
        2: { halign: 'center', cellWidth: 25 },
        3: { cellWidth: 25 },
        4: { cellWidth: 50 },
      },
      margin: { left: 20, right: 20 },
    });

    // Atualizar posição Y após a tabela
    yPosition = (doc as any).lastAutoTable.finalY + 15;

    // Adicionar resumo do tipo de refeição
    doc.setFontSize(10);
    doc.setFont('helvetica', 'bold');
    doc.text(`Total de participantes: ${participants.length}`, 20, yPosition);

    const playersCount = participants.filter(p => p.participant_type === 'player').length;
    const collaboratorsCount = participants.filter(p => p.participant_type === 'collaborator').length;

    yPosition += 5;
    doc.setFont('helvetica', 'normal');
    doc.text(`Jogadores: ${playersCount} | Colaboradores: ${collaboratorsCount}`, 20, yPosition);
    yPosition += 15;
  }

  // Resumo geral
  if (yPosition > 250) {
    doc.addPage();
    yPosition = 20;
  }

  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('Resumo Geral', 20, yPosition);
  yPosition += 10;

  doc.setFontSize(10);
  doc.setFont('helvetica', 'normal');

  const totalParticipants = Object.values(participantsByType).flat().length;
  const totalPlayers = Object.values(participantsByType).flat().filter(p => p.participant_type === 'player').length;
  const totalCollaborators = Object.values(participantsByType).flat().filter(p => p.participant_type === 'collaborator').length;

  doc.text(`Total geral de participantes: ${totalParticipants}`, 20, yPosition);
  yPosition += 5;
  doc.text(`Total de jogadores: ${totalPlayers}`, 20, yPosition);
  yPosition += 5;
  doc.text(`Total de colaboradores: ${totalCollaborators}`, 20, yPosition);
  yPosition += 5;
  doc.text(`Tipos de refeição: ${mealTypes.length}`, 20, yPosition);

  // Rodapé
  const pageCount = doc.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(8);
    doc.setFont('helvetica', 'normal');
    doc.text(
      `Página ${i} de ${pageCount} - Gerado em ${new Date().toLocaleString('pt-BR')}`,
      20,
      doc.internal.pageSize.height - 10
    );
  }

  // Salvar o PDF
  const timeForFileName = session.time ? session.time.replace(':', '') : 'sem_horario';
  const fileName = `relatorio_alimentacao_${session.date}_${timeForFileName}.pdf`;
  doc.save(fileName);
}

// Função auxiliar para gerar relatório de um tipo específico de refeição
export async function generateMealTypeReport(
  session: MealSessionWithDetails,
  mealType: SessionMealType,
  participants: MealParticipantWithDetails[],
  clubInfo: { name: string; logo?: string }
): Promise<void> {
  const doc = new jsPDF();
  let yPosition = 20;
  const pageWidth = doc.internal.pageSize.getWidth();
  const margin = 20;

  // Cabeçalho
  doc.setFontSize(18);
  doc.setFont('helvetica', 'bold');
  doc.text(clubInfo.name, margin, yPosition);

  if (clubInfo.logo) {
    try {
      const img = new Image();
      const loadImage = new Promise<void>((resolve, reject) => {
        img.onload = () => {
          try {
            const imgWidth = 30;
            const imgHeight = (img.height * imgWidth) / img.width;
            doc.addImage(img, 'PNG', pageWidth - margin - imgWidth, yPosition - 5, imgWidth, imgHeight);
            resolve();
          } catch (error) {
            reject(error);
          }
        };
        img.onerror = reject;
      });
      img.src = clubInfo.logo;
      await loadImage;
    } catch (logoError) {
      console.error('Erro ao adicionar logo ao PDF:', logoError);
    }
  }

  yPosition += 10;
  doc.setFontSize(14);
  doc.text(`Relatório de Alimentação - ${mealType.name}`, 20, yPosition);

  yPosition += 15;
  doc.setFontSize(12);
  doc.setFont('helvetica', 'normal');

  // Informações da sessão
  doc.text(`Data: ${new Date(session.date).toLocaleDateString('pt-BR')}`, 20, yPosition);
  yPosition += 7;
  doc.text(`Horário: ${session.time || 'Não especificado'}`, 20, yPosition);
  yPosition += 7;
  if (mealType.location) {
    doc.text(`Local: ${mealType.location}`, 20, yPosition);
    yPosition += 7;
  }
  if (mealType.address) {
    doc.text(`Endereço: ${mealType.address}`, 20, yPosition);
    yPosition += 7;
  }
  if (mealType.start_time && mealType.end_time) {
    doc.text(`Horário: ${mealType.start_time} - ${mealType.end_time}`, 20, yPosition);
    yPosition += 7;
  }
  yPosition += 10;

  // Ordenar participantes alfabeticamente por nome
  const sortedParticipants = [...participants].sort((a, b) => {
    const nameA = a.participant_name || 'Nome não disponível';
    const nameB = b.participant_name || 'Nome não disponível';
    return nameA.localeCompare(nameB, 'pt-BR', { sensitivity: 'base' });
  });

  // Preparar dados da tabela
  const tableData = sortedParticipants.map((participant, index) => [
    (index + 1).toString(),
    participant.participant_name || 'Nome não disponível',
    participant.participant_type === 'player' ? 'Jogador' : 'Colaborador',
    participant.participant_nickname || participant.participant_role || '-',
    '' // Coluna para assinatura
  ]);

  // Criar tabela
  autoTable(doc, {
    startY: yPosition,
    head: [['#', 'Nome', 'Tipo', 'Apelido/Função', 'Assinatura']],
    body: tableData,
    theme: 'grid',
    styles: {
      fontSize: 10,
      cellPadding: 4,
    },
    headStyles: {
      fillColor: getClubPrimaryColorRgb(),
      textColor: 255,
      fontStyle: 'bold',
    },
    columnStyles: {
      0: { halign: 'center', cellWidth: 15 },
      1: { cellWidth: 70 },
      2: { halign: 'center', cellWidth: 30 },
      3: { cellWidth: 35 },
      4: { cellWidth: 35 },
    },
    margin: { left: 20, right: 20 },
  });

  // Resumo
  yPosition = (doc as any).lastAutoTable.finalY + 15;
  doc.setFontSize(12);
  doc.setFont('helvetica', 'bold');
  doc.text('Resumo', 20, yPosition);

  yPosition += 10;
  doc.setFontSize(10);
  doc.setFont('helvetica', 'normal');

  const playersCount = participants.filter(p => p.participant_type === 'player').length;
  const collaboratorsCount = participants.filter(p => p.participant_type === 'collaborator').length;

  doc.text(`Total de participantes: ${participants.length}`, 20, yPosition);
  yPosition += 5;
  doc.text(`Jogadores: ${playersCount}`, 20, yPosition);
  yPosition += 5;
  doc.text(`Colaboradores: ${collaboratorsCount}`, 20, yPosition);

  // Rodapé
  doc.setFontSize(8);
  doc.setFont('helvetica', 'normal');
  doc.text(
    `Gerado em ${new Date().toLocaleString('pt-BR')}`,
    20,
    doc.internal.pageSize.height - 10
  );

  // Salvar o PDF
  const timeForFileName = session.time ? session.time.replace(':', '') : 'sem_horario';
  const fileName = `relatorio_${mealType.name.toLowerCase().replace(/\s+/g, '_')}_${session.date}_${timeForFileName}.pdf`;
  doc.save(fileName);
}
