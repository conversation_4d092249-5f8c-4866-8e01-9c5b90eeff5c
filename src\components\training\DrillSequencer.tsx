import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Play,
  Pause,
  Square,
  SkipBack,
  SkipForward,
  Plus,
  Trash2,
  Edit,
  Clock,
  Target,
  Users,
  Settings,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { TrainingDrill, DrillStep } from './InteractiveTrainingBuilder';

interface DrillSequencerProps {
  drill: TrainingDrill | null;
  currentStepIndex: number;
  onStepChange: (index: number) => void;
  onDrillUpdate: (drill: TrainingDrill) => void;
  isPlaying: boolean;
  onPlayStateChange: (playing: boolean) => void;
  playbackSpeed: number;
  onSpeedChange: (speed: number) => void;
}

export function DrillSequencer({
  drill,
  currentStepIndex,
  onStepChange,
  onDrillUpdate,
  isPlaying,
  onPlayStateChange,
  playbackSpeed,
  onSpeedChange
}: DrillSequencerProps) {
  const [editingStep, setEditingStep] = useState<string | null>(null);
  const [stepForm, setStepForm] = useState({
    name: '',
    description: '',
    duration: 300
  });
  const [playbackTime, setPlaybackTime] = useState(0);

  const currentStep = drill?.steps[currentStepIndex];

  // Timer para playback
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (isPlaying && currentStep) {
      interval = setInterval(() => {
        setPlaybackTime(prev => {
          const newTime = prev + (1000 / playbackSpeed);
          if (newTime >= currentStep.duration * 1000) {
            // Avançar para próximo step
            if (currentStepIndex < (drill?.steps.length || 0) - 1) {
              onStepChange(currentStepIndex + 1);
              return 0;
            } else {
              onPlayStateChange(false);
              return 0;
            }
          }
          return newTime;
        });
      }, 1000 / playbackSpeed);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isPlaying, currentStep, currentStepIndex, drill?.steps.length, onStepChange, onPlayStateChange, playbackSpeed]);

  const handleAddStep = () => {
    if (!drill) return;

    const newStep: DrillStep = {
      id: `step_${Date.now()}`,
      name: `Passo ${drill.steps.length + 1}`,
      description: '',
      duration: 300,
      elements: [],
      annotations: [],
      drawings: []
    };

    const updatedDrill = {
      ...drill,
      steps: [...drill.steps, newStep],
      totalDuration: drill.totalDuration + 300
    };

    onDrillUpdate(updatedDrill);
  };

  const handleDeleteStep = (stepId: string) => {
    if (!drill || drill.steps.length <= 1) return;

    const stepIndex = drill.steps.findIndex(s => s.id === stepId);
    const stepToDelete = drill.steps[stepIndex];
    
    const updatedDrill = {
      ...drill,
      steps: drill.steps.filter(s => s.id !== stepId),
      totalDuration: drill.totalDuration - stepToDelete.duration
    };

    onDrillUpdate(updatedDrill);
    
    // Ajustar índice atual se necessário
    if (currentStepIndex >= stepIndex && currentStepIndex > 0) {
      onStepChange(currentStepIndex - 1);
    }
  };

  const handleUpdateStep = (stepId: string, updates: Partial<DrillStep>) => {
    if (!drill) return;

    const updatedSteps = drill.steps.map(step => 
      step.id === stepId ? { ...step, ...updates } : step
    );

    const updatedDrill = {
      ...drill,
      steps: updatedSteps,
      totalDuration: updatedSteps.reduce((total, step) => total + step.duration, 0)
    };

    onDrillUpdate(updatedDrill);
  };

  const handleEditStep = (step: DrillStep) => {
    setEditingStep(step.id);
    setStepForm({
      name: step.name,
      description: step.description,
      duration: step.duration
    });
  };

  const handleSaveStep = () => {
    if (!editingStep) return;

    handleUpdateStep(editingStep, stepForm);
    setEditingStep(null);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatDuration = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    return formatTime(seconds);
  };

  if (!drill) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <Target className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
          <p className="text-sm text-muted-foreground">
            Crie um novo drill para começar
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm flex items-center gap-2">
          <Play className="h-4 w-4" />
          Sequenciador de Drill
        </CardTitle>
        <CardDescription className="text-xs">
          Gerencie passos e controle a reprodução
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Informações do drill */}
        <div className="space-y-2">
          <div>
            <Label className="text-xs font-medium">Nome do Drill</Label>
            <Input
              value={drill.name}
              onChange={(e) => onDrillUpdate({ ...drill, name: e.target.value })}
              className="text-xs"
            />
          </div>
          
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label className="text-xs font-medium">Categoria</Label>
              <Select
                value={drill.category}
                onValueChange={(value: any) => onDrillUpdate({ ...drill, category: value })}
              >
                <SelectTrigger className="text-xs">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="tactical">Tático</SelectItem>
                  <SelectItem value="technical">Técnico</SelectItem>
                  <SelectItem value="physical">Físico</SelectItem>
                  <SelectItem value="transition">Transição</SelectItem>
                  <SelectItem value="finishing">Finalização</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label className="text-xs font-medium">Dificuldade</Label>
              <Select
                value={drill.difficulty}
                onValueChange={(value: any) => onDrillUpdate({ ...drill, difficulty: value })}
              >
                <SelectTrigger className="text-xs">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="beginner">Iniciante</SelectItem>
                  <SelectItem value="intermediate">Intermediário</SelectItem>
                  <SelectItem value="advanced">Avançado</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <Separator />

        {/* Controles de reprodução */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label className="text-xs font-medium">Reprodução</Label>
            <div className="flex items-center gap-1">
              <span className="text-xs text-muted-foreground">Velocidade:</span>
              <Select
                value={playbackSpeed.toString()}
                onValueChange={(value) => onSpeedChange(parseFloat(value))}
              >
                <SelectTrigger className="w-16 h-6 text-xs">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0.5">0.5x</SelectItem>
                  <SelectItem value="1">1x</SelectItem>
                  <SelectItem value="1.5">1.5x</SelectItem>
                  <SelectItem value="2">2x</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8"
              onClick={() => onStepChange(Math.max(0, currentStepIndex - 1))}
              disabled={currentStepIndex === 0}
            >
              <SkipBack className="h-4 w-4" />
            </Button>
            
            <Button
              variant={isPlaying ? "secondary" : "default"}
              size="icon"
              className="h-8 w-8"
              onClick={() => onPlayStateChange(!isPlaying)}
            >
              {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
            </Button>
            
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8"
              onClick={() => {
                onPlayStateChange(false);
                setPlaybackTime(0);
              }}
            >
              <Square className="h-4 w-4" />
            </Button>
            
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8"
              onClick={() => onStepChange(Math.min(drill.steps.length - 1, currentStepIndex + 1))}
              disabled={currentStepIndex === drill.steps.length - 1}
            >
              <SkipForward className="h-4 w-4" />
            </Button>
          </div>

          {/* Progress bar do step atual */}
          {currentStep && (
            <div className="space-y-1">
              <div className="flex justify-between text-xs">
                <span>{formatDuration(playbackTime)}</span>
                <span>{formatTime(currentStep.duration)}</span>
              </div>
              <Progress 
                value={(playbackTime / (currentStep.duration * 1000)) * 100} 
                className="h-2"
              />
            </div>
          )}
        </div>

        <Separator />

        {/* Lista de passos */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label className="text-xs font-medium">Passos do Drill</Label>
            <Button variant="outline" size="sm" onClick={handleAddStep}>
              <Plus className="h-3 w-3 mr-1" />
              Adicionar
            </Button>
          </div>

          <div className="space-y-2 max-h-48 overflow-y-auto">
            {drill.steps.map((step, index) => (
              <div
                key={step.id}
                className={`p-2 rounded border ${
                  index === currentStepIndex ? 'border-primary bg-primary/5' : 'border-muted'
                }`}
              >
                {editingStep === step.id ? (
                  <div className="space-y-2">
                    <Input
                      value={stepForm.name}
                      onChange={(e) => setStepForm({ ...stepForm, name: e.target.value })}
                      className="text-xs"
                      placeholder="Nome do passo"
                    />
                    <Textarea
                      value={stepForm.description}
                      onChange={(e) => setStepForm({ ...stepForm, description: e.target.value })}
                      className="text-xs"
                      placeholder="Descrição"
                      rows={2}
                    />
                    <div className="flex items-center gap-2">
                      <Input
                        type="number"
                        value={stepForm.duration}
                        onChange={(e) => setStepForm({ ...stepForm, duration: parseInt(e.target.value) || 300 })}
                        className="text-xs w-20"
                        min="1"
                      />
                      <span className="text-xs text-muted-foreground">segundos</span>
                    </div>
                    <div className="flex gap-1">
                      <Button variant="outline" size="sm" onClick={handleSaveStep}>
                        Salvar
                      </Button>
                      <Button variant="ghost" size="sm" onClick={() => setEditingStep(null)}>
                        Cancelar
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div>
                    <div className="flex items-center justify-between mb-1">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">
                          {index + 1}
                        </Badge>
                        <span className="text-xs font-medium">{step.name}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6"
                          onClick={() => onStepChange(index)}
                        >
                          <ChevronRight className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6"
                          onClick={() => handleEditStep(step)}
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6"
                          onClick={() => handleDeleteStep(step.id)}
                          disabled={drill.steps.length <= 1}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    
                    {step.description && (
                      <p className="text-xs text-muted-foreground mb-1">
                        {step.description}
                      </p>
                    )}
                    
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <Clock className="h-3 w-3" />
                      <span>{formatTime(step.duration)}</span>
                      <span>•</span>
                      <span>{step.elements.length} elementos</span>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Resumo do drill */}
        <div className="p-3 bg-muted/50 rounded-lg">
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              <span>Duração: {formatTime(drill.totalDuration)}</span>
            </div>
            <div className="flex items-center gap-1">
              <Users className="h-3 w-3" />
              <span>Jogadores: {drill.playersRequired}</span>
            </div>
            <div className="flex items-center gap-1">
              <Target className="h-3 w-3" />
              <span>Passos: {drill.steps.length}</span>
            </div>
            <div className="flex items-center gap-1">
              <Settings className="h-3 w-3" />
              <span>Equipamentos: {drill.equipmentNeeded.length}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
