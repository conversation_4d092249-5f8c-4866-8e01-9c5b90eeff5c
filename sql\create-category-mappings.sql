-- <PERSON><PERSON><PERSON> da tabela category_mappings para salvar mapeamentos de jogadores por categoria
-- Este arquivo deve ser executado manualmente no Supabase

-- 1. <PERSON>riar tabela category_mappings
CREATE TABLE IF NOT EXISTS category_mappings (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id) NOT NULL,
  category_id INTEGER REFERENCES categories(id) NOT NULL,
  name TEXT NOT NULL DEFAULT 'Mapeamento Principal',
  mapping JSONB NOT NULL DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(club_id, category_id, name)
);

-- 2. <PERSON><PERSON>r índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_category_mappings_club_category ON category_mappings(club_id, category_id);
CREATE INDEX IF NOT EXISTS idx_category_mappings_club_id ON category_mappings(club_id);

-- 3. <PERSON><PERSON><PERSON> trigger para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_category_mappings_timestamp()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = NOW();
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_category_mappings_timestamp
BEFORE UPDATE ON category_mappings
FOR EACH ROW
EXECUTE FUNCTION update_category_mappings_timestamp();

-- 4. Habilitar RLS (Row Level Security)
ALTER TABLE category_mappings ENABLE ROW LEVEL SECURITY;

-- 5. Criar políticas de segurança
CREATE POLICY "Club members can view their own category mappings"
  ON category_mappings
  FOR SELECT
  USING (club_id = (auth.jwt() ->> 'club_id')::INTEGER);

CREATE POLICY "Club members can insert their own category mappings"
  ON category_mappings
  FOR INSERT
  WITH CHECK (club_id = (auth.jwt() ->> 'club_id')::INTEGER);

CREATE POLICY "Club members can update their own category mappings"
  ON category_mappings
  FOR UPDATE
  USING (club_id = (auth.jwt() ->> 'club_id')::INTEGER)
  WITH CHECK (club_id = (auth.jwt() ->> 'club_id')::INTEGER);

CREATE POLICY "Club members can delete their own category mappings"
  ON category_mappings
  FOR DELETE
  USING (club_id = (auth.jwt() ->> 'club_id')::INTEGER);

-- 6. Verificações e correções no banco de dados existente

-- Verificar se há jogadores sem posição definida e corrigir
UPDATE players 
SET position = 'Não definido' 
WHERE position IS NULL OR position = '';

-- Verificar se há jogadores sem status definido e corrigir
UPDATE players 
SET status = 'ativo' 
WHERE status IS NULL OR status = '';

-- Verificar se há categorias sem tipo definido e corrigir
UPDATE categories 
SET type = 'age_group' 
WHERE type IS NULL OR type = '';

-- 7. Adicionar comentários para documentação
COMMENT ON TABLE category_mappings IS 'Tabela para armazenar mapeamentos de jogadores por categoria no campo de futebol';
COMMENT ON COLUMN category_mappings.mapping IS 'JSON contendo as posições dos jogadores no campo (formato: {"position_key": {"player_id": "uuid", "player_name": "nome", "player_number": 10}})';
COMMENT ON COLUMN category_mappings.name IS 'Nome do mapeamento (permite múltiplos mapeamentos por categoria)';

-- 8. Verificar integridade dos dados
-- Esta query pode ser executada para verificar problemas nos dados:
/*
SELECT 
  'players_without_club' as issue,
  COUNT(*) as count
FROM players 
WHERE club_id IS NULL

UNION ALL

SELECT 
  'players_without_position' as issue,
  COUNT(*) as count
FROM players 
WHERE position IS NULL OR position = ''

UNION ALL

SELECT 
  'players_without_status' as issue,
  COUNT(*) as count
FROM players 
WHERE status IS NULL OR status = ''

UNION ALL

SELECT 
  'categories_without_type' as issue,
  COUNT(*) as count
FROM categories 
WHERE type IS NULL OR type = '';
*/
