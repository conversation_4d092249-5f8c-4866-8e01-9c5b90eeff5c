import { supabase } from "@/integrations/supabase/client";
import { createUserDirectly } from "./directAuth";

/**
 * Interface para criação de conta de profissional médico
 */
export interface MedicalAccountOptions {
  email: string;
  password?: string;
  sendInvite?: boolean;
}

/**
 * Cria uma conta de usuário para um profissional médico
 * @param clubId ID do clube
 * @param professionalId ID do profissional médico
 * @param options Opções para criação da conta
 * @returns Objeto com sucesso e mensagem
 */
export async function createMedicalAccount(
  clubId: any,
  professionalId: any,
  options: MedicalAccountOptions
): Promise<{ success: boolean; message: string; professionalId?: any }> {
  try {
    // Verificar se temos as informações necessárias
    if (!options.email) {
      return { success: false, message: "É necessário fornecer um email" };
    }

    // Obter o profissional médico
    const { data: professional, error: professionalError } = await supabase
      .from("medical_professionals")
      .select("*")
      .eq("club_id", clubId)
      .eq("id", professionalId)
      .single();

    if (professionalError || !professional) {
      return {
        success: false,
        message: `Erro ao buscar profissional médico: ${professionalError?.message || "Profissional não encontrado"}`
      };
    }

    // Verificar se o profissional já tem uma conta
    if (professional.user_id) {
      return {
        success: false,
        message: "Este profissional médico já possui uma conta"
      };
    }

    // Obter informações do clube
    const { data: clubData } = await supabase
      .from("club_info")
      .select("name")
      .eq("id", clubId)
      .single();

    const clubName = clubData?.name || "Seu Clube";

    // Usar nossa nova função para criar usuário diretamente
    const result = await createUserDirectly(
      options.email,
      professional.name,
      clubName,
      "medical",
      clubId,
      options.password
    );

    if (!result.success) {
      return { success: false, message: result.message };
    }

    const userId = result.userId;

    if (!userId) {
      return { success: false, message: "Erro ao criar usuário: ID não retornado" };
    }

    // As permissões serão definidas na próxima etapa

    // Verificar se o usuário já é membro do clube
    try {
      const { data: existingMember, error: memberCheckError } = await supabase
        .from("club_members")
        .select("id")
        .eq("club_id", clubId)
        .eq("user_id", userId)
        .maybeSingle();

      if (memberCheckError) {
        console.error("Erro ao verificar associação do usuário ao clube:", memberCheckError);
      }

      // Definir as permissões médicas
      const medicalPermissions = {
        "medical.view": true,
        "medical.create": true,
        "medical.edit": true,
        "medical_professionals.view": true,
        "medical_professionals.edit_own": true,
        "medical_professionals.edit": true,
        "agenda.view": true,
        "agenda.create": true,
        "agenda.edit": true,
        "medical.availability.view": true,
        "medical.availability.create": true,
        "medical.availability.edit": true,
        "medical.availability.delete": true,
        // Adicionar permissões específicas de agendamentos médicos
        "medical.appointments.view": true,
        "medical.appointments.create": true,
        "medical.appointments.edit": true,
        "medical.appointments.delete": true
      };

      // Se o usuário ainda não é membro do clube, adicioná-lo com as permissões
      if (!existingMember) {
        await supabase
          .from("club_members")
          .insert({
            club_id: clubId,
            user_id: userId,
            role: "medical",
            status: "ativo",
            permissions: medicalPermissions
          });
      } else {
        // Se já é membro, atualizar as permissões
        await supabase
          .from("club_members")
          .update({
            permissions: medicalPermissions,
            role: "medical" // Garantir que o papel seja 'medical'
          })
          .eq("club_id", clubId)
          .eq("user_id", userId);
      }
    } catch (memberError) {
      console.warn("Erro ao adicionar usuário ao clube:", memberError);
    }

    // Vincular usuário ao profissional médico
    const { error: updateError } = await supabase
      .from("medical_professionals")
      .update({ user_id: userId })
      .eq("club_id", clubId)
      .eq("id", professionalId);

    if (updateError) {
      return {
        success: false,
        message: `Erro ao vincular profissional médico à conta: ${updateError.message}`
      };
    }

    return {
      success: true,
      message: "Conta criada com sucesso",
      professionalId
    };
  } catch (error: any) {
    console.error("Erro ao criar conta para profissional médico:", error);
    return {
      success: false,
      message: error.message || "Erro ao criar conta para profissional médico"
    };
  }
}
