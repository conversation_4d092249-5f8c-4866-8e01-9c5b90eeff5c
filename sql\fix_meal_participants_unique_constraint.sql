-- Remover o índice único existente
DROP INDEX IF EXISTS idx_meal_participants_unique;

-- Criar um novo índice único que inclui meal_type_id
CREATE UNIQUE INDEX idx_meal_participants_unique 
ON meal_participants(meal_session_id, participant_id, participant_type, meal_type_id);

-- Comentário explicativo
COMMENT ON INDEX idx_meal_participants_unique IS 'Garante que um participante não seja adicionado ao mesmo tipo de refeição mais de uma vez na mesma sessão, mas permite que participe de múltiplos tipos de refeição na mesma sessão.';
