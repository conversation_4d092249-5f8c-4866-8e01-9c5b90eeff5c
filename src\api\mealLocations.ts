import { supabase } from "@/integrations/supabase/client";

export interface MealLocation {
  id: number;
  club_id: number;
  name: string;
  address?: string | null;
  number?: string | null;
  created_at?: string | null;
}

export async function getMealLocations(clubId: number): Promise<MealLocation[]> {
  const { data, error } = await supabase
    .from("meal_locations")
    .select("*")
    .eq("club_id", clubId as any)
    .order("name");

  if (error) {
    console.error("Erro ao buscar locais de refeição:", error);
    throw new Error(`Erro ao buscar locais de refeição: ${error.message}`);
  }

  return (data || []) as MealLocation[];
}

export async function createMealLocation(
  clubId: number,
  location: Omit<MealLocation, "id" | "club_id" | "created_at">
): Promise<MealLocation> {
  const { data, error } = await supabase
    .from("meal_locations")
    .insert({ ...location, club_id: clubId } as any)
    .select()
    .single();

  if (error) {
    console.error("Erro ao criar local de refeição:", error);
    throw new Error(`Erro ao criar local de refeição: ${error.message}`);
  }

  return data as MealLocation;
}

export async function updateMealLocation(
  clubId: number,
  id: number,
  location: Partial<MealLocation>
): Promise<MealLocation> {
  const { data, error } = await supabase
    .from("meal_locations")
    .update(location as any)
    .eq("club_id", clubId as any)
    .eq("id", id as any)
    .select()
    .single();

  if (error) {
    console.error(`Erro ao atualizar local ${id}:`, error);
    throw new Error(`Erro ao atualizar local: ${error.message}`);
  }

  return data as MealLocation;
}

export async function deleteMealLocation(
  clubId: number,
  id: number
): Promise<boolean> {
  const { error } = await supabase
    .from("meal_locations")
    .delete()
    .eq("club_id", clubId as any)
    .eq("id", id as any);

  if (error) {
    console.error(`Erro ao excluir local ${id}:`, error);
    throw new Error(`Erro ao excluir local: ${error.message}`);
  }

  return true;
}

