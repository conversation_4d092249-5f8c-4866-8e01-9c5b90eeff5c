import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "@/components/ui/use-toast";
import { useAdministrativeDocumentsStore } from "@/store/useAdministrativeDocumentsStore";
import { useUser } from "@/context/UserContext";
import { AdministrativeDocument } from "@/api/api";
import { RichTextEditor } from "@/components/ui/rich-text-editor";

interface NovoDocumentoDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: number;
  initialData?: AdministrativeDocument | null;
}

export function NovoDocumentoDialog({
  open,
  onOpenChange,
  clubId,
  initialData
}: NovoDocumentoDialogProps) {
  const [title, setTitle] = useState(initialData?.title || "");
  const [content, setContent] = useState(initialData?.content || "");
  const [documentType, setDocumentType] = useState<string>(
    initialData?.document_type || ""
  );
  const [error, setError] = useState("");

  const { addDocument, loading } = useAdministrativeDocumentsStore();
  const { user } = useUser();

  const handleSave = async () => {
    if (!title.trim()) {
      setError("O título é obrigatório.");
      return;
    }

    if (!content.trim()) {
      setError("O conteúdo é obrigatório.");
      return;
    }

    if (!user) {
      setError("Usuário não autenticado.");
      return;
    }

    setError("");

    try {
      await addDocument(clubId, {
        title,
        content,
        document_type: documentType,
        created_by: user.id,
        digital_signature: false,
        club_id: clubId
      });

      toast({
        title: "Documento criado",
        description: "O documento foi criado com sucesso.",
      });

      setTitle("");
      setContent("");
      setDocumentType("");
      onOpenChange(false);
    } catch (err) {
      setError("Erro ao criar documento.");
      toast({
        title: "Erro ao criar documento",
        description: "Ocorreu um erro ao criar o documento.",
        variant: "destructive",
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Novo Documento</DialogTitle>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="document-type" className="text-right">
              Assunto
              </Label>
            <Input
              id="document-type"
              value={documentType}
              onChange={(e) => setDocumentType(e.target.value)}
              className="col-span-3"
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="title" className="text-right">
              Título
            </Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="col-span-3"
            />
          </div>

          <div className="grid grid-cols-4 items-start gap-4">
            <Label htmlFor="content" className="text-right pt-2">
              Conteúdo
            </Label>
            <div className="col-span-3">
              <RichTextEditor
                content={content}
                onChange={setContent}
              />
            </div>
          </div>

          {error && (
            <div className="text-red-500 text-sm col-span-4 text-center">
              {error}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleSave} disabled={loading}>
            {loading ? "Salvando..." : "Salvar"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
