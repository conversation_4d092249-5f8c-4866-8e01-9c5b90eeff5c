import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "@/components/ui/use-toast";
import {
  Loader2,
  Edit,
  ArrowLeft,
  Eye,
  Download
} from "lucide-react";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import {
  getCallupById,
  getCallupPlayers,
  addPlayerToCallup,
  removePlayerFromCallup,
  updateCallup,
  generateCallupPDF,
  generateCallupAccommodationPDF,
  Callup,
  CallupPlayer
} from "@/api/callups";
import { getCategories, getCategoryPlayers, Category } from "@/api/api";
import { getClubInfo, ClubInfo } from "@/api/api";
import { getClubUsers, ClubUser } from "@/api/users";
import { getCollaborators, Collaborator } from "@/api/collaborators";
import { CallupDesignEditor } from "@/components/callups/CallupDesignEditor";
import { CallupPreview } from "@/components/callups/CallupPreview";

// Componente principal da página de detalhes da convocação
export default function ConvocacaoDetalhesPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const clubId = useCurrentClubId();
  const { user } = useUser();

  const [callup, setCallup] = useState<Callup | null>(null);
  const [callupPlayers, setCallupPlayers] = useState<CallupPlayer[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [categoryPlayers, setCategoryPlayers] = useState<any[]>([]);
  const [clubUsers, setClubUsers] = useState<ClubUser[]>([]);
  const [collaborators, setCollaborators] = useState<Collaborator[]>([]);
  const [clubInfo, setClubInfo] = useState<ClubInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [selectedTab, setSelectedTab] = useState("design");
  const [generatingPDF, setGeneratingPDF] = useState(false);
  const [generatingAccommodationPDF, setGeneratingAccommodationPDF] = useState(false);
  const [selectedFields, setSelectedFields] = useState({
    players: true,
    schedule: true,
    hotel: true,
    notices: true,
    staff: true,
    technical: true,
    executive: true
  });

  // Carregar dados da convocação
  useEffect(() => {
    const fetchData = async () => {
      if (!id) return;

      try {
        setLoading(true);

        // Carregar detalhes da convocação
        const callupData = await getCallupById(clubId, parseInt(id));
        setCallup(callupData);

        // Carregar jogadores da convocação
        const playersData = await getCallupPlayers(clubId, parseInt(id));
        setCallupPlayers(playersData);

        // Carregar categorias
        const categoriesData = await getCategories(clubId);
        setCategories(categoriesData);

        // Carregar jogadores da categoria
        if (callupData.category_id) {
          try {
            const categoryPlayersData = await getCategoryPlayers(clubId, callupData.category_id);
            setCategoryPlayers(categoryPlayersData);
          } catch (error) {
            console.error("Erro ao buscar jogadores da categoria:", error);
            setCategoryPlayers([]);
          }
        }

        // Carregar usuários do clube (para comissão técnica)
        const usersData = await getClubUsers(clubId);
        setClubUsers(usersData);

        // Carregar colaboradores do clube (para comissão técnica)
        try {
          const collaboratorsData = await getCollaborators(clubId);
          setCollaborators(collaboratorsData);
        } catch (error) {
          console.error("Erro ao buscar colaboradores:", error);
          setCollaborators([]);
        }

        // Carregar informações do clube
        const clubInfoData = await getClubInfo(clubId);
        setClubInfo(clubInfoData);
      } catch (error) {
        console.error("Erro ao carregar dados:", error);
        toast({
          title: "Erro",
          description: "Não foi possível carregar os detalhes da convocação.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [id, clubId]);

  // Salvar alterações na convocação
  const handleSaveCallup = async () => {
    if (!callup || !id) {
      toast({
        title: "Erro",
        description: "Dados da convocação não encontrados.",
        variant: "destructive",
      });
      return;
    }

    try {
      setSaving(true);

      // Atualizar a convocação
      const updatedCallup = await updateCallup(clubId, parseInt(id), callup, user?.id);
      setCallup(updatedCallup);

      // Obter a lista atual de jogadores do banco de dados
      const currentPlayers = await getCallupPlayers(clubId, parseInt(id));

      // Encontrar jogadores que precisam ser removidos (estão no banco mas não na lista atual)
      const playersToRemove = currentPlayers.filter(
        dbPlayer => !callupPlayers.some(p => p.id === dbPlayer.id)
      );

      // Remover jogadores que não estão mais na lista
      for (const player of playersToRemove) {
        if (player.id) {
          try {
            await removePlayerFromCallup(clubId, parseInt(id), player.id, user?.id);
          } catch (error) {
            console.error(`Erro ao remover jogador ${player.id}:`, error);
          }
        }
      }

      // Atualizar/Adicionar apenas jogadores que não existem ainda
      const updatedPlayers: CallupPlayer[] = [];

      // Primeiro, mapear os jogadores atuais para verificação
      const existingPlayerMap = new Map();
      currentPlayers.forEach(player => {
        // Criar chaves únicas para diferentes tipos de identificação
        if (player.player_id) {
          existingPlayerMap.set(`player_${player.player_id}`, true);
        }
        if (player.user_id) {
          existingPlayerMap.set(`user_${player.user_id}`, true);
        }
        if (player.player_name && !player.player_id && !player.user_id) {
          existingPlayerMap.set(`name_${player.player_name.toLowerCase().trim()}`, true);
        }
      });

      for (const player of callupPlayers) {
        // Verificar se o jogador já existe antes de tentar adicionar
        let playerExists = false;

        if (player.player_id) {
          playerExists = existingPlayerMap.has(`player_${player.player_id}`);
        } else if (player.user_id) {
          playerExists = existingPlayerMap.has(`user_${player.user_id}`);
        } else if (player.player_name) {
          playerExists = existingPlayerMap.has(`name_${player.player_name.toLowerCase().trim()}`);
        }

        // Só adicionar se não existir
        if (!playerExists) {
          try {
            if (player.player_id) {
              // Jogador do elenco
              const newPlayer = await addPlayerToCallup(
                clubId,
                parseInt(id),
                player.player_id,
                player.role,
                user?.id,
                undefined,
                undefined,
                player.player_name
              );
              updatedPlayers.push(newPlayer);
            } else if (player.user_id) {
              // Membro da comissão (usuário ou colaborador)
              const newPlayer = await addPlayerToCallup(
                clubId,
                parseInt(id),
                undefined,
                player.role,
                user?.id,
                player.user_id,
                player.user_name
              );
              updatedPlayers.push(newPlayer);
            } else if (player.player_name) {
              // Jogador adicionado manualmente
              const newPlayer = await addPlayerToCallup(
                clubId,
                parseInt(id),
                undefined,
                player.role,
                user?.id,
                undefined,
                undefined,
                player.player_name
              );
              updatedPlayers.push(newPlayer);
            }
          } catch (playerError) {
            console.error(`Erro ao adicionar jogador ${player.player_name || player.user_name}:`, playerError);
            // Continuar com os próximos jogadores mesmo se um falhar
          }
        } else {
          console.log(`Jogador ${player.player_name || player.user_name} já existe na convocação, pulando...`);
        }
      }

     // Recarrega a lista para garantir que todos os membros apareçam corretamente
     const refreshedPlayers = await getCallupPlayers(clubId, parseInt(id));
     setCallupPlayers(refreshedPlayers);

      toast({
        title: "Sucesso",
        description: "Convocação atualizada com sucesso.",
      });
    } catch (error) {
      console.error("Erro ao salvar convocação:", error);
      toast({
        title: "Erro",
        description: "Não foi possível salvar a convocação.",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  // Gerar PDF da convocação
  const handleGeneratePDF = async () => {
    if (!callup || !clubInfo) {
      console.error("Não é possível gerar PDF: callup ou clubInfo não definidos");
      toast({
        title: "Erro",
        description: "Dados incompletos para gerar o PDF.",
        variant: "destructive",
      });
      return;
    }

    try {
      setGeneratingPDF(true);

      // Gerar o PDF para download (não para impressão)
      await generateCallupPDF(clubId, callup.id, false);

      toast({
        title: "Sucesso",
        description: "PDF gerado com sucesso.",
      });
    } catch (error) {
      console.error("Erro ao gerar PDF:", error);
      toast({
        title: "Erro",
        description: "Não foi possível gerar o PDF.",
        variant: "destructive",
      });
    } finally {
      setGeneratingPDF(false);
    }
  };

  const handleGenerateAccommodationPDF = async () => {
    if (!callup || !clubInfo) {
      toast({ title: 'Erro', description: 'Dados incompletos para gerar o PDF.', variant: 'destructive' });
      return;
    }

    try {
      setGeneratingAccommodationPDF(true);
      await generateCallupAccommodationPDF(clubId, callup.id);
      toast({ title: 'Sucesso', description: 'PDF gerado com sucesso.' });
    } catch (error) {
      console.error('Erro ao gerar PDF:', error);
      toast({ title: 'Erro', description: 'Não foi possível gerar o PDF.', variant: 'destructive' });
    } finally {
      setGeneratingAccommodationPDF(false);
    }
  };

  // Renderizar a página de detalhes
  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center mb-6">
        <Button variant="outline" onClick={() => navigate("/convocacao")} className="mr-4">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Voltar
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Detalhes da Convocação</h1>
          {callup && (
            <p className="text-muted-foreground">
              {callup.tournament_type} - {format(new Date(callup.match_date), "dd 'de' MMMM 'de' yyyy", { locale: ptBR })}
            </p>
          )}
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      ) : !callup ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center h-64">
            <p className="text-muted-foreground mb-4">
              Convocação não encontrada
            </p>
            <Button onClick={() => navigate("/convocacao")}>
              Voltar para Convocações
            </Button>
          </CardContent>
        </Card>
      ) : (
        <>
          <div className="flex justify-between items-center mb-6">
            <div>
              <h2 className="text-2xl font-bold">
                {callup?.tournament_type} - {callup?.match_date ? format(new Date(callup.match_date), "dd/MM/yyyy", { locale: ptBR }) : ""}
              </h2>
              <p className="text-muted-foreground">
                {callup?.match_location} - {categories.find(c => c.id === callup?.category_id)?.name || "Categoria não definida"}
              </p>
            </div>
            <div className="flex gap-2">
              <Button onClick={handleSaveCallup} disabled={saving}>
                {saving ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Edit className="h-4 w-4 mr-2" />
                )}
                Salvar Alterações
              </Button>
              <Button onClick={handleGenerateAccommodationPDF} disabled={generatingAccommodationPDF}>
                {generatingAccommodationPDF ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Download className="h-4 w-4 mr-2" />
                )}
                PDF Alojamento
              </Button>
            </div>
          </div>

          <Tabs value={selectedTab} onValueChange={setSelectedTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="design">
                <Edit className="h-4 w-4 mr-2" />
                Design
              </TabsTrigger>
              <TabsTrigger value="preview">
                <Eye className="h-4 w-4 mr-2" />
                Visualização
              </TabsTrigger>
            </TabsList>

            <TabsContent value="design">
              {callup && (
                <CallupDesignEditor
                  callup={callup}
                  onCallupChange={(updatedCallup) => setCallup({ ...callup, ...updatedCallup })}
                  players={callupPlayers}
                  onPlayersChange={setCallupPlayers}
                  categoryPlayers={categoryPlayers}
                  clubUsers={clubUsers}
                  collaborators={collaborators}
                  clubInfo={clubInfo}
                  categories={categories}
                />
              )}
            </TabsContent>

            <TabsContent value="preview">
              {callup && (
                <CallupPreview
                  callup={callup}
                  players={callupPlayers}
                  clubInfo={clubInfo}
                  selectedFields={selectedFields}
                />
              )}
            </TabsContent>
          </Tabs>
        </>
      )}
    </div>
  );
}