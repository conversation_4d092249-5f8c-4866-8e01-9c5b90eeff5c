# Plano de Implementação: Atualização do Sistema de Cadastro e Gestão de Usuários

## 1. Visão Geral

Este plano detalha as atualizações necessárias para implementar:
- Upload e exibição de fotos para usuários
- Sistema de hierarquia e permissões
- Cadastro simplificado de jogadores
- Integração com API de CEP e CPF
- Sistema de upload e gestão de documentos
- Controle financeiro mensal para jogadores

## 2. Atualizações no Banco de Dados (Supabase)

### 2.1. Tabela de Usuários e Permissões
```sql
-- Atualizar tabela de usuários
ALTER TABLE users 
ADD COLUMN role TEXT DEFAULT 'user',
ADD COLUMN permissions JSONB DEFAULT '{}';

-- <PERSON><PERSON>r tabel<PERSON> de departamentos
CREATE TABLE departments (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id),
  name TEXT NOT NULL,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Criar tabela de associação entre usuários e departamentos
CREATE TABLE user_departments (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id),
  user_id UUID REFERENCES users(id),
  department_id INTEGER REFERENCES departments(id),
  role TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 2.2. Atualização da Tabela de Jogadores
```sql
-- Atualizar tabela de jogadores
ALTER TABLE players
ADD COLUMN user_id UUID REFERENCES users(id),
ADD COLUMN observation TEXT,
ADD COLUMN is_accommodated BOOLEAN DEFAULT FALSE,
ADD COLUMN accommodation_id INTEGER REFERENCES accommodations(id),
ADD COLUMN financial_data JSONB DEFAULT '{}';
```

### 2.3. Tabela de Documentos
```sql
-- Criar tabela de documentos
CREATE TABLE player_documents (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id),
  player_id UUID REFERENCES players(id),
  document_type TEXT NOT NULL,
  file_url TEXT NOT NULL,
  status TEXT DEFAULT 'pending',
  uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  verified_at TIMESTAMP WITH TIME ZONE,
  verified_by UUID REFERENCES users(id)
);
```

## 3. Atualizações na API

### 3.1. Autenticação e Permissões
- Criar funções para gerenciar permissões de usuários
- Implementar middleware de verificação de permissões
- Criar endpoints para gerenciar departamentos e associações

### 3.2. Upload de Imagens e Documentos
- Implementar endpoints para upload de imagens de perfil
- Criar sistema de armazenamento e recuperação de documentos
- Implementar validação de documentos

### 3.3. Integração com APIs Externas
- Implementar integração com API de CEP (ViaCEP)
- Implementar integração com API de consulta de CPF (a definir)

### 3.4. Gestão Financeira
- Criar endpoints para gerenciar dados financeiros dos jogadores
- Implementar lógica de cálculo de saldos

## 4. Atualizações no Frontend

### 4.1. Componentes de Upload
- Criar componente de upload de imagem de perfil
- Implementar componente de upload de documentos
- Adicionar visualizador de documentos

### 4.2. Sistema de Permissões
- Criar interface de administração de permissões
- Implementar controle de acesso baseado em permissões
- Criar visualizações específicas para cada tipo de usuário

### 4.3. Formulário Simplificado de Cadastro
- Implementar formulário simplificado para cadastro de jogadores
- Adicionar integração com API de CEP
- Implementar busca automática de dados por CPF

### 4.4. Gestão de Documentos
- Criar interface para upload e visualização de documentos
- Implementar indicadores visuais de status dos documentos
- Adicionar sistema de notificações para documentos pendentes

### 4.5. Controle Financeiro
- Criar interface para gestão financeira mensal
- Implementar visualização de histórico financeiro
- Adicionar relatórios e gráficos

## 5. Fluxos de Trabalho

### 5.1. Cadastro de Jogador
1. Departamento administrativo/técnico/secretaria cadastra jogador com informações básicas
2. Sistema cria conta de usuário para o jogador
3. Jogador recebe credenciais e completa seu cadastro
4. Jogador faz upload dos documentos obrigatórios
5. Departamento verifica e valida documentos

### 5.2. Gestão de Permissões
1. Presidente define departamentos e funções
2. Usuários são associados a departamentos com funções específicas
3. Sistema aplica permissões baseadas nas associações
4. Usuários visualizam apenas o conteúdo permitido para seu nível de acesso

### 5.3. Upload e Verificação de Documentos
1. Jogador faz upload de documentos obrigatórios
2. Sistema marca documentos como pendentes
3. Departamento responsável verifica documentos
4. Documentos são marcados como verificados ou rejeitados
5. Jogador é notificado sobre o status dos documentos

### 5.4. Gestão Financeira
1. Departamento financeiro registra transações mensais
2. Sistema calcula saldos e gera relatórios
3. Jogador visualiza seu histórico financeiro
4. Departamentos autorizados acessam relatórios financeiros

## 6. Priorização e Cronograma

### Fase 1: Infraestrutura Básica
- Atualização do banco de dados
- Implementação do sistema de upload de imagens
- Criação do sistema básico de permissões

### Fase 2: Cadastro e Documentos
- Formulário simplificado de cadastro
- Integração com APIs externas (CEP e CPF)
- Sistema de upload e gestão de documentos

### Fase 3: Permissões Avançadas e Financeiro
- Sistema completo de hierarquia e departamentos
- Controle financeiro mensal
- Relatórios e dashboards

## 7. Considerações Técnicas

### 7.1. Armazenamento de Arquivos
- Utilizar o storage do Supabase para armazenar imagens e documentos
- Implementar políticas de segurança para acesso aos arquivos
- Considerar compressão de imagens para otimizar o armazenamento

### 7.2. Segurança
- Implementar validação rigorosa de permissões em todas as operações
- Proteger dados sensíveis (CPF, documentos, dados financeiros)
- Registrar logs de acesso e modificações em dados sensíveis

### 7.3. Performance
- Otimizar consultas ao banco de dados
- Implementar cache para dados frequentemente acessados
- Considerar paginação para listas grandes de documentos ou transações

### 7.4. Usabilidade
- Criar interfaces intuitivas para diferentes perfis de usuário
- Implementar feedback visual claro para ações importantes
- Fornecer mensagens de erro e sucesso informativas
