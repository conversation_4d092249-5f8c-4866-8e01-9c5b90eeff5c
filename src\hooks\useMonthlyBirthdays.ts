import { useState, useEffect } from "react";
import { getMonthlyBirthdays, Birthday<PERSON>erson } from "@/api/birthdays";

interface UseMonthlyBirthdaysReturn {
  birthdays: BirthdayPerson[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export function useMonthlyBirthdays(
  clubId: number | null,
  categoryId?: number
): UseMonthlyBirthdaysReturn {
  const [birthdays, setBirthdays] = useState<BirthdayPerson[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    if (!clubId) {
      setBirthdays([]);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const data = await getMonthlyBirthdays(clubId, categoryId);
      setBirthdays(data);
    } catch (err) {
      const msg = err instanceof Error ? err.message : "Erro ao buscar aniversariantes";
      setError(msg);
      console.error("Error fetching birthdays:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [clubId, categoryId]);

  return { birthdays, loading, error, refetch: fetchData };
}