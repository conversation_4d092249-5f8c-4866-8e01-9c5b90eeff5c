import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Di<PERSON>, DialogContent, DialogHeader, <PERSON><PERSON>Title, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { usePermission } from "@/hooks/usePermission";
import {
  InventoryRequest,
  InventoryRequestItem,
  INVENTORY_DEPARTMENTS,
  PLAYER_REQUEST_CATEGORIES,
  createInventoryRequest,
  updateInventoryRequest,
  getInventoryProducts,
  addInventoryRequestItem,
  getInventoryRequestItems,
  removeInventoryRequestItem,
  getPlayers,
  getClubUsers,
  getDepartments
} from "@/api/api";
import { Plus, Trash2 } from "lucide-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

// Schema for validation
const requestSchema = z.object({
  department_id: z.coerce.number().optional().nullable(),
  requester_type: z.string().min(1, "Tipo de solicitante é obrigatório"),
  requester_id: z.string().optional().nullable(),
  category: z.string().min(1, "Categoria é obrigatória"),
  withdrawal_date: z.string().min(1, "Data de retirada é obrigatória"),
  requester_notes: z.string().optional().nullable(),
  delivery_method: z.enum(["pickup", "delivery"], {
    required_error: "Método de entrega é obrigatório",
  }),
  delivery_location: z.string().optional().nullable(),
});

type RequestFormValues = z.infer<typeof requestSchema>;

interface SolicitacaoEstoqueDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  request: InventoryRequest | null;
  onSuccess: () => void;
}

export function SolicitacaoEstoqueDialog({
  open,
  onOpenChange,
  request,
  onSuccess,
}: SolicitacaoEstoqueDialogProps) {
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const { role } = usePermission();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [products, setProducts] = useState<any[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<any[]>([]);
  const [selectedProduct, setSelectedProduct] = useState<number | null>(null);
  const [selectedQuantity, setSelectedQuantity] = useState<number>(1);
  const [requestItems, setRequestItems] = useState<InventoryRequestItem[]>([]);
  const [players, setPlayers] = useState<any[]>([]);
  const [users, setUsers] = useState<any[]>([]);
  const editMode = !!request;

  // Initialize form
  const form = useForm<RequestFormValues>({
    resolver: zodResolver(requestSchema),
    defaultValues: {
      department_id: null,
      requester_type: role === 'player' ? 'player' : 'staff',
      requester_id: null,
      category: "",
      withdrawal_date: new Date().toISOString().split("T")[0],
      requester_notes: "",
      delivery_method: "pickup",
      delivery_location: "",
    },
  });

  // Load data when dialog opens
  useEffect(() => {
    if (open) {
      loadData();
      if (editMode && request) {
        loadRequestData();
      }
    }
  }, [open, editMode, request]);

  // Filter products based on selected category
  useEffect(() => {
    const selectedCategory = form.watch("category");
    if (selectedCategory && products.length > 0) {
      const filtered = products.filter(product => product.department === selectedCategory);
      setFilteredProducts(filtered);
      // Reset selected product if it's not in the filtered list
      if (selectedProduct && !filtered.some(p => p.id === selectedProduct)) {
        setSelectedProduct(null);
      }
    } else {
      setFilteredProducts(products);
    }
  }, [form.watch("category"), products, selectedProduct]);

  // Load products, players, users, and departments
  const loadData = async () => {
    try {
      setIsLoading(true);

      // Load products
      const productsData = await getInventoryProducts(clubId);
      setProducts(productsData);

      // Initialize filtered products
      const selectedCategory = form.watch("category");
      if (selectedCategory) {
        setFilteredProducts(productsData.filter(product => product.department === selectedCategory));
      } else {
        setFilteredProducts(productsData);
      }

      // Load players
      const playersData = await getPlayers(clubId);
      setPlayers(playersData);

      // Load users, including collaborators without user accounts
      const usersData = await getClubUsers(clubId, [], true);
      setUsers(usersData);

      // Load departments (não precisamos armazenar mais)
      await getDepartments(clubId);
    } catch (error) {
      console.error("Error loading data:", error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao carregar os dados. Tente novamente.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Load request data for editing
  const loadRequestData = async () => {
    if (!request) return;

    try {
      setIsLoading(true);

      // Set form values
      form.setValue("department_id", request.department_id);
      form.setValue("requester_type", request.requester_type);
      form.setValue("requester_id", request.requester_id);
      form.setValue("category", request.category);
      form.setValue("withdrawal_date", new Date(request.withdrawal_date).toISOString().split("T")[0]);
      form.setValue("requester_notes", request.requester_notes || "");
      form.setValue("delivery_method", request.delivery_method as "pickup" | "delivery");
      form.setValue("delivery_location", request.delivery_location || "");

      // Load request items
      const items = await getInventoryRequestItems(clubId, request.id);
      setRequestItems(items);
    } catch (error) {
      console.error("Error loading request data:", error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao carregar os dados da solicitação. Tente novamente.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Add product to request
  const handleAddProduct = async () => {
    if (!selectedProduct || selectedQuantity <= 0) {
      toast({
        title: "Erro",
        description: "Selecione um produto e uma quantidade válida.",
        variant: "destructive",
      });
      return;
    }

    const product = products.find(p => p.id === selectedProduct);
    if (product && product.quantity <= 0) {
      toast({
        title: "Sem estoque",
        description: "Este produto está sem estoque disponível.",
        variant: "destructive",
      });
      return;
    }
    // Check if product is already in the list
    const existingItem = requestItems.find(item => item.product_id === selectedProduct);
    if (existingItem) {
      // Update quantity
      const updatedItems = requestItems.map(item => {
        if (item.product_id === selectedProduct) {
          return {
            ...item,
            quantity: item.quantity + selectedQuantity
          };
        }
        return item;
      });
      setRequestItems(updatedItems);
    } else {
      // Add new item
      const product = products.find(p => p.id === selectedProduct);
      if (product) {
        const newItem: InventoryRequestItem = {
          id: 0, // Temporary ID
          club_id: clubId,
          request_id: request?.id || 0,
          product_id: selectedProduct,
          quantity: selectedQuantity,
          returned_quantity: 0,
          created_at: new Date().toISOString(),
          product_name: product.name,
          product_department: product.department,
          product_location: product.location,
          available_quantity: product.quantity
        };
        setRequestItems([...requestItems, newItem]);
      }
    }

    // Reset selection
    setSelectedProduct(null);
    setSelectedQuantity(1);
  };

  // Remove product from request
  const handleRemoveProduct = async (itemId: number) => {
    // If editing an existing request, remove from database
    if (editMode && request && itemId > 0) {
      try {
        await removeInventoryRequestItem(clubId, itemId, user?.id);
        setRequestItems(requestItems.filter(item => item.id !== itemId));
      } catch (error) {
        console.error("Error removing item:", error);
        toast({
          title: "Erro",
          description: "Ocorreu um erro ao remover o item. Tente novamente.",
          variant: "destructive",
        });
      }
    } else {
      // Just remove from local state
      setRequestItems(requestItems.filter(item => item.id !== itemId));
    }
  };

  // Save request
  const onSubmit = async (data: RequestFormValues) => {
    if (requestItems.length === 0) {
      toast({
        title: "Erro",
        description: "Adicione pelo menos um produto à solicitação.",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);

      if (editMode && request) {
        // Update existing request
        const updateData = {
          department_id: data.department_id,
          requester_type: data.requester_type,
          requester_id: data.requester_id,
          category: data.category,
          withdrawal_date: data.withdrawal_date,
          requester_notes: data.requester_notes,
          delivery_method: data.delivery_method,
          delivery_location: data.delivery_location,
          status: request.status
        };

        await updateInventoryRequest(
          clubId,
          request.id,
          updateData,
          user?.id
        );

        toast({
          title: "Solicitação atualizada",
          description: "A solicitação foi atualizada com sucesso.",
        });

        onSuccess();
        onOpenChange(false);
      } else {
        // Create new request
        const requestData = {
          department_id: data.department_id || null,
          requester_type: data.requester_type,
          requester_id: data.requester_id || null,
          category: data.category,
          withdrawal_date: data.withdrawal_date,
          requester_notes: data.requester_notes || null,
          delivery_method: data.delivery_method,
          delivery_location: data.delivery_location || null,
          requested_by: user?.id || "",
          status: "pending" as const,
          delivery_notes: null,
          requester_signature_url: null,
          delivery_signature_url: null
        };

        const newRequest = await createInventoryRequest(
          clubId,
          requestData,
          user?.id
        );

        // Add items to the request
        for (const item of requestItems) {
          await addInventoryRequestItem(
            clubId,
            newRequest.id,
            item.product_id,
            item.quantity,
            user?.id
          );
        }

        toast({
          title: "Solicitação criada",
          description: "A solicitação foi criada e assinada automaticamente com sucesso.",
        });

        onSuccess();
        onOpenChange(false);
      }
    } catch (error) {
      console.error("Error saving request:", error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao salvar a solicitação. Tente novamente.",
        variant: "destructive",
      });
      setIsLoading(false);
    }
  };



  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{editMode ? "Editar Solicitação" : "Nova Solicitação de Estoque"}</DialogTitle>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="category"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Departamento</FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o departamento" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {(role === 'player' ? PLAYER_REQUEST_CATEGORIES : INVENTORY_DEPARTMENTS).map((dept) => (
                            <SelectItem key={dept} value={dept}>
                              {dept}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="requester_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tipo de Solicitante</FormLabel>
                      <Select
                        disabled={role === 'player'}
                        value={field.value}
                        onValueChange={(value) => {
                          field.onChange(value);
                          form.setValue("requester_id", null);
                        }}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o tipo" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="player">Atleta</SelectItem>
                          <SelectItem value="staff">Membro da Equipe Técnica</SelectItem>
                          <SelectItem value="medical">Departamento Médico</SelectItem>
                          <SelectItem value="administrative">Administrativo</SelectItem>
                          <SelectItem value="cozinha">Cozinha</SelectItem>
                          <SelectItem value="manutencao">Manutenção</SelectItem>
                          <SelectItem value="dep_medico">Dep Médico</SelectItem>
                          <SelectItem value="torcedor">Torcedor</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">


                <FormField
                  control={form.control}
                  name="requester_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Retirada por</FormLabel>
                      <Select
                        value={field.value || ""}
                        onValueChange={field.onChange}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione a pessoa" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {form.watch("requester_type") === "player" ? (
                            [...players]
                              .sort((a, b) => a.name.localeCompare(b.name))
                              .map((player) => (
                                <SelectItem key={player.id} value={player.id}>
                                  {player.name}
                                </SelectItem>
                              ))
                          ) : (
                            [...users]
                              .sort((a, b) => (a.name || a.email).localeCompare(b.name || b.email))
                              .map((user) => (
                                <SelectItem key={user.id} value={user.id}>
                                  {user.name || user.email}
                                </SelectItem>
                              ))
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                              <FormField
                control={form.control}
                name="withdrawal_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Data de Saída</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              </div>



              <div className="border p-4 rounded-md">
                <h3 className="text-lg font-medium mb-2">Departamento do Produto</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Os produtos são filtrados de acordo com o departamento selecionado no campo "Categoria".
                </p>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div>
                    <Label htmlFor="product">Produto</Label>
                    <Select
                      value={selectedProduct?.toString() || ""}
                      onValueChange={(value) => setSelectedProduct(parseInt(value))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o produto" />
                      </SelectTrigger>
                      <SelectContent>
                        {filteredProducts.length > 0 ? (
                          filteredProducts.map((product) => (
                            <SelectItem
                            key={product.id}
                            value={product.id.toString()}
                            disabled={product.quantity === 0}
                          >
                              {product.name} ({product.quantity} disponíveis)
                            </SelectItem>
                          ))
                        ) : (
                          <SelectItem value="no-products" disabled>
                            Nenhum produto disponível neste departamento
                          </SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="quantity">Quantidade</Label>
                    <Input
                      id="quantity"
                      type="number"
                      min="1"
                      value={selectedQuantity}
                      onChange={(e) => setSelectedQuantity(parseInt(e.target.value) || 1)}
                    />
                  </div>

                  <div className="flex items-end">
                    <Button
                      type="button"
                      onClick={handleAddProduct}
                      className="gap-1"
                    >
                      <Plus className="h-4 w-4" />
                      Adicionar
                    </Button>
                  </div>
                </div>

                <div className="border rounded-md">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Produto</TableHead>
                        <TableHead>Departamento</TableHead>
                        <TableHead>Localização</TableHead>
                        <TableHead>Quantidade</TableHead>
                        <TableHead>Ações</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {requestItems.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={5} className="text-center py-4">
                            Nenhum produto adicionado
                          </TableCell>
                        </TableRow>
                      ) : (
                        requestItems.map((item, index) => (
                          <TableRow key={item.id || `temp-${index}`}>
                            <TableCell>{item.product_name}</TableCell>
                            <TableCell>{item.product_department}</TableCell>
                            <TableCell>{item.product_location || "-"}</TableCell>
                            <TableCell>{item.quantity}</TableCell>
                            <TableCell>
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                onClick={() => handleRemoveProduct(item.id)}
                              >
                                <Trash2 className="h-4 w-4 text-red-500" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </div>

              <FormField
                control={form.control}
                name="requester_notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Observações do Solicitante</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Observações adicionais sobre a solicitação"
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="delivery_method"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Retirada</FormLabel>
                    <FormControl>
                      <RadioGroup
                        value={field.value}
                        onValueChange={field.onChange}
                        className="flex flex-col space-y-1"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="pickup" id="pickup" />
                          <Label htmlFor="pickup">Vou retirar</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="delivery" id="delivery" />
                          <Label htmlFor="delivery">Preciso que me entregue</Label>
                        </div>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {form.watch("delivery_method") === "delivery" && (
                <FormField
                  control={form.control}
                  name="delivery_location"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Local de Entrega</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Informe o local de entrega"
                          {...field}
                          value={field.value || ""}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                >
                  Cancelar
                </Button>
                <Button
                  type="submit"
                  isLoading={isLoading}
                >
                  {editMode ? "Atualizar" : "Criar Solicitação"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
  );
}
