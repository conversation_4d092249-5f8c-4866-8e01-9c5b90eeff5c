import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { ImageUpload } from "@/components/ui/image-upload";
import { toast } from "@/components/ui/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { createPlayer, getAccommodations, Accommodation } from "@/api/api";
import { uploadProfileImage } from "@/api/storage";
import { v4 as uuidv4 } from "uuid";
import { useUser } from "@/context/UserContext";

interface SimplePlayerDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function SimplePlayerDialog({
  open,
  onOpenChange,
  onSuccess,
}: SimplePlayerDialogProps) {
  const clubId = useCurrentClubId();
  const { user } = useUser();

  // Estados para os campos do formulário
  const [name, setName] = useState("");
  const [observation, setObservation] = useState("");
  const [isAccommodated, setIsAccommodated] = useState(false);
  const [accommodationId, setAccommodationId] = useState<string>("");
  const [status, setStatus] = useState("disponivel");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [profileImage, setProfileImage] = useState<string | null>(null);
  const [profileImageFile, setProfileImageFile] = useState<File | null>(null);

  // Estados para controle do formulário
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [accommodations, setAccommodations] = useState<Accommodation[]>([]);

  // Carregar alojamentos disponíveis
  useEffect(() => {
    const fetchAccommodations = async () => {
      try {
        const data = await getAccommodations(clubId);
        setAccommodations(data);
      } catch (err: any) {
        console.error("Erro ao carregar alojamentos:", err);
        toast({
          title: "Erro",
          description: "Não foi possível carregar os alojamentos",
          variant: "destructive",
        });
      }
    };

    if (open) {
      fetchAccommodations();
    }
  }, [clubId, open]);

  // Limpar formulário quando o modal é aberto/fechado
  useEffect(() => {
    if (open) {
      setName("");
      setObservation("");
      setIsAccommodated(false);
      setAccommodationId("");
      setStatus("disponivel");
      setEmail("");
      setPassword("");
      setProfileImage(null);
      setProfileImageFile(null);
      setError(null);
    }
  }, [open]);

  // Função para gerar uma senha aleatória
  const generatePassword = () => {
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let result = "";
    for (let i = 0; i < 10; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    setPassword(result);
  };

  // Função para extrair o primeiro nome
  const getFirstName = (fullName: string) => {
    return fullName.split(" ")[0] || "";
  };

  // Função para salvar o jogador
  const handleSave = async () => {
    try {
      // Validar campos obrigatórios
      if (!name.trim()) {
        setError("Nome é obrigatório");
        return;
      }

      if (!email.trim()) {
        setError("Email é obrigatório");
        return;
      }

      if (!password.trim()) {
        setError("Senha é obrigatória");
        return;
      }

      if (isAccommodated && !accommodationId) {
        setError("Selecione um alojamento");
        return;
      }

      setLoading(true);
      setError(null);

      console.log("Iniciando cadastro de jogador:", { name, email });

      // Gerar ID único para o jogador
      const playerId = uuidv4();
      console.log("ID gerado para o jogador:", playerId);

      // Fazer upload da imagem de perfil, se houver
      let imageUrl = null;
      if (profileImageFile) {
        console.log("Fazendo upload da imagem de perfil");
        imageUrl = await uploadProfileImage(playerId, profileImageFile);
        console.log("Upload concluído, URL:", imageUrl);
      }

      // Criar jogador
      const playerData = {
        name,
        observation,
        is_accommodated: isAccommodated,
        accommodation_id: isAccommodated ? parseInt(accommodationId) : undefined,
        status,
        image: imageUrl,
        // Campos obrigatórios para a API de jogadores
        position: "Não definido",
        age: 0,
        number: 0,
        // Campos para criar conta de usuário
        user_id: playerId,
        nickname: getFirstName(name),
        email,
        password,
      };

      console.log("Chamando API para criar jogador:", {
        clubId,
        playerName: playerData.name,
        playerId: playerData.user_id
      });

      const createdPlayer = await createPlayer(clubId, playerData, user?.id);
      console.log("Jogador criado com sucesso:", {
        id: createdPlayer.id,
        name: createdPlayer.name,
        registrationNumber: createdPlayer.registration_number
      });

      toast({
        title: "Sucesso",
        description: "Jogador cadastrado com sucesso",
      });

      onOpenChange(false);

      if (onSuccess) {
        onSuccess();
      }
    } catch (err: any) {
      console.error("Erro ao cadastrar jogador:", err);
      setError(err.message || "Erro ao cadastrar jogador");
      toast({
        title: "Erro",
        description: err.message || "Erro ao cadastrar jogador",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Cadastro Rápido de Jogador</DialogTitle>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="flex justify-center mb-4">
            <ImageUpload
              value={profileImage || undefined}
              onChange={(url, file) => {
                setProfileImage(url);
                setProfileImageFile(file || null);
              }}
              shape="circle"
              placeholder="Foto do jogador"
            />
          </div>

          <div className="grid grid-cols-1 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Nome do Jogador*</Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Nome completo"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="observation">Observação</Label>
              <Textarea
                id="observation"
                value={observation}
                onChange={(e) => setObservation(e.target.value)}
                placeholder="Observações sobre o jogador"
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="is-accommodated"
                  checked={isAccommodated}
                  onCheckedChange={(checked) => setIsAccommodated(checked === true)}
                />
                <Label htmlFor="is-accommodated">Jogador está alojado</Label>
              </div>
            </div>

            {isAccommodated && (
              <div className="space-y-2">
                <Label htmlFor="accommodation">Alojamento*</Label>
                <Select
                  value={accommodationId}
                  onValueChange={setAccommodationId}
                >
                  <SelectTrigger id="accommodation">
                    <SelectValue placeholder="Selecione um alojamento" />
                  </SelectTrigger>
                  <SelectContent>
                    {accommodations.map((accommodation) => (
                      <SelectItem
                        key={accommodation.id}
                        value={accommodation.id.toString()}
                      >
                        {accommodation.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="status">Status do Atleta*</Label>
              <Select value={status} onValueChange={setStatus}>
                <SelectTrigger id="status">
                  <SelectValue placeholder="Selecione o status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="disponivel">Disponível</SelectItem>
                  <SelectItem value="indisponivel">Indisponível</SelectItem>
                  <SelectItem value="emprestado">Emprestado</SelectItem>
                  <SelectItem value="lesionado">Lesionado</SelectItem>
                  <SelectItem value="suspenso">Suspenso</SelectItem>
                  <SelectItem value="em avaliacao">Em Avaliação</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">Email*</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Email para acesso ao sistema"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">Senha*</Label>
              <div className="flex space-x-2">
                <Input
                  id="password"
                  type="text"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Senha para acesso ao sistema"
                  required
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={generatePassword}
                >
                  Gerar
                </Button>
              </div>
            </div>
          </div>
        </div>

        {error && <p className="text-red-500 text-sm">{error}</p>}

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleSave} disabled={loading}>
            {loading ? "Salvando..." : "Salvar"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
