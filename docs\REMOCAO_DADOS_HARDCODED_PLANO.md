# Plano de Ação: Remoção de Dados Hardcoded e Integração Total com Supabase

## 📋 Visão Geral
Este documento descreve o plano detalhado para remover todos os dados hardcoded do sistema e garantir que todos os dados sejam obtidos do Supabase.

## 🎯 Objetivos
- Remover 100% dos dados hardcoded do frontend
- Garantir que todos os dados sejam obtidos do Supabase
- Manter ou melhorar a performance
- Garantir consistência dos dados
- Melhorar a manutenibilidade do código

## 📅 Cronograma Estimado
**Duração total estimada:** 4-6 semanas

## 🗂️ Módulos do Sistema

### 1. 🔐 Autenticação e Usuários
- [ ] Verificar se todos os dados de usuário vêm do Supabase
- [ ] Garantir que as permissões sejam validadas corretamente
- [ ] Remover quaisquer mocks de usuários
- [ ] Validar fluxos de autenticação

### 2. ⚽ Elenco e Jogadores (Em andamento)
#### 2.1 Listagem de Jogadores
- [x] Garantir que a lista de jogadores vem do Supabase
- [x] Remover dados fake da listagem
- [x] Implementar loading states

#### 2.2 Detalhes do Jogador
- [ ] Garantir que todos os detalhes vêm do banco
- [ ] Remover dados hardcoded de estatísticas
- [ ] Implementar tratamento para dados ausentes

#### 2.3 Status do Elenco
- [x] Implementar cálculo dinâmico de status (Disponíveis/Lesionados)
- [ ] Validar com diferentes cenários de dados

### 3. 📅 Partidas
#### 3.1 Próximas Partidas
- [ ] Buscar dados reais do Supabase
- [ ] Remover dados de exemplo
- [ ] Implementar tratamento para sem partidas

#### 3.2 Histórico de Partidas
- [ ] Integrar com histórico real
- [ ] Remover dados fake
- [ ] Implementar paginação se necessário

#### 3.3 Estatísticas de Partidas
- [ ] Garantir que estatísticas são calculadas a partir de dados reais
- [ ] Remover valores hardcoded
- [ ] Validar cálculos

### 4. 🏥 Departamento Médico
#### 4.1 Prontuários
- [ ] Garantir que todos os prontuários vêm do banco
- [ ] Remover dados de exemplo
- [ ] Implementar filtros e busca

#### 4.2 Sessões de Reabilitação
- [ ] Integrar com a tabela rehab_sessions
- [ ] Remover dados fake
- [ ] Implementar CRUD completo

### 5. 🏋️ Treinamentos
#### 5.1 Sessões de Treino
- [ ] Buscar dados do Supabase
- [ ] Remover dados de exemplo
- [ ] Implementar criação/edição

#### 5.2 Exercícios
- [ ] Integrar com banco de exercícios
- [ ] Remover exemplos hardcoded
- [ ] Implementar busca/filtros

### 6. 💰 Financeiro
#### 6.1 Contratos
- [ ] Integrar com dados reais
- [ ] Remover exemplos
- [ ] Implementar relatórios

#### 6.2 Folha de Pagamento
- [ ] Buscar dados do banco
- [ ] Remover valores fake
- [ ] Implementar cálculos dinâmicos

### 7. 📊 Relatórios
#### 7.1 Estatísticas
- [ ] Garantir que todos os dados são reais
- [ ] Remover dados de exemplo
- [ ] Validar cálculos

#### 7.2 Exportação
- [ ] Implementar exportação de dados reais
- [ ] Validar formatos

## 🔄 Fluxo de Trabalho

1. **Para cada módulo**:
   - Criar branch específica (ex: `feature/remove-hardcoded-NOME_MODULO`)
   - Implementar as mudanças
   - Testar localmente
   - Criar PR para revisão
   - Fazer deploy em ambiente de teste
   - Validar em QA
   - Fazer merge para develop

2. **Padrões a Seguir**:
   - Sempre usar hooks do React para gerenciar estado
   - Implementar estados de loading/error
   - Usar TypeScript para tipagem forte
   - Documentar alterações

## 🛠️ Ferramentas e Recursos

- **Supabase Dashboard**: Para monitorar queries
- **React DevTools**: Para depuração
- **Redux DevTools**: Para gerenciamento de estado
- **Lighthouse**: Para métricas de performance

## 📈 Métricas de Sucesso

- [ ] 0 dados hardcoded no código
- [ ] Todas as chamadas de API com tratamento de erro
- [ ] Tempo de carregamento aceitável
- [ ] Cobertura de testes adequada
- [ ] Documentação atualizada

## ✅ Checklist de Validação

Antes de marcar um módulo como concluído:

- [ ] Todos os dados vêm do Supabase
- [ ] Não há mais dados hardcoded
- [ ] Loading states implementados
- [ ] Tratamento de erros implementado
- [ ] Testes atualizados
- [ ] Documentação atualizada

## 📝 Notas Adicionais

- Manter comunicação constante com a equipe
- Documentar quaisquer desafios encontrados
- Atualizar este documento conforme necessário

---
**Última atualização:** 27/05/2025
**Responsável:** [Seu Nome]
