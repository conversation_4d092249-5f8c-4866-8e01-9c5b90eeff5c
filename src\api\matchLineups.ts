import { supabase } from "@/integrations/supabase/client";
import { addPlayerToCallup } from "./callups";

// Tipos para o sistema de escalação por partida
export interface MatchLineup {
  id: number;
  club_id: number;
  match_id: string;
  formation: string;
  lineup: Record<string, string | null>; // posição -> player_id
  created_at: string;
  updated_at: string;
}

export interface MatchSquadMember {
  id: number;
  club_id: number;
  match_id: string;
  player_id?: string;
  user_id?: string;
  collaborator_id?: number;
  role: 'starter' | 'substitute' | 'technical_staff' | 'staff' | 'executive';
  position?: string;
  jersey_number?: number;
  created_at: string;
  // Dados relacionados (populados via join)
  player?: {
    id: string;
    name: string;
    position: string;
    number: number;
    image?: string;
  };
  user?: {
    id: string;
    name: string;
    email: string;
  };
  collaborator?: {
    id: number;
    full_name: string;
    role: string;
    user_id?: string;
  };
}

export interface MatchSubstitution {
  id: number;
  club_id: number;
  match_id: string;
  player_out_id: string;
  player_in_id: string;
  minute: number;
  reason?: string;
  created_at: string;
  // Dados relacionados
  player_out?: {
    id: string;
    name: string;
    number: number;
  };
  player_in?: {
    id: string;
    name: string;
    number: number;
  };
}

export interface MatchPlayerMinutes {
  id: number;
  club_id: number;
  match_id: string;
  player_id: string;
  minutes_played: number;
  started: boolean;
  substituted_in_minute?: number;
  substituted_out_minute?: number;
  created_at: string;
  updated_at: string;
  // Dados relacionados
  player?: {
    id: string;
    name: string;
    number: number;
    position: string;
  };
}

/**
 * Busca a escalação de uma partida específica
 */
export async function getMatchLineup(clubId: number, matchId: string): Promise<MatchLineup | null> {
  try {
    const { data, error } = await supabase
      .from("match_lineups")
      .select("*")
      .eq("club_id", clubId)
      .eq("match_id", matchId)
      .maybeSingle();

    if (error) {
      console.error("Erro ao buscar escalação da partida:", error);
      throw new Error(`Erro ao buscar escalação da partida: ${error.message}`);
    }

    return data;
  } catch (error: any) {
    // Se a tabela não existir, retornar null
    if (error.message?.includes('relation "match_lineups" does not exist') ||
        error.message?.includes('Not Acceptable') ||
        error.code === 'PGRST106') {
      console.warn("Tabela match_lineups não existe. Execute o script SQL para criar as tabelas.");
      return null;
    }
    throw error;
  }
}

/**
 * Salva ou atualiza a escalação de uma partida
 */
export async function saveMatchLineup(
  clubId: number,
  matchId: string,
  lineup: Record<string, string | null>,
  formation: string
): Promise<MatchLineup> {
  // Verificar se já existe uma escalação para esta partida
  const existing = await getMatchLineup(clubId, matchId);

  let result: MatchLineup;

  if (existing) {
    // Atualizar escalação existente
    const { data, error } = await supabase
      .from("match_lineups")
      .update({
        lineup,
        formation,
        updated_at: new Date().toISOString()
      })
      .eq("club_id", clubId)
      .eq("match_id", matchId)
      .select()
      .single();

    if (error) {
      console.error("Erro ao atualizar escalação da partida:", error);
      throw new Error(`Erro ao atualizar escalação da partida: ${error.message}`);
    }

    result = data;
  } else {
    // Criar nova escalação
    const { data, error } = await supabase
      .from("match_lineups")
      .insert({
        club_id: clubId,
        match_id: matchId,
        lineup,
        formation
      })
      .select()
      .single();

    if (error) {
      console.error("Erro ao criar escalação da partida:", error);
      throw new Error(`Erro ao criar escalação da partida: ${error.message}`);
    }

    result = data;
  }

  // Automaticamente sincronizar com o squad da partida
  await syncLineupWithSquad(clubId, matchId, lineup);

  // Automaticamente sincronizar com a convocação se existir
  await syncWithCallup(clubId, matchId);

  return result;
}

/**
 * Busca todos os membros do squad de uma partida
 */
export async function getMatchSquad(clubId: number, matchId: string): Promise<MatchSquadMember[]> {
  try {
    const { data, error } = await supabase
      .from("match_squad")
      .select(`
        *,
        player:players(id, name, position, number, image)
      `)
      .eq("club_id", clubId)
      .eq("match_id", matchId)
      .order("role", { ascending: true })
      .order("jersey_number", { ascending: true });

    if (error) {
      console.error("Erro ao buscar squad da partida:", error);
      throw new Error(`Erro ao buscar squad da partida: ${error.message}`);
    }

    // Se temos dados, buscar informações dos usuários e colaboradores separadamente
    if (data && data.length > 0) {
      const userIds = data.filter(item => item.user_id).map(item => item.user_id);
      const collaboratorIds = data.filter(item => item.collaborator_id).map(item => item.collaborator_id);

      let usersData: any[] = [];
      if (userIds.length > 0) {
        const { data: users, error: usersError } = await supabase
          .from("users")
          .select("id, email, name")
          .in("id", userIds);

        if (!usersError && users) {
          usersData = users;
        }
      }

      let collaboratorsData: any[] = [];
      if (collaboratorIds.length > 0) {
        const { data: collaborators, error: collaboratorsError } = await supabase
          .from("collaborators")
          .select("id, full_name, role")
          .in("id", collaboratorIds);

        if (!collaboratorsError && collaborators) {
          collaboratorsData = collaborators;
        }
      }

      // Combinar dados
      const enrichedData = data.map(item => ({
        ...item,
        user: item.user_id ? usersData.find(u => u.id === item.user_id) : null,
        collaborator: item.collaborator_id ? collaboratorsData.find(c => c.id === item.collaborator_id) : null
      }));

      return enrichedData;
    }

    return data || [];
  } catch (error: any) {
    // Se a tabela não existir, retornar array vazio
    if (error.message?.includes('relation "match_squad" does not exist') ||
        error.message?.includes('Not Acceptable') ||
        error.code === 'PGRST106') {
      console.warn("Tabela match_squad não existe. Execute o script SQL para criar as tabelas.");
      return [];
    }
    throw error;
  }
}

/**
 * Adiciona um membro ao squad da partida
 */
export async function addMatchSquadMember(
  clubId: number,
  matchId: string,
  member: {
    player_id?: string;
    user_id?: string;
    collaborator_id?: number;
    role: 'starter' | 'substitute' | 'technical_staff' | 'staff' | 'executive';
    position?: string;
    jersey_number?: number;
  }
): Promise<MatchSquadMember> {
  const { data, error } = await supabase
    .from("match_squad")
    .insert({
      club_id: clubId,
      match_id: matchId,
      ...member
    })
    .select(`
      *,
      player:players(id, name, position, number, image)
    `)
    .single();

  if (error) {
    console.error("Erro ao adicionar membro ao squad:", error);
    throw new Error(`Erro ao adicionar membro ao squad: ${error.message}`);
  }

  // Buscar informações do usuário se necessário
  let enrichedData = data;
  if (data.user_id) {
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("id, email, name")
      .eq("id", data.user_id)
      .single();

    if (!userError && userData) {
      enrichedData = {
        ...data,
        user: userData
      };
    }
  }

  // Buscar informações do colaborador se necessário
  if (data.collaborator_id) {
    const { data: collaboratorData, error: collaboratorError } = await supabase
      .from("collaborators")
      .select("id, full_name, role")
      .eq("id", data.collaborator_id)
      .single();

    if (!collaboratorError && collaboratorData) {
      enrichedData = {
        ...enrichedData,
        collaborator: collaboratorData
      };
    }
  }

  // Sincronizar com convocação após adicionar ao squad
  try {
    await syncWithCallup(clubId, matchId);
  } catch (syncError) {
    console.error("Erro ao sincronizar com convocação:", syncError);
    // Não falhar a operação principal
  }

  return enrichedData;
}

/**
 * Remove um membro do squad da partida
 */
export async function removeMatchSquadMember(
  clubId: number,
  matchId: string,
  memberId: number
): Promise<void> {
  const { error } = await supabase
    .from("match_squad")
    .delete()
    .eq("club_id", clubId)
    .eq("match_id", matchId)
    .eq("id", memberId);

  if (error) {
    console.error("Erro ao remover membro do squad:", error);
    throw new Error(`Erro ao remover membro do squad: ${error.message}`);
  }
}

/**
 * Atualiza um membro do squad da partida
 */
export async function updateMatchSquadMember(
  clubId: number,
  matchId: string,
  memberId: number,
  updates: {
    role?: 'starter' | 'substitute' | 'technical_staff' | 'staff' | 'executive';
    position?: string;
    jersey_number?: number;
  }
): Promise<MatchSquadMember> {
  const { data, error } = await supabase
    .from("match_squad")
    .update(updates)
    .eq("club_id", clubId)
    .eq("match_id", matchId)
    .eq("id", memberId)
    .select(`
      *,
      player:players(id, name, position, number, image)
    `)
    .single();

  if (error) {
    console.error("Erro ao atualizar membro do squad:", error);
    throw new Error(`Erro ao atualizar membro do squad: ${error.message}`);
  }

  // Buscar informações do usuário se necessário
  let enrichedData = data;
  if (data.user_id) {
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("id, email, name")
      .eq("id", data.user_id)
      .single();

    if (!userError && userData) {
      enrichedData = {
        ...data,
        user: userData
      };
    }
  }

  return enrichedData;
}

/**
 * Busca todas as substituições de uma partida
 */
export async function getMatchSubstitutions(clubId: number, matchId: string): Promise<MatchSubstitution[]> {
  const { data, error } = await supabase
    .from("match_substitutions")
    .select(`
      *,
      player_out:players!match_substitutions_player_out_id_fkey(id, name, number),
      player_in:players!match_substitutions_player_in_id_fkey(id, name, number)
    `)
    .eq("club_id", clubId)
    .eq("match_id", matchId)
    .order("minute", { ascending: true });

  if (error) {
    console.error("Erro ao buscar substituições da partida:", error);
    throw new Error(`Erro ao buscar substituições da partida: ${error.message}`);
  }

  return data || [];
}

/**
 * Registra uma substituição
 */
export async function createMatchSubstitution(
  clubId: number,
  matchId: string,
  substitution: {
    player_out_id: string;
    player_in_id: string;
    minute: number;
    reason?: string;
  }
): Promise<MatchSubstitution> {
  const { data, error } = await supabase
    .from("match_substitutions")
    .insert({
      club_id: clubId,
      match_id: matchId,
      ...substitution
    })
    .select(`
      *,
      player_out:players!match_substitutions_player_out_id_fkey(id, name, number),
      player_in:players!match_substitutions_player_in_id_fkey(id, name, number)
    `)
    .single();

  if (error) {
    console.error("Erro ao criar substituição:", error);
    throw new Error(`Erro ao criar substituição: ${error.message}`);
  }

  return data;
}

/**
 * Busca os minutos jogados por todos os jogadores de uma partida
 */
export async function getMatchPlayerMinutes(clubId: number, matchId: string): Promise<MatchPlayerMinutes[]> {
  const { data, error } = await supabase
    .from("match_player_minutes")
    .select(`
      *,
      player:players(id, name, number, position)
    `)
    .eq("club_id", clubId)
    .eq("match_id", matchId)
    .order("minutes_played", { ascending: false });

  if (error) {
    console.error("Erro ao buscar minutos dos jogadores:", error);
    throw new Error(`Erro ao buscar minutos dos jogadores: ${error.message}`);
  }

  return data || [];
}

/**
 * Salva manualmente os minutos jogados de um jogador em uma partida
 */
export async function saveMatchPlayerMinutes(
  clubId: number,
  matchId: string,
  playerId: string,
  minutesPlayed: number,
  started: boolean
): Promise<MatchPlayerMinutes> {
  const { data, error } = await supabase
    .from("match_player_minutes")
    .upsert(
      {
        club_id: clubId,
        match_id: matchId,
        player_id: playerId,
        minutes_played: minutesPlayed,
        started,
      },
      {
        onConflict: "club_id, match_id, player_id",
      }
    )
    .select(
      `*, player:players(id, name, number, position)`
    )
    .single();

  if (error) {
    console.error("Erro ao salvar minutos do jogador:", error);
    throw new Error(`Erro ao salvar minutos do jogador: ${error.message}`);
  }

  return data as MatchPlayerMinutes;
}

/**
 * Finaliza uma partida e calcula os minutos finais de todos os jogadores
 */
export async function finalizeMatchMinutes(
  clubId: number,
  matchId: string,
  matchDuration: number = 90
): Promise<void> {
  // Buscar todos os jogadores que participaram da partida
  const squad = await getMatchSquad(clubId, matchId);
  const starters = squad.filter(m => m.role === 'starter' && m.player_id);
  const substitutes = squad.filter(m => m.role === 'substitute' && m.player_id);

  // Calcular minutos para jogadores titulares
  for (const starter of starters) {
    if (starter.player_id) {
      const { error } = await supabase.rpc('calculate_player_minutes', {
        p_club_id: clubId,
        p_match_id: matchId,
        p_player_id: starter.player_id,
        p_match_duration: matchDuration
      });

      if (error) {
        console.error("Erro ao calcular minutos do jogador:", error);
      }
    }
  }

  // Atualizar minutos dos substitutos que entraram
  const substitutions = await getMatchSubstitutions(clubId, matchId);
  for (const sub of substitutions) {
    const { error } = await supabase.rpc('calculate_player_minutes', {
      p_club_id: clubId,
      p_match_id: matchId,
      p_player_id: sub.player_in_id,
      p_match_duration: matchDuration
    });

    if (error) {
      console.error("Erro ao calcular minutos do substituto:", error);
    }
  }
}

/**
 * Busca jogadores disponíveis para escalação (da categoria da partida)
 */
export async function getAvailablePlayersForMatch(
  clubId: number,
  matchId: string
): Promise<any[]> {
  // Primeiro buscar a categoria da partida
  const { data: match, error: matchError } = await supabase
    .from("matches")
    .select("category_id")
    .eq("id", matchId)
    .eq("club_id", clubId)
    .single();

  if (matchError || !match?.category_id) {
    console.error("Erro ao buscar categoria da partida:", matchError);
    throw new Error("Não foi possível determinar a categoria da partida");
  }

  // Buscar jogadores da categoria
  const { data: playerCategories, error: pcError } = await supabase
    .from("player_categories")
    .select("player_id")
    .eq("club_id", clubId)
    .eq("category_id", match.category_id);

  if (pcError) {
    console.error("Erro ao buscar jogadores da categoria:", pcError);
    throw new Error(`Erro ao buscar jogadores da categoria: ${pcError.message}`);
  }

  if (!playerCategories || playerCategories.length === 0) {
    return [];
  }

  const playerIds = playerCategories.map(pc => pc.player_id);

  // Buscar dados completos dos jogadores
  const { data: players, error: playersError } = await supabase
    .from("players")
    .select("*")
    .eq("club_id", clubId)
    .in("id", playerIds)
    .neq("status", "inativo")
    .order("name");

  if (playersError) {
    console.error("Erro ao buscar dados dos jogadores:", playersError);
    throw new Error(`Erro ao buscar dados dos jogadores: ${playersError.message}`);
  }

  return players || [];
}

/**
 * Sincroniza a escalação com o squad da partida
 * Adiciona automaticamente jogadores titulares ao squad
 */
async function syncLineupWithSquad(
  clubId: number,
  matchId: string,
  lineup: Record<string, string | null>
): Promise<void> {
  try {
    // Buscar squad atual da partida
    const currentSquad = await getMatchSquad(clubId, matchId);

    // Extrair IDs dos jogadores titulares da escalação
    const starterPlayerIds = Object.values(lineup).filter(id => id !== null) as string[];

    // Encontrar jogadores que estão na escalação mas não no squad como titulares
    const currentStarters = currentSquad.filter(member => member.role === 'starter');
    const currentStarterIds = currentStarters.map(member => member.player_id).filter(Boolean) as string[];

    // Remover jogadores que não estão mais na escalação
    for (const member of currentStarters) {
      if (member.player_id && !starterPlayerIds.includes(member.player_id)) {
        await removeMatchSquadMember(clubId, matchId, member.id);
      }
    }

    // Adicionar novos jogadores titulares
    for (const playerId of starterPlayerIds) {
      if (!currentStarterIds.includes(playerId)) {
        // Verificar se o jogador já está no squad com outro papel
        const existingMember = currentSquad.find(member => member.player_id === playerId);

        if (existingMember) {
          // Atualizar papel para titular
          await updateMatchSquadMember(clubId, matchId, existingMember.id, {
            role: 'starter'
          });
        } else {
          // Adicionar como novo titular
          await addMatchSquadMember(clubId, matchId, {
            player_id: playerId,
            role: 'starter'
          });
        }
      }
    }

    // Atualizar posições dos titulares
    for (const [position, playerId] of Object.entries(lineup)) {
      if (playerId) {
        const member = currentSquad.find(m => m.player_id === playerId && m.role === 'starter');
        if (member && member.position !== position) {
          await updateMatchSquadMember(clubId, matchId, member.id, {
            position: position
          });
        }
      }
    }

  } catch (error) {
    console.error("Erro ao sincronizar escalação com squad:", error);
    // Não falhar a operação principal se a sincronização falhar
  }
}

/**
 * Sincroniza automaticamente com a convocação da partida
 * Adiciona jogadores e colaboradores do squad à convocação se ela existir
 */
async function syncWithCallup(clubId: number, matchId: string): Promise<void> {
  try {
    // Buscar a partida para obter informações da convocação
    const { data: match, error: matchError } = await supabase
      .from("matches")
      .select("id, date, category_id")
      .eq("id", matchId)
      .single();

    if (matchError || !match) {
      console.log("Partida não encontrada para sincronização com convocação");
      return;
    }

    // Buscar convocação relacionada à partida (baseada na data e categoria)
    const matchDate = new Date(match.date);
    const startOfDay = new Date(matchDate);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(matchDate);
    endOfDay.setHours(23, 59, 59, 999);

    let { data: callups, error: callupError } = await supabase
      .from("callups")
      .select("id, category_id, match_date")
      .eq("club_id", clubId)
      .gte("match_date", startOfDay.toISOString())
      .lte("match_date", endOfDay.toISOString());

    if (callupError) {
      console.error("Erro ao buscar convocações:", callupError);
      return;
    }

    if (!callups || callups.length === 0) {
      // Se não encontrou na data exata, buscar convocações próximas (±3 dias)
      const threeDaysBefore = new Date(matchDate);
      threeDaysBefore.setDate(threeDaysBefore.getDate() - 3);
      const threeDaysAfter = new Date(matchDate);
      threeDaysAfter.setDate(threeDaysAfter.getDate() + 3);

      const { data: nearbyCallups, error: nearbyError } = await supabase
        .from("callups")
        .select("id, category_id, match_date")
        .eq("club_id", clubId)
        .gte("match_date", threeDaysBefore.toISOString())
        .lte("match_date", threeDaysAfter.toISOString());

      if (nearbyError || !nearbyCallups || nearbyCallups.length === 0) {
        console.log("Nenhuma convocação encontrada para sincronização");
        return;
      }

      callups = nearbyCallups;
    }

    // Encontrar convocação da mesma categoria ou a primeira disponível
    let targetCallup = callups.find(c => c.category_id === match.category_id);
    if (!targetCallup) {
      targetCallup = callups[0];
    }

    // Buscar squad atual da partida
    const squad = await getMatchSquad(clubId, matchId);

    // Buscar jogadores já convocados para evitar duplicações
    const { data: existingCallupPlayers, error: existingError } = await supabase
      .from("callup_players")
      .select("player_id, user_id, name")
      .eq("club_id", clubId)
      .eq("callup_id", targetCallup.id);

    if (existingError) {
      console.error("Erro ao buscar jogadores já convocados:", existingError);
      return;
    }

    const existingPlayerIds = existingCallupPlayers?.map((p: any) => p.player_id).filter(Boolean) || [];
    const existingUserIds = existingCallupPlayers?.map((p: any) => p.user_id).filter(Boolean) || [];
    const existingNames = existingCallupPlayers?.map((p: any) => p.name).filter(Boolean) || [];

    // Adicionar jogadores do squad à convocação
    for (const member of squad) {
      try {
        if (member.player_id && !existingPlayerIds.includes(member.player_id)) {
          // Todos os jogadores (titulares e reservas) são "Atleta" na convocação
          const callupRole = "Atleta";



          await addPlayerToCallup(
            clubId,
            targetCallup.id,
            member.player_id, // playerId
            callupRole, // role
            undefined, // currentUserId (sem verificação de permissão)
            undefined, // userId
            undefined, // userName
            member.player?.name // playerName
          );
        } else if (member.user_id || member.collaborator_id) {
          // Adicionar colaboradores com papéis específicos
          let callupRole = "Roupeiro"; // Papel padrão para staff
          if (member.role === 'technical_staff') {
            callupRole = "Técnico"; // Papel padrão para comissão técnica
          } else if (member.role === 'executive') {
            callupRole = "Diretor"; // Papel padrão para diretoria
          }

          // Determinar nome e ID para a convocação
          let userName = "Colaborador";
          let userId: string | undefined = member.user_id ?? undefined;

          if (member.collaborator) {
            userName = member.collaborator.full_name;
            // Se o colaborador tem user_id, usar ele, senão usar o ID do colaborador como string
            userId = member.collaborator.user_id || member.collaborator_id?.toString();
          } else if (member.user?.email) {
            userName = member.user.email;
          }

          const userIdStr = userId ? String(userId) : undefined;
          const isUuid = !!userIdStr && /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(userIdStr);

          const alreadyAdded = isUuid
            ? existingUserIds.includes(userIdStr)
            : existingNames.includes(userName);

          if (!alreadyAdded) {
            await addPlayerToCallup(
              clubId,
              targetCallup.id,
              undefined, // playerId
              callupRole, // role
              undefined, // currentUserId (sem verificação de permissão)
              userIdStr, // userId
              userName, // userName
              undefined // playerName
            );

            if (isUuid && userIdStr) {
              existingUserIds.push(userIdStr);
            } else {
              existingNames.push(userName);
            }
          }
        }
      } catch (error) {
        console.error(`Erro ao adicionar membro ${member.id} à convocação:`, error);
        // Continuar com os próximos membros mesmo se um falhar
      }
    }

    console.log(`Sincronização com convocação ${targetCallup.id} concluída`);
  } catch (error) {
    console.error("Erro ao sincronizar com convocação:", error);
    // Não falhar a operação principal se a sincronização falhar
  }
}
