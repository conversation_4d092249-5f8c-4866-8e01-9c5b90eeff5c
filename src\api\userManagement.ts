import { supabase } from "@/api/supabaseClient";

/**
 * Exclui um usuário do sistema
 * Esta função chama a Edge Function delete-user para excluir o usuário
 * 
 * @param userId ID do usuário a ser excluído
 * @returns Objeto com sucesso e mensagem
 */
export async function deleteUser(userId: string): Promise<{ success: boolean; message: string }> {
  try {
    // Obter token de autenticação do usuário atual
    const { data: { session } } = await supabase.auth.getSession();
    const token = session?.access_token;
    
    if (!token) {
      return {
        success: false,
        message: "Não autorizado: usuário não está logado"
      };
    }
    
    // Chamar a Edge Function para excluir o usuário
    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://qoujacltecwxvymynbsh.supabase.co';
    const response = await fetch(`${supabaseUrl}/functions/v1/delete-user`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        userId
      })
    });
    
    const result = await response.json();
    
    if (!response.ok) {
      console.error("Erro na resposta da Edge Function:", result);
      return { 
        success: false, 
        message: result.error || `Erro ao excluir usuário: ${response.statusText}` 
      };
    }
    
    return { 
      success: true, 
      message: result.message || "Usuário excluído com sucesso" 
    };
  } catch (error) {
    console.error("Erro ao excluir usuário:", error);
    return { 
      success: false, 
      message: error instanceof Error ? error.message : "Erro desconhecido" 
    };
  }
}


/**
 * Atualiza a senha de um usuário
 * Esta função chama a Edge Function change-user-password
 *
 * @param userId ID do usuário
 * @param password Nova senha
 * @returns Objeto com sucesso e mensagem
 */
export async function updateUserPassword(userId: string, password: string): Promise<{ success: boolean; message: string }> {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    const token = session?.access_token;

    if (!token) {
      return { success: false, message: "Não autorizado: usuário não está logado" };
    }

    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://qoujacltecwxvymynbsh.supabase.co';
    const response = await fetch(`${supabaseUrl}/functions/v1/dynamic-function`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ userId, password })
    });

    const result = await response.json();

    if (!response.ok) {
      console.error("Erro na resposta da Edge Function:", result);
      return { success: false, message: result.error || `Erro ao atualizar senha: ${response.statusText}` };
    }

    return { success: true, message: result.message || 'Senha atualizada com sucesso' };
  } catch (error) {
    console.error('Erro ao atualizar senha:', error);
    return { success: false, message: error instanceof Error ? error.message : 'Erro desconhecido' };
  }
}