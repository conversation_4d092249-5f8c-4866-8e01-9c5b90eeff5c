import { ReactNode, useEffect, useState } from "react";
import { Navigate } from "react-router-dom";
import { usePermission } from "@/hooks/usePermission";
import { isAuthenticated } from "@/utils/auth";

interface MappingPermissionGuardProps {
  children: ReactNode;
  fallbackPath?: string;
}

/**
 * Componente para proteger a página de mapeamento com regras específicas:
 * - President: Sempre tem acesso
 * - Admin: Sempre tem acesso
 * - Colaborador: Tem acesso apenas se tiver a permissão "mapping.view"
 * - Jogador: Tem acesso apenas para visualizar o mapeamento da sua categoria
 */
export function MappingPermissionGuard({ 
  children, 
  fallbackPath = "/dashboard"
}: MappingPermissionGuardProps) {
  const { isLoaded, can, role } = usePermission();
  const [hasAccess, setHasAccess] = useState(false);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    // Verificar se o usuário está autenticado
    if (!isAuthenticated()) {
      setHasAccess(false);
      setLoading(false);
      return;
    }
    
    // Aguardar o carregamento das permissões
    if (!isLoaded) {
      return;
    }
    
    // Aplicar regras específicas de acesso
    let hasPermission = false;
    
    switch (role) {
      case "president":
        // Presidente sempre tem acesso
        hasPermission = true;
        console.log("[MappingPermissionGuard] Acesso concedido: Usuário é presidente");
        break;
        
      case "admin":
        // Admin sempre tem acesso
        hasPermission = true;
        console.log("[MappingPermissionGuard] Acesso concedido: Usuário é administrador");
        break;
        
      case "collaborator":
        // Colaborador precisa ter a permissão específica
        hasPermission = can("mapping.view");
        console.log(`[MappingPermissionGuard] Colaborador - Permissão mapping.view: ${hasPermission}`);
        break;
        
      case "player":
        // Jogador tem acesso apenas para visualizar o mapeamento da sua categoria
        hasPermission = true;
        console.log("[MappingPermissionGuard] Acesso concedido: Jogador pode visualizar mapeamento da sua categoria");
        break;
        
      case "medical":
        // Médico não tem acesso por padrão, mas pode ter se tiver a permissão
        hasPermission = can("mapping.view");
        console.log(`[MappingPermissionGuard] Médico - Permissão mapping.view: ${hasPermission}`);
        break;
        
      default:
        // Outros tipos de usuário precisam da permissão específica
        hasPermission = can("mapping.view");
        console.log(`[MappingPermissionGuard] Usuário tipo '${role}' - Permissão mapping.view: ${hasPermission}`);
        break;
    }
    
    setHasAccess(hasPermission);
    setLoading(false);
  }, [isLoaded, can, role]);
  
  // Enquanto estiver carregando, mostrar indicador de carregamento
  if (loading || !isLoaded) {
    return (
      <div className="flex justify-center items-center h-80">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-team-blue"></div>
      </div>
    );
  }
  
  // Se não tiver acesso, redirecionar
  if (!hasAccess) {
    console.log(`[MappingPermissionGuard] Redirecionando usuário sem acesso para ${fallbackPath}`);
    return <Navigate to={fallbackPath} replace />;
  }
  
  // Se tiver acesso, renderizar o conteúdo
  return <>{children}</>;
}
