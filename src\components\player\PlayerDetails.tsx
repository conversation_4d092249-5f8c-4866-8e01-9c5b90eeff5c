import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Player } from "@/api/api";
import { PlayerOccurrences } from "./PlayerOccurrences";

interface PlayerDetailsProps {
  player: Player;
}

export function PlayerDetails({ player }: PlayerDetailsProps) {
  const [activeTab, setActiveTab] = useState("basic");

  return (
    <div className="flex-1 overflow-y-auto min-h-0">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
        <TabsList className="grid grid-cols-7 mb-4 bg-muted/50 flex-shrink-0">
          <TabsTrigger value="basic" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
            Dados Básicos
          </TabsTrigger>
          <TabsTrigger value="category" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
            Contrato
          </TabsTrigger>
          <TabsTrigger value="documents" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
            Documentos
          </TabsTrigger>
          <TabsTrigger value="contact" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
            Contato
          </TabsTrigger>
          <TabsTrigger value="accommodation" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
            Alojamento
          </TabsTrigger>

        </TabsList>

        <TabsContent value="basic" className="space-y-4 p-1">
          {/* Conteúdo da aba Dados Básicos */}
        </TabsContent>

        <TabsContent value="category" className="space-y-4 p-1">
          {/* Conteúdo da aba Contrato */}
        </TabsContent>

        <TabsContent value="documents" className="space-y-4 p-1">
          {/* Conteúdo da aba Documentos */}
        </TabsContent>

        <TabsContent value="contact" className="space-y-4 p-1">
          {/* Conteúdo da aba Contato */}
        </TabsContent>

        <TabsContent value="accommodation" className="space-y-4 p-1">
          {/* Conteúdo da aba Alojamento */}
        </TabsContent>

        <TabsContent value="occurrences" className="space-y-4 p-1">
          <PlayerOccurrences playerId={player.id} clubId={player.club_id} />
        </TabsContent>

        <TabsContent value="medical" className="space-y-4 p-1">
          {/* Conteúdo da aba Médico */}
        </TabsContent>
      </Tabs>
    </div>
  );
} 