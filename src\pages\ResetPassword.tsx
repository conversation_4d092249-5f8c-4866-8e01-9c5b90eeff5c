import { useState } from "react";
import { <PERSON>, Card<PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON>le, CardDescription } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { requestPasswordReset } from "@/api/auth";

export default function ResetPassword() {
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState("");
  const [error, setError] = useState("");

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    setLoading(true);
    setError("");
    try {
      await requestPasswordReset(email);
      setMessage("Enviamos um email com instru\u00e7\u00f5es para redefinir sua senha.");
    } catch (err) {
      console.error("Erro ao solicitar redefini\u00e7\u00e3o:", err);
      setError("N\u00e3o foi poss\u00edvel enviar o email de redefini\u00e7\u00e3o.");
    } finally {
      setLoading(false);
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-muted">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Redefinir Senha</CardTitle>
          <CardDescription>Informe seu email para receber um link de redefini\u00e7\u00e3o.</CardDescription>
        </CardHeader>
        <CardContent>
          {message ? (
            <p className="text-sm text-green-600">{message}</p>
          ) : (
            <form className="space-y-4" onSubmit={handleSubmit}>
              <Input
                type="email"
                value={email}
                onChange={e => setEmail(e.target.value)}
                required
                placeholder="<EMAIL>"
              />
              {error && <p className="text-sm text-red-600">{error}</p>}
              <Button type="submit" className="w-full" disabled={loading}>
                {loading ? "Enviando..." : "Enviar"}
              </Button>
            </form>
          )}
        </CardContent>
      </Card>
    </div>
  );
}