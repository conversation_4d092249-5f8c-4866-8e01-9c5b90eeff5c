-- Atualiza permissões de usuários e membros com role medical
UPDATE club_members
SET permissions = COALESCE(permissions, '{}'::jsonb) || '{"medical.availability.view": true, "medical.availability.create": true, "medical.availability.edit": true, "medical.availability.delete": true}'::jsonb
WHERE role = 'medical';

UPDATE users
SET permissions = COALESCE(permissions, '{}'::jsonb) || '{"medical.availability.view": true, "medical.availability.create": true, "medical.availability.edit": true, "medical.availability.delete": true}'::jsonb
WHERE role = 'medical';
