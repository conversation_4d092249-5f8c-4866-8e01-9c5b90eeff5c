import { useState } from "react";
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON>nt, 
  <PERSON><PERSON><PERSON><PERSON>, 
  Card<PERSON>itle, 
  CardDescription 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { toast } from "@/components/ui/use-toast";
import { 
  Plus, 
  Pencil, 
  Trash2, 
  Building, 
  Phone, 
  Mail, 
  Calendar, 
  MapPin, 
  Search,
  CreditCard
} from "lucide-react";
import { Supplier, deleteSupplier } from "@/api/api";
import { useUser } from "@/context/UserContext";
import { NovoFornecedorDialog } from "@/components/administrativo/NovoFornecedorDialog";
import { EditarFornecedorDialog } from "@/components/administrativo/EditarFornecedorDialog";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";
import { Input } from "@/components/ui/input";
import { formatDate } from "@/lib/utils";
import { usePermission } from "@/hooks/usePermission";
import { SUPPLIER_PERMISSIONS } from "@/constants/permissions";

interface FornecedoresTabProps {
  suppliers: Supplier[];
  loading: boolean;
  error: string | null;
  clubId: number;
  onRefresh: () => void;
}

export function FornecedoresTab({ 
  suppliers, 
  loading, 
  error, 
  clubId,
  onRefresh 
}: FornecedoresTabProps) {
  const [novoFornecedorDialogOpen, setNovoFornecedorDialogOpen] = useState(false);
  const [editarFornecedorDialogOpen, setEditarFornecedorDialogOpen] = useState(false);
  const [excluirFornecedorDialogOpen, setExcluirFornecedorDialogOpen] = useState(false);
  const [fornecedorSelecionado, setFornecedorSelecionado] = useState<Supplier | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const { user } = useUser();
  const { can } = usePermission();

  const canCreate = can(SUPPLIER_PERMISSIONS.CREATE);
  const canEdit = can(SUPPLIER_PERMISSIONS.EDIT);
  const canDelete = can(SUPPLIER_PERMISSIONS.DELETE);

  // Filtrar fornecedores com base no termo de busca
  const filteredSuppliers = suppliers.filter(
    (fornecedor) =>
      fornecedor.company_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (fornecedor.city && fornecedor.city.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (fornecedor.email && fornecedor.email.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Função para excluir um fornecedor
  const handleDeleteSupplier = async () => {
    if (!fornecedorSelecionado || !user) return;

    try {
      await deleteSupplier(clubId, user.id, fornecedorSelecionado.id);
      
      toast({
        title: "Sucesso",
        description: "Fornecedor excluído com sucesso",
      });
      
      setExcluirFornecedorDialogOpen(false);
      onRefresh();
    } catch (err: any) {
      console.error("Erro ao excluir fornecedor:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao excluir fornecedor",
        variant: "destructive",
      });
    }
  };

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Fornecedores</CardTitle>
          <CardDescription>
            Gerenciar fornecedores do clube
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-red-500">Erro ao carregar fornecedores: {error}</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Fornecedores</CardTitle>
          <CardDescription>
            Gerenciar fornecedores do clube
          </CardDescription>
        </div>
        {canCreate && (
          <Button
            onClick={() => {
              setFornecedorSelecionado(null);
              setNovoFornecedorDialogOpen(true);
            }}
          >
            <Plus className="h-4 w-4 mr-2" />
            Novo Fornecedor
          </Button>
        )}
      </CardHeader>
      <CardContent>
        <div className="mb-4 relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Buscar fornecedores..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        {loading ? (
          <div className="text-center py-4">Carregando fornecedores...</div>
        ) : filteredSuppliers.length === 0 ? (
          <div className="text-center py-4 text-muted-foreground">
            {searchTerm ? "Nenhum fornecedor encontrado com esse termo." : "Nenhum fornecedor cadastrado."}
          </div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Empresa</TableHead>
                  <TableHead>Localização</TableHead>
                  <TableHead>Contato</TableHead>
                  <TableHead>Vencimento</TableHead>
                  <TableHead>Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredSuppliers.map((fornecedor) => (
                  <TableRow key={fornecedor.id}>
                    <TableCell className="font-medium">
                      <div className="flex items-center">
                        <Building className="h-4 w-4 mr-2 text-muted-foreground" />
                        {fornecedor.company_name}
                      </div>
                    </TableCell>
                    <TableCell>
                      {(fornecedor.city || fornecedor.state) && (
                        <div className="flex items-center text-sm">
                          <MapPin className="h-3 w-3 mr-1 text-muted-foreground" />
                          {[fornecedor.city, fornecedor.state].filter(Boolean).join(", ")}
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      {fornecedor.phone1 && (
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Phone className="h-3 w-3 mr-1" />
                          {fornecedor.phone1}
                        </div>
                      )}
                      {fornecedor.email && (
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Mail className="h-3 w-3 mr-1" />
                          {fornecedor.email}
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      {fornecedor.expiration_date && (
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1 text-muted-foreground" />
                          {formatDate(fornecedor.expiration_date)}
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        {canEdit && (
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => {
                              setFornecedorSelecionado(fornecedor);
                              setEditarFornecedorDialogOpen(true);
                            }}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                        )}
                        {canDelete && (
                          <Button
                            variant="ghost"
                            size="icon"
                            className="text-red-500 hover:text-red-700 hover:bg-red-50"
                            onClick={() => {
                              setFornecedorSelecionado(fornecedor);
                              setExcluirFornecedorDialogOpen(true);
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>

      {/* Diálogos */}
      {canCreate && (
        <NovoFornecedorDialog
          open={novoFornecedorDialogOpen}
          onOpenChange={setNovoFornecedorDialogOpen}
          clubId={clubId}
          onSuccess={onRefresh}
        />
      )}

      {fornecedorSelecionado && (
        <>
          {canEdit && (
            <EditarFornecedorDialog
              open={editarFornecedorDialogOpen}
              onOpenChange={setEditarFornecedorDialogOpen}
              clubId={clubId}
              supplier={fornecedorSelecionado}
              onSuccess={onRefresh}
            />
          )}

          {canDelete && (
            <ConfirmDialog
              open={excluirFornecedorDialogOpen}
              onOpenChange={setExcluirFornecedorDialogOpen}
              title="Excluir Fornecedor"
              description={`Tem certeza que deseja excluir o fornecedor ${fornecedorSelecionado.company_name}? Esta ação não pode ser desfeita.`}
              onConfirm={handleDeleteSupplier}
            />
          )}
        </>
      )}
    </Card>
  );
}
