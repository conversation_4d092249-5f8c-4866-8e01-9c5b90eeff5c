-- Atualiza permissões dos jogadores para permitir criar solicitações de estoque
UPDATE club_members
SET permissions = '{
  "dashboard.view": true,
  "players.view_own": true,
  "players.edit_own": true,
  "inventory.requests.create": true,
  "inventory.requests.edit": true
}'::jsonb
WHERE role = 'player';

UPDATE users
SET permissions = '{
  "dashboard.view": true,
  "players.view_own": true,
  "players.edit_own": true,
  "inventory.requests.create": true,
  "inventory.requests.edit": true
}'::jsonb
WHERE role = 'player';

INSERT INTO audit_logs (club_id, user_id, action, details, success)
SELECT
    cm.club_id,
    cm.user_id,
    'system.update_player_permissions',
    jsonb_build_object(
        'role', 'player',
        'permissions', '{"inventory.requests.create": true, "inventory.requests.edit": true}'::jsonb
    ),
    true
FROM club_members cm
WHERE cm.role = 'player';

DO $$
BEGIN
    RAISE NOTICE 'Permissões de jogadores atualizadas para solicitações de estoque';
END $$;
