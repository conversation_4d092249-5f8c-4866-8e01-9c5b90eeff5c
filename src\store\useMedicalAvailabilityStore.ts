import { create } from "zustand";
import {
  MedicalAvailability,
  getMedicalAvailability,
  createMedicalAvailability,
  updateMedicalAvailability,
  deleteMedicalAvailability
} from "@/api/api";

interface MedicalAvailabilityState {
  availabilities: MedicalAvailability[];
  loading: boolean;
  error: string | null;
  fetchAvailability: (
    clubId: number,
    professionalId: number,
    startDate?: string,
    endDate?: string
  ) => Promise<void>;
  addAvailability: (
    clubId: number,
    userId: string,
    availability: Omit<MedicalAvailability, "id" | "club_id" | "booked" | "created_at" | "updated_at">
  ) => Promise<void>;
  updateAvailability: (
    clubId: number,
    userId: string,
    id: number,
    availability: Partial<Omit<MedicalAvailability, "id" | "club_id" | "created_at" | "updated_at" | "booked">>
  ) => Promise<void>;
  deleteAvailability: (clubId: number, userId: string, id: number) => Promise<void>;
}

export const useMedicalAvailabilityStore = create<MedicalAvailabilityState>((set) => ({
  availabilities: [],
  loading: false,
  error: null,

  fetchAvailability: async (clubId, professionalId, startDate, endDate) => {
    set({ loading: true, error: null });
    try {
      const data = await getMedicalAvailability(clubId, professionalId, startDate, endDate);
      const today = new Date().toISOString().slice(0, 10);
      const filtered = data.filter(a => a.date >= today);
      set({ availabilities: filtered, loading: false });
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao buscar disponibilidade", loading: false });
    }
  },

  addAvailability: async (clubId, userId, availability) => {
    set({ loading: true, error: null });
    try {
      const newItem = await createMedicalAvailability(clubId, userId, availability);
      set((state) => ({ availabilities: [...state.availabilities, newItem], loading: false }));
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao adicionar disponibilidade", loading: false });
    }
  },

  updateAvailability: async (clubId, userId, id, availability) => {
    set({ loading: true, error: null });
    try {
      const updated = await updateMedicalAvailability(clubId, userId, id, availability);
      set((state) => ({ availabilities: state.availabilities.map(a => a.id === id ? updated : a), loading: false }));
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao atualizar disponibilidade", loading: false });
    }
  },

  deleteAvailability: async (clubId, userId, id) => {
    set({ loading: true, error: null });
    try {
      await deleteMedicalAvailability(clubId, userId, id);
      set((state) => ({ availabilities: state.availabilities.filter(a => a.id !== id), loading: false }));
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao excluir disponibilidade", loading: false });
    }
  }
}));
