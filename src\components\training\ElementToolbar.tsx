import React from 'react';
import { useDrag } from 'react-dnd';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
  Target,
  Circle,
  Square,
  Users,
  MapPin,
  Type,
  Palette,
  Move3D
} from 'lucide-react';
import { TrainingElement } from './InteractiveTrainingBuilder';

interface ElementToolbarProps {
  onAddElement: (element: Omit<TrainingElement, 'id'>) => void;
  activeTool: string | null;
  onToolChange: (tool: string | null) => void;
}

interface DraggableToolProps {
  type: string;
  icon: React.ReactNode;
  label: string;
  color?: string;
  properties?: any;
  isActive?: boolean;
  onClick?: () => void;
}

function DraggableTool({ type, icon, label, color, properties = {}, isActive, onClick }: DraggableToolProps) {
  const [{ isDragging }, drag] = useDrag({
    type: type,
    item: { type, properties },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  return (
    <div
      ref={drag}
      className={`
        flex flex-col items-center p-3 rounded-lg border-2 border-dashed cursor-grab
        transition-all duration-200 hover:scale-105 hover:shadow-md
        ${isDragging ? 'opacity-50 scale-95' : 'opacity-100'}
        ${isActive ? 'border-primary bg-primary/10' : 'border-muted-foreground/30 hover:border-primary/50'}
      `}
      onClick={onClick}
      style={{ borderColor: color }}
    >
      <div className={`p-2 rounded-full ${isActive ? 'bg-primary text-white' : 'bg-muted'}`}>
        {icon}
      </div>
      <span className="text-xs font-medium mt-1 text-center">{label}</span>
    </div>
  );
}

export function ElementToolbar({ onAddElement, activeTool, onToolChange }: ElementToolbarProps) {
  const handleToolClick = (toolType: string) => {
    onToolChange(activeTool === toolType ? null : toolType);
  };

  const coneVariants = [
    { color: '#f97316', label: 'Laranja', size: 'medium' },
    { color: '#ef4444', label: 'Vermelho', size: 'medium' },
    { color: '#3b82f6', label: 'Azul', size: 'medium' },
    { color: '#10b981', label: 'Verde', size: 'medium' },
    { color: '#f59e0b', label: 'Amarelo', size: 'medium' },
  ];

  const goalVariants = [
    { size: 'small', label: 'Pequeno' },
    { size: 'medium', label: 'Médio' },
    { size: 'large', label: 'Grande' },
  ];

  const ballVariants = [
    { size: 'small', label: 'Pequena' },
    { size: 'medium', label: 'Normal' },
    { size: 'large', label: 'Grande' },
  ];

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm flex items-center gap-2">
          <Move3D className="h-4 w-4" />
          Elementos de Treino
        </CardTitle>
        <CardDescription className="text-xs">
          Arraste os elementos para o campo ou clique para adicionar
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Cones */}
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Target className="h-4 w-4 text-orange-500" />
            <span className="text-sm font-medium">Cones</span>
          </div>
          <div className="grid grid-cols-2 gap-2">
            {coneVariants.map((variant, index) => (
              <DraggableTool
                key={`cone-${index}`}
                type="cone"
                icon={<Target className="h-4 w-4" />}
                label={variant.label}
                color={variant.color}
                properties={{ color: variant.color, size: variant.size }}
                isActive={activeTool === `cone-${variant.color}`}
                onClick={() => handleToolClick(`cone-${variant.color}`)}
              />
            ))}
          </div>
        </div>

        <Separator />

        {/* Bolas */}
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Circle className="h-4 w-4 text-white" />
            <span className="text-sm font-medium">Bolas</span>
          </div>
          <div className="grid grid-cols-3 gap-2">
            {ballVariants.map((variant, index) => (
              <DraggableTool
                key={`ball-${index}`}
                type="ball"
                icon={<Circle className="h-4 w-4" />}
                label={variant.label}
                properties={{ size: variant.size }}
                isActive={activeTool === `ball-${variant.size}`}
                onClick={() => handleToolClick(`ball-${variant.size}`)}
              />
            ))}
          </div>
        </div>

        <Separator />

        {/* Gols */}
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Square className="h-4 w-4 text-purple-500" />
            <span className="text-sm font-medium">Gols</span>
          </div>
          <div className="grid grid-cols-3 gap-2">
            {goalVariants.map((variant, index) => (
              <DraggableTool
                key={`goal-${index}`}
                type="goal"
                icon={<Square className="h-4 w-4" />}
                label={variant.label}
                properties={{ size: variant.size }}
                isActive={activeTool === `goal-${variant.size}`}
                onClick={() => handleToolClick(`goal-${variant.size}`)}
              />
            ))}
          </div>
        </div>

        <Separator />

        {/* Jogadores */}
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Users className="h-4 w-4 text-blue-500" />
            <span className="text-sm font-medium">Jogadores</span>
          </div>
          <div className="grid grid-cols-1 gap-2">
            <DraggableTool
              type="player"
              icon={<Users className="h-4 w-4" />}
              label="Adicionar Jogador"
              properties={{ color: '#3b82f6' }}
              isActive={activeTool === 'player'}
              onClick={() => handleToolClick('player')}
            />
          </div>
        </div>

        <Separator />

        {/* Marcadores */}
        <div>
          <div className="flex items-center gap-2 mb-2">
            <MapPin className="h-4 w-4 text-red-500" />
            <span className="text-sm font-medium">Marcadores</span>
          </div>
          <div className="grid grid-cols-2 gap-2">
            <DraggableTool
              type="marker"
              icon={<MapPin className="h-4 w-4" />}
              label="Zona A"
              properties={{ color: '#ef4444', label: 'A' }}
              isActive={activeTool === 'marker-a'}
              onClick={() => handleToolClick('marker-a')}
            />
            <DraggableTool
              type="marker"
              icon={<MapPin className="h-4 w-4" />}
              label="Zona B"
              properties={{ color: '#3b82f6', label: 'B' }}
              isActive={activeTool === 'marker-b'}
              onClick={() => handleToolClick('marker-b')}
            />
          </div>
        </div>

        <Separator />

        {/* Anotações */}
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Type className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium">Anotações</span>
          </div>
          <div className="grid grid-cols-1 gap-2">
            <DraggableTool
              type="annotation"
              icon={<Type className="h-4 w-4" />}
              label="Texto"
              properties={{ text: 'Anotação' }}
              isActive={activeTool === 'annotation'}
              onClick={() => handleToolClick('annotation')}
            />
          </div>
        </div>

        {/* Instruções */}
        <div className="mt-4 p-3 bg-muted/50 rounded-lg">
          <p className="text-xs text-muted-foreground">
            💡 <strong>Dica:</strong> Arraste os elementos diretamente para o campo ou clique para selecionar e depois clique no campo para posicionar.
          </p>
        </div>

        {/* Atalhos de teclado */}
        <div className="space-y-2">
          <h4 className="text-xs font-medium">Atalhos:</h4>
          <div className="space-y-1 text-xs text-muted-foreground">
            <div className="flex justify-between">
              <span>Selecionar múltiplos:</span>
              <Badge variant="outline" className="text-xs">Ctrl + Click</Badge>
            </div>
            <div className="flex justify-between">
              <span>Deletar selecionados:</span>
              <Badge variant="outline" className="text-xs">Delete</Badge>
            </div>
            <div className="flex justify-between">
              <span>Duplicar:</span>
              <Badge variant="outline" className="text-xs">Ctrl + D</Badge>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
