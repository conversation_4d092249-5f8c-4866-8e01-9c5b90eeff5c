import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Search, Filter } from "lucide-react";
import type { Player } from "@/api/api";

interface PlayerSelectorProps {
  players: Player[];
  onPlayerSelect: (player: Player) => void;
  selectedPosition?: string;
  title?: string;
  subtitle?: string;
  initialPositionFilter?: string;
}

export function PlayerSelector({
  players,
  onPlayerSelect,
  selectedPosition,
  title = "Selecionar Jogador",
  subtitle,
  initialPositionFilter = ""
}: PlayerSelectorProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [positionFilter, setPositionFilter] = useState<string>(initialPositionFilter);

  // Atualizar o filtro de posição quando initialPositionFilter mudar
  useEffect(() => {
    setPositionFilter(initialPositionFilter);
  }, [initialPositionFilter]);

  // Filtrar jogadores baseado na busca e filtros
  const filteredPlayers = players.filter(player => {
    const matchesSearch = player.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         player.number.toString().includes(searchTerm);

    const matchesPosition = !positionFilter || player.position === positionFilter;

    return matchesSearch && matchesPosition;
  });

  // Obter posições únicas dos jogadores
  const uniquePositions = Array.from(new Set(players.map(p => p.position))).sort();

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          {title}
          {selectedPosition && (
            <Badge variant="outline">
              {subtitle || `Posição: ${selectedPosition}`}
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Filtros */}
        <div className="flex gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Buscar por nome ou número..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          {uniquePositions.length > 1 && (
            <select
              value={positionFilter}
              onChange={(e) => setPositionFilter(e.target.value)}
              className="border rounded-md px-3 py-2 min-w-[120px]"
            >
              <option value="">Todas posições</option>
              {uniquePositions.map(position => (
                <option key={position} value={position}>
                  {position}
                </option>
              ))}
            </select>
          )}
        </div>

        {/* Lista de jogadores */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-[400px] overflow-y-auto">
          {filteredPlayers.length === 0 ? (
            <div className="col-span-full text-center py-8 text-gray-500">
              {searchTerm || positionFilter ? 
                "Nenhum jogador encontrado com os filtros aplicados" : 
                "Nenhum jogador disponível"
              }
            </div>
          ) : (
            filteredPlayers.map(player => (
              <PlayerCard
                key={player.id}
                player={player}
                onSelect={() => onPlayerSelect(player)}
              />
            ))
          )}
        </div>

        {/* Estatísticas */}
        <div className="text-sm text-gray-500 border-t pt-3">
          Mostrando {filteredPlayers.length} de {players.length} jogadores
        </div>
      </CardContent>
    </Card>
  );
}

interface PlayerCardProps {
  player: Player;
  onSelect: () => void;
}

function PlayerCard({ player, onSelect }: PlayerCardProps) {
  return (
    <div
      className="border rounded-lg p-3 cursor-pointer transition-all hover:shadow-md hover:bg-blue-50 hover:border-blue-300"
      onClick={onSelect}
    >
      <div className="flex items-center gap-3">
        <Avatar className="w-10 h-10">
          <AvatarImage src={player.image || ""} />
          <AvatarFallback className="bg-blue-600 text-white font-bold">
            {player.number || player.name.charAt(0)}
          </AvatarFallback>
        </Avatar>
        
        <div className="flex-1 min-w-0">
          <div className="font-medium truncate">{player.name}</div>
          <div className="text-sm text-gray-500 flex items-center gap-2">
            <span>{player.position}</span>
            <span>•</span>
            <span>#{player.number}</span>
          </div>
          
          {/* Status do jogador */}
          <div className="flex items-center gap-1 mt-1">
            <div className={`w-2 h-2 rounded-full ${getStatusColor(player.status)}`} />
            <span className="text-xs text-gray-500 capitalize">{player.status}</span>
          </div>
        </div>
      </div>
      
      {/* Estatísticas básicas se disponíveis */}
      {player.stats && (
        <div className="mt-2 pt-2 border-t text-xs text-gray-500 grid grid-cols-3 gap-1">
          <div>Jogos: {player.stats.matches || 0}</div>
          <div>Gols: {player.stats.goals || 0}</div>
          <div>Min: {player.stats.minutes || 0}</div>
        </div>
      )}
    </div>
  );
}



// Função para obter a cor do status do jogador
function getStatusColor(status: string): string {
  switch (status) {
    case 'disponível':
      return 'bg-green-500';
    case 'lesionado':
      return 'bg-red-500';
    case 'em recuperação':
      return 'bg-yellow-500';
    case 'suspenso':
      return 'bg-orange-500';
    case 'inativo':
      return 'bg-gray-500';
    default:
      return 'bg-gray-400';
  }
}
