import { supabase } from "@/integrations/supabase/client";
import { withPermission, withAuditLog } from "./middleware";
import { createMedicalNotification } from "./notifications";
import type { Database } from "@/integrations/supabase/types";

// Types
export type MedicalAppointment = {
  id: number;
  club_id: number;
  player_id: string;
  professional_id: number;
  appointment_date: string;
  appointment_time: string;
  duration: number;
  status: AppointmentStatus;
  appointment_type: string;
  location: string;
  notes: string;
  created_at: string;
  updated_at: string;
  created_by?: string;
  notification_sent: boolean;
  player_name?: string;
  professional_name?: string;
};

export type AppointmentStatus =
  | "Agendada"
  | "Em andamento"
  | "Finalizada"
  | "Cancelada";

export const APPOINTMENT_PERMISSIONS = {
  VIEW: "medical.appointments.view",
  CREATE: "medical.appointments.create",
  EDIT: "medical.appointments.edit",
  DELETE: "medical.appointments.delete",
};

/**
 * Get all medical appointments for a club
 * @param clubId The club ID
 * @param filters Optional filters for the query
 * @returns Array of medical appointments
 */
export async function getMedicalAppointments(
  clubId: number,
  filters?: {
    professionalId?: number;
    playerId?: string;
    startDate?: string;
    endDate?: string;
    status?: AppointmentStatus;
  }
): Promise<MedicalAppointment[]> {
  try {
    // First, get the appointments
    let query = supabase
      .from("medical_appointments")
      .select("*")
      .eq("club_id", clubId);

    // Apply filters if provided
    if (filters?.professionalId) {
      query = query.eq("professional_id", filters.professionalId);
    }
    if (filters?.playerId) {
      query = query.eq("player_id", filters.playerId);
    }
    if (filters?.status) {
      query = query.eq("status", filters.status);
    }
    if (filters?.startDate) {
      query = query.gte("appointment_date", filters.startDate);
    }
    if (filters?.endDate) {
      query = query.lte("appointment_date", filters.endDate);
    }

    // Order by date and time
    query = query.order("appointment_date").order("appointment_time");

    const { data, error } = await query;

    if (error) {
      throw new Error(`Error fetching medical appointments: ${error.message}`);
    }

    if (!data || data.length === 0) {
      return [];
    }

    // Get unique player IDs and professional IDs
    const playerIds = [...new Set(data.map(appointment => appointment.player_id))];
    const professionalIds = [...new Set(data.map(appointment => appointment.professional_id))];

    // Get player names
    let playerNames: Record<string, string> = {};
    if (playerIds.length > 0) {
      const { data: playersData, error: playersError } = await supabase
        .from("players")
        .select("id, name")
        .eq("club_id", clubId)
        .in("id", playerIds);

      if (playersError) {
        console.error("Error fetching player names:", playersError);
      } else if (playersData) {
        playerNames = playersData.reduce((acc, player) => {
          acc[player.id] = player.name;
          return acc;
        }, {} as Record<string, string>);
      }
    }

    // Get professional names
    let professionalNames: Record<number, string> = {};
    if (professionalIds.length > 0) {
      const { data: professionalsData, error: professionalsError } = await supabase
        .from("medical_professionals")
        .select("id, name")
        .eq("club_id", clubId)
        .in("id", professionalIds);

      if (professionalsError) {
        console.error("Error fetching professional names:", professionalsError);
      } else if (professionalsData) {
        professionalNames = professionalsData.reduce((acc, professional) => {
          acc[professional.id] = professional.name;
          return acc;
        }, {} as Record<number, string>);
      }
    }

    // Add player and professional names to appointments
    const appointmentsWithNames = data.map(appointment => ({
      ...appointment,
      player_name: playerNames[appointment.player_id] || "",
      professional_name: professionalNames[appointment.professional_id] || ""
    }));

    return appointmentsWithNames as MedicalAppointment[];
  } catch (error: any) {
    console.error("Error in getMedicalAppointments:", error);
    throw new Error(error.message || "Failed to fetch medical appointments");
  }
}

/**
 * Create a new medical appointment
 * @param clubId The club ID
 * @param userId The user ID creating the appointment
 * @param appointment The appointment data
 * @returns The created appointment
 */
export async function createMedicalAppointment(
  clubId: number,
  userId: string,
  appointment: Omit<MedicalAppointment, "id" | "club_id" | "created_at" | "updated_at" | "notification_sent">
): Promise<MedicalAppointment> {
  return withPermission(
    clubId,
    userId,
    APPOINTMENT_PERMISSIONS.CREATE,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "medical.appointment.create",
        {
          player_id: appointment.player_id,
          professional_id: appointment.professional_id,
          appointment_date: appointment.appointment_date,
          appointment_time: appointment.appointment_time
        },
        async () => {
          try {
            // Verificar disponibilidade do profissional
            const { data: slot, error: slotError } = await supabase
              .from("medical_availability")
              .select("*")
              .eq("club_id", clubId)
              .eq("professional_id", appointment.professional_id)
              .eq("date", appointment.appointment_date)
              .eq("start_time", appointment.appointment_time)
              .maybeSingle();

            if (slotError) {
              throw new Error(`Error checking availability: ${slotError.message}`);
            }

            if (!slot || slot.booked >= slot.capacity) {
              throw new Error("Horário indisponível");
            }

            const { data, error } = await supabase
              .from("medical_appointments")
              .insert({
                club_id: clubId,
                ...appointment,
                created_by: userId
              })
              .select()
              .single();

            if (error) {
              throw new Error(`Error creating medical appointment: ${error.message}`);
            }

            const createdAppointment = data as MedicalAppointment;

            // Atualizar contagem de agendamentos do slot
            await supabase
              .from("medical_availability")
              .update({ booked: (slot.booked || 0) + 1 })
              .eq("id", slot.id);

            // Enviar notificação para o médico
            try {
              // Buscar o user_id do profissional médico
              const { data: professional, error: professionalError } = await supabase
                .from("medical_professionals")
                .select("user_id, name")
                .eq("id", appointment.professional_id)
                .eq("club_id", clubId)
                .single();

              if (!professionalError && professional?.user_id) {
                // Buscar o nome do paciente (jogador ou colaborador)
                let patientName = "Paciente";

                // Primeiro tentar buscar como jogador
                const { data: player } = await supabase
                  .from("players")
                  .select("name")
                  .eq("id", appointment.player_id)
                  .eq("club_id", clubId)
                  .single();

                if (player) {
                  patientName = player.name;
                } else {
                  // Se não for jogador, tentar buscar como colaborador
                  const { data: collaborator } = await supabase
                    .from("collaborators")
                    .select("full_name")
                    .eq("id", appointment.player_id)
                    .eq("club_id", clubId)
                    .single();

                  if (collaborator) {
                    patientName = collaborator.full_name;
                  }
                }

                // Criar notificação
                await createMedicalNotification({
                  club_id: clubId,
                  user_id: professional.user_id,
                  title: "Novo agendamento médico",
                  message: `Você tem um novo agendamento de ${appointment.appointment_type} com ${patientName} em ${appointment.appointment_date} às ${appointment.appointment_time}.`,
                  type: "medical_appointment",
                  reference_id: createdAppointment.id.toString(),
                  reference_type: "medical_appointment"
                });
              }
            } catch (notificationError) {
              console.error("Error sending appointment notification:", notificationError);
              // Não falhar a criação do agendamento se a notificação falhar
            }

            return createdAppointment;
          } catch (error: any) {
            console.error("Error in createMedicalAppointment:", error);
            throw new Error(error.message || "Failed to create medical appointment");
          }
        }
      );
    }
  );
}

/**
 * Update an existing medical appointment
 * @param clubId The club ID
 * @param userId The user ID updating the appointment
 * @param id The appointment ID
 * @param appointment The updated appointment data
 * @returns The updated appointment
 */
export async function updateMedicalAppointment(
  clubId: number,
  userId: string,
  id: number,
  appointment: Partial<Omit<MedicalAppointment, "id" | "club_id" | "created_at" | "updated_at">>
): Promise<MedicalAppointment> {
  return withPermission(
    clubId,
    userId,
    APPOINTMENT_PERMISSIONS.EDIT,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "medical.appointment.update",
        { id, ...appointment },
        async () => {
          try {
            const { data, error } = await supabase
              .from("medical_appointments")
              .update({
                ...appointment,
                updated_at: new Date().toISOString()
              })
              .eq("club_id", clubId)
              .eq("id", id)
              .select()
              .single();

            if (error) {
              throw new Error(`Error updating medical appointment: ${error.message}`);
            }

            return data as MedicalAppointment;
          } catch (error: any) {
            console.error("Error in updateMedicalAppointment:", error);
            throw new Error(error.message || "Failed to update medical appointment");
          }
        }
      );
    }
  );
}

/**
 * Delete a medical appointment
 * @param clubId The club ID
 * @param userId The user ID deleting the appointment
 * @param id The appointment ID
 * @returns True if successful
 */
export async function deleteMedicalAppointment(
  clubId: number,
  userId: string,
  id: number
): Promise<boolean> {
  return withPermission(
    clubId,
    userId,
    APPOINTMENT_PERMISSIONS.DELETE,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "medical.appointment.delete",
        { id },
        async () => {
          try {
            // Buscar o agendamento para atualizar a disponibilidade
            const { data: appointment, error: fetchError } = await supabase
              .from("medical_appointments")
              .select("professional_id, appointment_date, appointment_time")
              .eq("club_id", clubId)
              .eq("id", id)
              .single();

            if (fetchError) {
              throw new Error(`Error fetching appointment: ${fetchError.message}`);
            }

            const { error } = await supabase
              .from("medical_appointments")
              .delete()
              .eq("club_id", clubId)
              .eq("id", id);

            if (error) {
              throw new Error(`Error deleting medical appointment: ${error.message}`);
            }

            if (appointment) {
              await supabase
                .from("medical_availability")
                .update({ booked: supabase.literal("booked - 1") })
                .eq("club_id", clubId)
                .eq("professional_id", appointment.professional_id)
                .eq("date", appointment.appointment_date)
                .eq("start_time", appointment.appointment_time);
            }

            return true;
          } catch (error: any) {
            console.error("Error in deleteMedicalAppointment:", error);
            throw new Error(error.message || "Failed to delete medical appointment");
          }
        }
      );
    }
  );
}

/**
 * Update the status of a medical appointment
 * @param clubId The club ID
 * @param userId The user ID updating the status
 * @param id The appointment ID
 * @param status The new status
 * @returns The updated appointment
 */
export async function updateAppointmentStatus(
  clubId: number,
  userId: string,
  id: number,
  status: AppointmentStatus
): Promise<MedicalAppointment> {
  return updateMedicalAppointment(clubId, userId, id, { status });
}

/**
 * Mark an appointment as notification sent
 * @param clubId The club ID
 * @param id The appointment ID
 * @returns True if successful
 */
export async function markAppointmentNotificationSent(
  clubId: number,
  id: number
): Promise<boolean> {
  try {
    const { error } = await supabase
      .from("medical_appointments")
      .update({ notification_sent: true })
      .eq("club_id", clubId)
      .eq("id", id);

    if (error) {
      throw new Error(`Error marking appointment notification as sent: ${error.message}`);
    }

    return true;
  } catch (error: any) {
    console.error("Error in markAppointmentNotificationSent:", error);
    throw new Error(error.message || "Failed to mark appointment notification as sent");
  }
}
