import { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  RefreshCw,
  CheckCircle,
  XCircle,
  Eye,
  Clock,
  User,
  Calendar
} from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { usePermission } from "@/hooks/usePermission";
import {
  getPendingEvaluationsForApproval,
  approveEvaluationAsManager,
  approveEvaluationAsPresident,
  rejectEvaluation,
  PlayerEvaluation
} from "@/api/api";
import { formatDate } from "@/utils/formatters";
import { EvaluationApprovalDialog } from "./EvaluationApprovalDialog";

export function PendingEvaluationsTable() {
  const [evaluations, setEvaluations] = useState<PlayerEvaluation[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedEvaluation, setSelectedEvaluation] = useState<PlayerEvaluation | null>(null);
  const [showApprovalDialog, setShowApprovalDialog] = useState(false);
  const [actionType, setActionType] = useState<"approve" | "reject">("approve");
  const { toast } = useToast();
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const { role } = usePermission();

  const isManager = role === "manager";
  const isPresident = role === "president";
  const isAdmin = role === "admin";
  const canApprove = isManager || isPresident || isAdmin;

  const fetchEvaluations = async () => {
    if (!clubId || !user?.id) return;

    try {
      setLoading(true);
      const data = await getPendingEvaluationsForApproval(clubId, user.id);
      setEvaluations(data);
    } catch (error) {
      console.error("Error fetching pending evaluations:", error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar as avaliações pendentes",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchEvaluations();
  }, [clubId, user?.id]);

  const handleApprove = (evaluation: PlayerEvaluation) => {
    setSelectedEvaluation(evaluation);
    setActionType("approve");
    setShowApprovalDialog(true);
  };

  const handleReject = (evaluation: PlayerEvaluation) => {
    setSelectedEvaluation(evaluation);
    setActionType("reject");
    setShowApprovalDialog(true);
  };

  const handleApprovalSubmit = async (notes: string) => {
    if (!selectedEvaluation || !user?.id || !clubId) return;

    try {
      if (actionType === "approve") {
        if (isManager || isAdmin) {
          await approveEvaluationAsManager(clubId, selectedEvaluation.id, user.id, notes);
          toast({
            title: "Sucesso",
            description: `Avaliação aprovada pelo ${isAdmin ? "administrador" : "gerente"}`,
          });
        } else if (isPresident) {
          await approveEvaluationAsPresident(clubId, selectedEvaluation.id, user.id, notes);
          toast({
            title: "Sucesso",
            description: "Avaliação aprovada pelo presidente",
          });
        }
      } else {
        const rejectorRole = (isManager || isAdmin) ? "manager" : "president";
        await rejectEvaluation(clubId, selectedEvaluation.id, user.id, notes, rejectorRole);
        toast({
          title: "Avaliação rejeitada",
          description: "A avaliação foi rejeitada com sucesso",
        });
      }

      setShowApprovalDialog(false);
      setSelectedEvaluation(null);
      fetchEvaluations(); // Refresh the list
    } catch (error) {
      console.error("Error processing evaluation:", error);
      toast({
        title: "Erro",
        description: "Não foi possível processar a avaliação",
        variant: "destructive",
      });
    }
  };

  const getApprovalStatus = (evaluation: PlayerEvaluation) => {
    const managerApproved = !!evaluation.approved_by_manager;
    const presidentApproved = !!evaluation.approved_by_president;
    const requiresManager = evaluation.requires_manager_approval;
    const requiresPresident = evaluation.requires_president_approval;

    if (requiresManager && requiresPresident) {
      if (managerApproved && presidentApproved) {
        return <Badge variant="default" className="bg-green-500">Totalmente Aprovada</Badge>;
      } else if (managerApproved) {
        return <Badge variant="secondary">Aprovada pelo Gerente</Badge>;
      } else if (presidentApproved) {
        return <Badge variant="secondary">Aprovada pelo Presidente</Badge>;
      } else {
        return <Badge variant="outline">Aguardando Aprovação</Badge>;
      }
    } else if (requiresManager && managerApproved) {
      return <Badge variant="default" className="bg-green-500">Aprovada</Badge>;
    } else if (requiresPresident && presidentApproved) {
      return <Badge variant="default" className="bg-green-500">Aprovada</Badge>;
    }

    return <Badge variant="outline">Aguardando Aprovação</Badge>;
  };

  const canUserApprove = (evaluation: PlayerEvaluation) => {
    if (isManager && evaluation.requires_manager_approval && !evaluation.approved_by_manager) {
      return true;
    }
    if (isPresident && evaluation.requires_president_approval && !evaluation.approved_by_president) {
      return true;
    }
    return false;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-32">
        <RefreshCw className="h-6 w-6 animate-spin" />
        <span className="ml-2">Carregando avaliações...</span>
      </div>
    );
  }

  if (!canApprove) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">
          Você não tem permissão para aprovar avaliações.
        </p>
      </div>
    );
  }

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Atleta</TableHead>
              <TableHead>Posição</TableHead>
              <TableHead>Avaliador</TableHead>
              <TableHead>Data de Criação</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {evaluations.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8">
                  <div className="flex flex-col items-center gap-2">
                    <CheckCircle className="h-8 w-8 text-muted-foreground" />
                    <p className="text-muted-foreground">
                      Nenhuma avaliação pendente de aprovação
                    </p>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              evaluations.map((evaluation) => (
                <TableRow key={evaluation.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar className="h-8 w-8">
                        {evaluation.player_image ? (
                          <AvatarImage src={evaluation.player_image} alt={evaluation.player_name} />
                        ) : null}
                        <AvatarFallback 
                          className="text-white text-xs"
                          style={{ backgroundColor: 'var(--color-primary)' }}
                        >
                          {evaluation.player_name?.slice(0, 2).toUpperCase() || "AT"}
                        </AvatarFallback>
                      </Avatar>
                      <span className="font-medium">{evaluation.player_name}</span>
                    </div>
                  </TableCell>
                  <TableCell>{evaluation.position || "-"}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <User className="h-3.5 w-3.5 text-muted-foreground" />
                      <span>{evaluation.created_by_name || "Não informado"}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3.5 w-3.5 text-muted-foreground" />
                      <span>{formatDate(evaluation.created_at)}</span>
                    </div>
                  </TableCell>
                  <TableCell>{getApprovalStatus(evaluation)}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => window.location.href = `/jogador/${evaluation.player_id}`}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      {canUserApprove(evaluation) && (
                        <>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleApprove(evaluation)}
                            className="text-green-600 hover:text-green-700"
                          >
                            <CheckCircle className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleReject(evaluation)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <XCircle className="h-4 w-4" />
                          </Button>
                        </>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {selectedEvaluation && (
        <EvaluationApprovalDialog
          open={showApprovalDialog}
          onOpenChange={setShowApprovalDialog}
          evaluation={selectedEvaluation}
          actionType={actionType}
          userRole={role}
          onSubmit={handleApprovalSubmit}
        />
      )}
    </>
  );
}
