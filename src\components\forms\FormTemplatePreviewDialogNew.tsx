import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { useFormTemplatesStore } from "@/store/useFormTemplatesStore";
import { ClubFormTemplate } from "@/api/clubFormTemplates";
import { FileText, Code, Download, Loader2 } from "lucide-react";

interface FormTemplatePreviewDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  template: ClubFormTemplate | null;
}

export function FormTemplatePreviewDialog({
  open,
  onOpenChange,
  template
}: FormTemplatePreviewDialogProps) {
  const clubId = useCurrentClubId();
  const { toast } = useToast();
  const { generatePDFHtml } = useFormTemplatesStore();

  const [viewMode, setViewMode] = useState<'html' | 'pdf'>('html');
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  // Generate PDF when switching to PDF view
  useEffect(() => {
    if (viewMode === 'pdf' && template && !pdfUrl) {
      generatePdfPreview();
    }
  }, [viewMode, template]);

  // Clean up PDF URL when dialog closes
  useEffect(() => {
    if (!open && pdfUrl) {
      URL.revokeObjectURL(pdfUrl);
      setPdfUrl(null);
    }
  }, [open, pdfUrl]);

  const generatePdfPreview = async () => {
    if (!template) return;

    setLoading(true);
    try {
      const pdfBlob = await generatePDFHtml(template, clubId);
      const url = URL.createObjectURL(pdfBlob);
      setPdfUrl(url);
    } catch (err) {
      toast({
        title: "Erro ao gerar preview",
        description: "Não foi possível gerar a visualização em PDF.",
        variant: "destructive",
      });
      setViewMode('html'); // Fallback to HTML view
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async () => {
    if (!template) return;

    try {
      const pdfBlob = await generatePDFHtml(template, clubId);
      const url = URL.createObjectURL(pdfBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${template.name.replace(/\s+/g, '_')}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast({
        title: "PDF baixado",
        description: "O arquivo foi baixado com sucesso.",
      });
    } catch (err) {
      toast({
        title: "Erro ao baixar PDF",
        description: "Não foi possível baixar o arquivo.",
        variant: "destructive",
      });
    }
  };

  if (!template) {
    return null;
  }

  const FORM_TYPE_LABELS = {
    pre_registration: "Pré-cadastro",
    housing: "Moradia",
    liability_waiver_minor: "Termo de Responsabilidade - Menor de 18",
    liability_waiver_adult: "Termo de Responsabilidade - Maior de 18",
    liability_waiver: "Termo de Responsabilidade",
    custom: "Personalizado"
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[90vw] sm:max-h-[90vh] w-[90vw] h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {template.name}
            <span className="text-sm font-normal text-muted-foreground">
              ({FORM_TYPE_LABELS[template.form_type]})
            </span>
          </DialogTitle>
          {template.description && (
            <p className="text-sm text-muted-foreground">
              {template.description}
            </p>
          )}
        </DialogHeader>

        <div className="mb-4">
          <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as 'html' | 'pdf')}>
            <TabsList>
              <TabsTrigger value="html" className="flex items-center gap-1">
                <Code className="h-4 w-4" />
                <span>Visualização HTML</span>
              </TabsTrigger>
              <TabsTrigger value="pdf" className="flex items-center gap-1">
                <FileText className="h-4 w-4" />
                <span>Visualização PDF</span>
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        <div className="flex-1 h-[calc(90vh-200px)] overflow-auto border rounded-md">
          {viewMode === 'html' ? (
            <div className="p-8 bg-white min-h-full">
              <div className="max-w-3xl mx-auto">
                <div
                  className="prose max-w-none"
                  dangerouslySetInnerHTML={{ __html: template.content }}
                />
              </div>
            </div>
          ) : loading ? (
            <div className="flex items-center justify-center h-full">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              <span className="ml-2 text-muted-foreground">Gerando visualização PDF...</span>
            </div>
          ) : pdfUrl ? (
            <iframe
              src={pdfUrl}
              className="w-full h-full"
              title={`Preview - ${template.name}`}
            />
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-muted-foreground">Erro ao carregar visualização PDF</p>
                <Button 
                  variant="outline" 
                  onClick={generatePdfPreview}
                  className="mt-2"
                >
                  Tentar novamente
                </Button>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Fechar
          </Button>
          <Button onClick={handleDownload}>
            <Download className="h-4 w-4 mr-2" />
            Baixar PDF
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
