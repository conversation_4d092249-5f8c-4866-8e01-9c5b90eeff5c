import { supabase } from "@/integrations/supabase/client";
import { Collaborator, getCollaborators } from "./collaborators";
import { Department, getDepartments } from "./departments";
import { FinancialDataByDepartment } from "@/utils/financialReportGenerator";
import { ClubInfo, getClubInfo } from "./api";

/**
 * Generate financial data organized by department and function
 * @param clubId Club ID
 * @param month Month number (0-11)
 * @param year Year
 * @returns Financial data organized by department and function
 */
export async function generateFinancialDataByDepartment(
  clubId: number,
  month: number,
  year: number
): Promise<FinancialDataByDepartment[]> {
  try {
    // Get all departments
    const departments = await getDepartments(clubId);

    // Get all collaborators
    const allCollaborators = await getCollaborators(clubId);
    
    // Filter out inactive collaborators
    const activeCollaborators = allCollaborators.filter(collaborator => collaborator.status !== 'inactive');

    // Initialize result array
    const result: FinancialDataByDepartment[] = [];

    // For each department
    for (const department of departments) {
      // Get collaborators associated with this department through user_departments
      const departmentCollaborators = await getCollaboratorsByDepartmentId(clubId, department.id);
      
      // Filter out inactive collaborators from department
      const activeDepartmentCollaborators = departmentCollaborators.filter(collaborator => collaborator.status !== 'inactive');

      if (activeDepartmentCollaborators.length > 0) {
        // Initialize department data
        const departmentData: FinancialDataByDepartment = {
          departmentName: department.name,
          functions: [{
            functionName: "Colaboradores",
            collaborators: activeDepartmentCollaborators.map(c => ({
              id: c.id,
              name: c.full_name,
              amount: c.salary || 0,
              details: c.bonus ? `Bônus: R$ ${c.bonus.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}` : undefined
            }))
          }]
        };

        result.push(departmentData);
      }
    }

    // Add collaborators without a department
    const collaboratorsWithoutDepartment = activeCollaborators.filter(c =>
      !result.some(dept =>
        dept.functions.some(func =>
          func.collaborators.some(collab => collab.id === c.id)
        )
      )
    );

    if (collaboratorsWithoutDepartment.length > 0) {
      result.push({
        departmentName: "Sem departamento específico",
        functions: [{
          functionName: "Colaboradores",
          collaborators: collaboratorsWithoutDepartment.map(c => ({
            id: c.id,
            name: c.full_name,
            amount: c.salary || 0,
            details: c.bonus ? `Bônus: R$ ${c.bonus.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}` : undefined
          }))
        }]
      });
    }

    return result;
  } catch (error: any) {
    console.error("Error generating financial data by department:", error);
    throw new Error(error.message || "Error generating financial data by department");
  }
}

/**
 * Get collaborators by department ID using user_departments table
 * @param clubId Club ID
 * @param departmentId Department ID
 * @returns List of collaborators in the department
 */
async function getCollaboratorsByDepartmentId(clubId: number, departmentId: number): Promise<Collaborator[]> {
  try {
    // Get user IDs from user_departments table
    const { data: userDepartments, error: userDeptError } = await supabase
      .from("user_departments")
      .select("user_id")
      .eq("club_id", clubId)
      .eq("department_id", departmentId);

    if (userDeptError) {
      console.error("Error fetching user departments:", userDeptError);
      return [];
    }

    if (!userDepartments || userDepartments.length === 0) {
      return [];
    }

    const userIds = userDepartments.map(ud => ud.user_id);

    // Get collaborators for these users
    const { data: collaborators, error: collabError } = await supabase
      .from("collaborators_view")
      .select("*")
      .eq("club_id", clubId)
      .in("user_id", userIds)
      .order("full_name");

    if (collabError) {
      console.error("Error fetching collaborators by department:", collabError);
      return [];
    }

    return collaborators || [];
  } catch (error: any) {
    console.error("Error in getCollaboratorsByDepartmentId:", error);
    return [];
  }
}

/**
 * Generate and download a financial report
 * @param clubId Club ID
 * @param month Month number (0-11)
 * @param year Year
 * @param filename Optional filename for the PDF
 */
export async function generateAndDownloadFinancialReport(
  clubId: number,
  month: number,
  year: number,
  filename?: string
): Promise<void> {
  try {
    // Get club info
    const clubInfo = await getClubInfo(clubId);

    // Generate financial data
    const financialData = await generateFinancialDataByDepartment(clubId, month, year);

    // Import the financial report generator
    const { generateFinancialReport } = await import("@/utils/financialReportGenerator");

    // Generate the report
    await generateFinancialReport(financialData, clubInfo, month, year, filename);
  } catch (error: any) {
    console.error("Error generating financial report:", error);
    throw new Error(error.message || "Error generating financial report");
  }
}
