import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";

interface Option {
  id: string;
  name: string;
}

interface RequesterSelectorProps {
  value: string;
  onChange: (value: string) => void;
  label?: string;
  placeholder?: string;
  disabled?: boolean;
}

export function RequesterSelector({
  value,
  onChange,
  label = "",
  placeholder = "Selecione um solicitante",
  disabled = false,
  options = [],
}: RequesterSelectorProps & { options?: Option[] }) {
  const showLabel = Boolean(label);

  return (
    <div className={showLabel ? "space-y-2" : undefined}>
      {showLabel && <Label htmlFor="requester-selector">{label}</Label>}
      <Select
        value={value}
        onValueChange={onChange}
        disabled={disabled}
      >
        <SelectTrigger id="requester-selector" className="w-full">
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">Todos</SelectItem>
          {options.map((opt) => (
            <SelectItem key={opt.id} value={opt.id}>
              {opt.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
