import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Plus, X } from "lucide-react";
import type { Player } from "@/api/api";

interface FootballFieldProps {
  formation: string;
  lineup: Record<string, Player | undefined>;
  onPositionClick: (position: string) => void;
  onPlayerRemove: (position: string) => void;
  readonly?: boolean;
}

// Posições por formação com coordenadas para o campo
const formationLayouts: Record<string, Record<string, { x: number; y: number; label: string }>> = {
  "4-4-2": {
    GK: { x: 50, y: 10, label: "Goleiro" },
    RB: { x: 80, y: 25, label: "LE" },
    CB1: { x: 60, y: 25, label: "ZAG" },
    CB2: { x: 40, y: 25, label: "ZAG" },
    LB: { x: 20, y: 25, label: "LD" },
    RM: { x: 80, y: 50, label: "ME" },
    CM1: { x: 60, y: 50, label: "MC" },
    CM2: { x: 40, y: 50, label: "MC" },
    LM: { x: 20, y: 50, label: "MD" },
    ST1: { x: 60, y: 75, label: "ATA" },
    ST2: { x: 40, y: 75, label: "ATA" },
  },
  "4-3-3": {
    GK: { x: 50, y: 10, label: "Goleiro" },
    RB: { x: 80, y: 25, label: "LD" },
    CB1: { x: 60, y: 25, label: "ZAG" },
    CB2: { x: 40, y: 25, label: "ZAG" },
    LB: { x: 20, y: 25, label: "LE" },
    CDM: { x: 50, y: 40, label: "VOL" },
    CM1: { x: 65, y: 55, label: "MC" },
    CM2: { x: 35, y: 55, label: "MC" },
    RW: { x: 80, y: 75, label: "PD" },
    ST: { x: 50, y: 75, label: "ATA" },
    LW: { x: 20, y: 75, label: "PE" },
  },
  "3-5-2": {
    GK: { x: 50, y: 10, label: "Goleiro" },
    CB1: { x: 70, y: 25, label: "ZAG" },
    CB2: { x: 50, y: 25, label: "ZAG" },
    CB3: { x: 30, y: 25, label: "ZAG" },
    RWB: { x: 85, y: 45, label: "ALA" },
    CM1: { x: 65, y: 50, label: "MC" },
    CM2: { x: 50, y: 50, label: "MC" },
    CM3: { x: 35, y: 50, label: "MC" },
    LWB: { x: 15, y: 45, label: "ALA" },
    ST1: { x: 60, y: 75, label: "ATA" },
    ST2: { x: 40, y: 75, label: "ATA" },
  },
  "4-2-3-1": {
    GK: { x: 50, y: 10, label: "Goleiro" },
    RB: { x: 80, y: 25, label: "LD" },
    CB1: { x: 60, y: 25, label: "ZAG" },
    CB2: { x: 40, y: 25, label: "ZAG" },
    LB: { x: 20, y: 25, label: "LE" },
    CDM1: { x: 60, y: 40, label: "VOL" },
    CDM2: { x: 40, y: 40, label: "VOL" },
    CAM1: { x: 70, y: 60, label: "MEI" },
    CAM2: { x: 50, y: 60, label: "MEI" },
    CAM3: { x: 30, y: 60, label: "MEI" },
    ST: { x: 50, y: 75, label: "ATA" },
  },
  "5-3-2": {
    GK: { x: 50, y: 10, label: "Goleiro" },
    RB: { x: 85, y: 25, label: "LD" },
    CB1: { x: 65, y: 25, label: "ZAG" },
    CB2: { x: 50, y: 25, label: "ZAG" },
    CB3: { x: 35, y: 25, label: "ZAG" },
    LB: { x: 15, y: 25, label: "LE" },
    CM1: { x: 65, y: 50, label: "MC" },
    CM2: { x: 50, y: 50, label: "MC" },
    CM3: { x: 35, y: 50, label: "MC" },
    ST1: { x: 60, y: 75, label: "ATA" },
    ST2: { x: 40, y: 75, label: "ATA" },
  },
};

export function FootballField({ formation, lineup, onPositionClick, onPlayerRemove, readonly = false }: FootballFieldProps) {
  const layout = formationLayouts[formation] || formationLayouts["4-4-2"];

  return (
    <div className="relative w-full h-[600px] bg-gradient-to-b from-green-400 to-green-500 rounded-lg border-4 border-white overflow-hidden">
      {/* Campo de futebol - linhas */}
      <svg className="absolute inset-0 w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
        {/* Linha do meio */}
        <line x1="0" y1="50" x2="100" y2="50" stroke="white" strokeWidth="0.3" />
        
        {/* Círculo central */}
        <circle cx="50" cy="50" r="8" fill="none" stroke="white" strokeWidth="0.3" />
        <circle cx="50" cy="50" r="0.5" fill="white" />
        
        {/* Área do goleiro - superior */}
        <rect x="40" y="0" width="20" height="8" fill="none" stroke="white" strokeWidth="0.3" />
        <rect x="45" y="0" width="10" height="4" fill="none" stroke="white" strokeWidth="0.3" />
        
        {/* Área do goleiro - inferior */}
        <rect x="40" y="92" width="20" height="8" fill="none" stroke="white" strokeWidth="0.3" />
        <rect x="45" y="96" width="10" height="4" fill="none" stroke="white" strokeWidth="0.3" />
        
        {/* Semicírculo da grande área - superior */}
        <path d="M 40 8 A 8 8 0 0 0 60 8" fill="none" stroke="white" strokeWidth="0.3" />
        
        {/* Semicírculo da grande área - inferior */}
        <path d="M 40 92 A 8 8 0 0 1 60 92" fill="none" stroke="white" strokeWidth="0.3" />
        
        {/* Marca do pênalti */}
        <circle cx="50" cy="12" r="0.5" fill="white" />
        <circle cx="50" cy="88" r="0.5" fill="white" />
      </svg>

      {/* Posições dos jogadores */}
      {Object.entries(layout).map(([position, coords]) => {
        const player = lineup[position];
        
        return (
          <div
            key={position}
            className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer"
            style={{
              left: `${coords.x}%`,
              top: `${coords.y}%`,
            }}
            onClick={() => !readonly && onPositionClick(position)}
          >
            <div className="relative group">
              {player ? (
                <div className="flex flex-col items-center">
                  <div className="relative">
                    <Avatar className="w-12 h-12 border-2 border-white shadow-lg">
                      <AvatarImage src={player.image || ""} />
                      <AvatarFallback className="bg-blue-600 text-white font-bold">
                        {player.number || player.name.charAt(0)}
                      </AvatarFallback>
                    </Avatar>
                    {!readonly && (
                      <Button
                        size="sm"
                        variant="destructive"
                        className="absolute -top-1 -right-1 w-5 h-5 rounded-full p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={(e) => {
                          e.stopPropagation();
                          onPlayerRemove(position);
                        }}
                      >
                        <X className="w-3 h-3" />
                      </Button>
                    )}
                  </div>
                  <div className="mt-1 text-center">
                    <div className="text-xs font-bold text-white bg-black bg-opacity-50 px-1 rounded">
                      {(player.nickname || player.name)} #{player.number}
                    </div>
                    <div className="text-xs text-white bg-black bg-opacity-30 px-1 rounded mt-0.5">
                      {coords.label}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex flex-col items-center">
                  <div className="w-12 h-12 border-2 border-dashed border-white rounded-full bg-white bg-opacity-20 flex items-center justify-center hover:bg-opacity-40 transition-colors">
                    <Plus className="w-6 h-6 text-white" />
                  </div>
                  <div className="mt-1 text-center">
                    <div className="text-xs text-white bg-black bg-opacity-50 px-1 rounded">
                      {coords.label}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
}
