-- Ad<PERSON><PERSON><PERSON> coluna collaborator_id à tabela match_squad
-- Isso permite identificar colaboradores no squad da partida

-- 1. <PERSON><PERSON><PERSON>r a coluna collaborator_id
ALTER TABLE match_squad 
ADD COLUMN IF NOT EXISTS collaborator_id INTEGER REFERENCES collaborators(id);

-- 2. Atualizar a constraint para permitir colaboradores
ALTER TABLE match_squad 
DROP CONSTRAINT IF EXISTS check_player_or_user;

-- 3. Criar nova constraint que permite player_id, user_id ou collaborator_id
ALTER TABLE match_squad 
ADD CONSTRAINT check_player_or_user_or_collaborator CHECK (
  (player_id IS NOT NULL AND user_id IS NULL AND collaborator_id IS NULL) OR 
  (player_id IS NULL AND user_id IS NOT NULL AND collaborator_id IS NULL) OR
  (player_id IS NULL AND user_id IS NULL AND collaborator_id IS NOT NULL)
);

-- 4. Adicionar constraint única para colaboradores
ALTER TABLE match_squad 
ADD CONSTRAINT unique_collaborator_per_match 
UNIQUE(club_id, match_id, collaborator_id);

-- 5. <PERSON><PERSON><PERSON> índice para melhor performance
CREATE INDEX IF NOT EXISTS idx_match_squad_collaborator 
ON match_squad(club_id, match_id, collaborator_id);

-- 6. Comentários para documentação
COMMENT ON COLUMN match_squad.collaborator_id IS 'ID do colaborador quando o membro do squad é um colaborador';
COMMENT ON CONSTRAINT check_player_or_user_or_collaborator ON match_squad IS 'Garante que apenas um tipo de membro seja especificado: jogador, usuário ou colaborador';
COMMENT ON CONSTRAINT unique_collaborator_per_match ON match_squad IS 'Garante que cada colaborador só pode ter um papel por partida';
