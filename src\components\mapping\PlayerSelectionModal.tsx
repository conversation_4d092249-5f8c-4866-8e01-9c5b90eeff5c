import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Search, Calendar, Hash } from "lucide-react";
import { Player } from "@/api/players";
import { MappingPosition } from "@/api/categoryMappings";

interface PlayerSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectPlayer: (player: MappingPosition) => void;
  players: Player[];
  positionType: string;
  positionLabel: string;
  selectedPlayers: { [key: string]: MappingPosition | null };
}

// Mapeamento de tipos de posição para posições de jogador
const POSITION_FILTERS = {
  goalkeeper: ["goleiro", "gol", "goalkeeper"],
  defender: [
    "zagueiro", "zag", "defensor", "defender",
    "lateral-direito", "lateral direito", "ld", "lateral_direito",
    "lateral-esquerdo", "lateral esquerdo", "le", "lateral_esquerdo",
    "lateral", "laterais", "beque"
  ],
  midfielder: [
    "volante", "vol", "meio-campo", "meio campo", "meia", "mei", "mc", "md", "me",
    "meio-campista", "meio campista", "midfielder", "midfield",
    "armador", "segundo volante", "meia-atacante", "meia atacante"
  ],
  attacker: [
    "atacante", "ata", "centroavante", "centro-avante", "attacker", "forward",
    "ponta", "ponta-direita", "ponta direita", "pd", "ponta_direita",
    "ponta-esquerda", "ponta esquerda", "pe", "ponta_esquerda",
    "extremo", "ala", "segundo atacante"
  ],
};

const PlayerSelectionModal: React.FC<PlayerSelectionModalProps> = ({
  isOpen,
  onClose,
  onSelectPlayer,
  players,
  positionType,
  positionLabel,
  selectedPlayers,
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredPlayers, setFilteredPlayers] = useState<Player[]>([]);

  // Filtrar jogadores baseado na posição e termo de busca
  useEffect(() => {
    let filtered = players.filter(player => {
      // Filtrar apenas jogadores disponíveis (não emprestados ou inativos)
      if (player.status === "emprestado" || player.status === "inativo") {
        return false;
      }

      // Verificar se o jogador já está selecionado em alguma posição
      const isAlreadySelected = Object.values(selectedPlayers).some(
        selectedPlayer => selectedPlayer?.player_id === player.id
      );
      if (isAlreadySelected) {
        return false;
      }

      // Filtrar por posição se especificado
      if (positionType && positionType !== "all") {
        const allowedPositions = POSITION_FILTERS[positionType as keyof typeof POSITION_FILTERS] || [];
        const playerPosition = player.position?.toLowerCase().trim() || "";

        // Verificar se a posição do jogador corresponde a alguma das posições permitidas
        const matchesPosition = allowedPositions.some(pos => {
          const normalizedPos = pos.toLowerCase().trim();
          // Verificar correspondência exata ou se contém a posição
          return playerPosition === normalizedPos ||
                 playerPosition.includes(normalizedPos) ||
                 normalizedPos.includes(playerPosition);
        });

        if (!matchesPosition) {
          return false;
        }
      }

      // Filtrar por termo de busca
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        return (
          player.name.toLowerCase().includes(searchLower) ||
          player.nickname?.toLowerCase().includes(searchLower) ||
          player.number.toString().includes(searchTerm) ||
          player.position?.toLowerCase().includes(searchLower)
        );
      }

      return true;
    });

    // Ordenar por número da camisa
    filtered.sort((a, b) => a.number - b.number);

    setFilteredPlayers(filtered);
  }, [players, searchTerm, positionType, selectedPlayers]);

  const handleSelectPlayer = (player: Player) => {
    const mappingPosition: MappingPosition = {
      player_id: player.id,
      player_name: player.name,
      player_nickname: player.nickname,
      player_number: player.number,
      player_birthdate: player.birthdate,
      player_image: player.image,
    };

    onSelectPlayer(mappingPosition);
    onClose();
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return "";
    try {
      return new Date(dateString).toLocaleDateString("pt-BR");
    } catch {
      return "";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "ativo":
        return "bg-green-100 text-green-800";
      case "lesionado":
        return "bg-red-100 text-red-800";
      case "suspenso":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>
            Selecionar Jogador para {positionLabel}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Barra de busca */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Buscar por nome, apelido, número ou posição..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Lista de jogadores */}
          <div className="max-h-96 overflow-y-auto space-y-2">
            {filteredPlayers.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                {searchTerm ? "Nenhum jogador encontrado" : "Nenhum jogador disponível para esta posição"}
              </div>
            ) : (
              filteredPlayers.map((player) => (
                <div
                  key={player.id}
                  className="flex items-center space-x-4 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                  onClick={() => handleSelectPlayer(player)}
                >
                  {/* Avatar do jogador */}
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={player.image || ""} />
                    <AvatarFallback className="bg-blue-500 text-white font-bold">
                      {player.number}
                    </AvatarFallback>
                  </Avatar>

                  {/* Informações do jogador */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <h3 className="font-medium text-gray-900 truncate">
                        {player.name}
                      </h3>
                      {player.nickname && (
                        <span className="text-sm text-gray-500">
                          ({player.nickname})
                        </span>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-4 mt-1">
                      <div className="flex items-center space-x-1 text-sm text-gray-600">
                        <Hash className="h-3 w-3" />
                        <span>{player.number}</span>
                      </div>
                      
                      {player.birthdate && (
                        <div className="flex items-center space-x-1 text-sm text-gray-600">
                          <Calendar className="h-3 w-3" />
                          <span>{formatDate(player.birthdate)}</span>
                        </div>
                      )}
                      
                      <Badge variant="outline" className="text-xs">
                        {player.position}
                      </Badge>
                      
                      <Badge className={`text-xs ${getStatusColor(player.status)}`}>
                        {player.status}
                      </Badge>
                    </div>
                  </div>

                  {/* Botão de seleção */}
                  <Button size="sm" variant="outline">
                    Selecionar
                  </Button>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Rodapé */}
        <div className="flex justify-between items-center pt-4 border-t">
          <div className="text-sm text-gray-500">
            {filteredPlayers.length} jogador(es) disponível(is)
          </div>
          <Button variant="outline" onClick={onClose}>
            Cancelar
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PlayerSelectionModal;
