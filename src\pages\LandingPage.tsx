import { useEffect, useRef, useState } from "react";
import { <PERSON> } from "react-router-dom";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { motion } from "framer-motion";
import { Check, TrendingUp, Shield, Calendar, Users, Award, ChevronRight, Sparkles, Zap, Target, BarChart3, Heart, Eye, Play, Pause, ChevronLeft, Star, Globe, Flame, ArrowRight, ArrowUp, ArrowDown, Activity, Trophy, Rocket, Brain, Cpu, Database, Lock, Wifi } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

// Registrar plugins do GSAP
gsap.registerPlugin(ScrollTrigger);

// Dados do carrossel espetacular sobre o programa
const heroCarouselData = [
  {
    id: 1,
    title: "Revolução na Gestão Esportiva",
    subtitle: "O futuro chegou ao seu clube",
    description: "Transforme completamente a gestão do seu clube com tecnologia de ponta, IA integrada e automação inteligente",
    image: "https://images.unsplash.com/photo-1431324155629-1a6deb1dec8d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1740&q=80",
    icon: Rocket,
    color: "from-blue-600 via-purple-600 to-pink-600",
    stats: { clubs: "500+", players: "10K+", matches: "50K+" },
    features: ["IA Avançada", "Automação Total", "Analytics Real-time"]
  },
  {
    id: 2,
    title: "Inteligência Artificial Integrada",
    subtitle: "Decisões baseadas em dados",
    description: "Nossa IA analisa padrões, prevê resultados e oferece insights estratégicos para maximizar o desempenho da sua equipe",
    image: "https://images.unsplash.com/photo-1574629810360-7efbbe195018?ixlib=rb-4.0.3&auto=format&fit=crop&w=1740&q=80",
    icon: Brain,
    color: "from-green-500 via-teal-500 to-cyan-500",
    stats: { accuracy: "95%", predictions: "1M+", insights: "24/7" },
    features: ["Machine Learning", "Análise Preditiva", "Otimização Automática"]
  },
  {
    id: 3,
    title: "Ecossistema Completo",
    subtitle: "Tudo integrado em uma plataforma",
    description: "Gestão médica, financeira, técnica e administrativa em um só lugar. Sincronização perfeita entre todos os departamentos",
    image: "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1740&q=80",
    icon: Cpu,
    color: "from-orange-500 via-red-500 to-pink-500",
    stats: { modules: "15+", integration: "100%", efficiency: "+300%" },
    features: ["Módulos Integrados", "API Robusta", "Sincronização Real-time"]
  },
  {
    id: 4,
    title: "Segurança de Nível Bancário",
    subtitle: "Seus dados protegidos",
    description: "Criptografia militar, backup automático e conformidade total com LGPD. Seus dados nunca estiveram tão seguros",
    image: "https://images.unsplash.com/photo-1554224155-6726b3ff858f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1740&q=80",
    icon: Lock,
    color: "from-indigo-600 via-blue-600 to-purple-600",
    stats: { uptime: "99.9%", security: "Military", backup: "Real-time" },
    features: ["Criptografia AES-256", "Backup Automático", "Conformidade LGPD"]
  }
];

// Dados dos módulos do sistema
const modulesData = [
  {
    id: 1,
    title: "Gestão de Elenco",
    description: "Controle completo de jogadores, comissão técnica e staff",
    icon: Users,
    color: "from-blue-500 to-blue-600",
    features: ["Cadastro Completo", "Histórico Detalhado", "Documentação Digital", "Contratos Inteligentes"]
  },
  {
    id: 2,
    title: "Sistema Médico",
    description: "Acompanhamento médico e fisioterapêutico avançado",
    icon: Shield,
    color: "from-red-500 to-red-600",
    features: ["Prontuário Digital", "Controle de Lesões", "Reabilitação", "Exames Médicos"]
  },
  {
    id: 3,
    title: "Analytics Avançado",
    description: "Estatísticas e análises de performance em tempo real",
    icon: BarChart3,
    color: "from-green-500 to-green-600",
    features: ["Métricas Avançadas", "Relatórios Automáticos", "Comparativos", "Tendências"]
  },
  {
    id: 4,
    title: "Gestão Financeira",
    description: "Controle total das finanças do clube",
    icon: TrendingUp,
    color: "from-yellow-500 to-yellow-600",
    features: ["Fluxo de Caixa", "Orçamentos", "Relatórios Fiscais", "Controle de Gastos"]
  },
  {
    id: 5,
    title: "Treinamentos",
    description: "Planejamento e execução de treinos inteligentes",
    icon: Target,
    color: "from-purple-500 to-purple-600",
    features: ["Planos de Treino", "Periodização", "Controle de Carga", "Avaliações"]
  },
  {
    id: 6,
    title: "Base Juvenil",
    description: "Desenvolvimento de jovens talentos",
    icon: Award,
    color: "from-indigo-500 to-indigo-600",
    features: ["Categorias de Base", "Desenvolvimento", "Avaliações", "Progressão"]
  }
];

// Dados dos módulos específicos para o carrossel avançado
const specificModulesData = [
  {
    id: 1,
    title: "Sistema Médico Avançado",
    subtitle: "Cuidado total com seus atletas",
    description: "Prontuário digital completo, controle de lesões, reabilitação e acompanhamento médico em tempo real",
    image: "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1740&q=80",
    icon: Shield,
    color: "from-red-500 via-pink-500 to-rose-500",
    features: [
      { title: "Prontuário Digital", description: "Histórico médico completo e seguro" },
      { title: "Controle de Lesões", description: "Monitoramento e prevenção de lesões" },
      { title: "Reabilitação", description: "Planos personalizados de recuperação" },
      { title: "Exames Médicos", description: "Gestão completa de exames e resultados" }
    ],
    stats: { patients: "1000+", recovery: "95%", prevention: "80%" }
  },
  {
    id: 2,
    title: "Gestão de Estoque Inteligente",
    subtitle: "Controle total do seu inventário",
    description: "Sistema automatizado de controle de estoque com alertas inteligentes e gestão de equipamentos esportivos",
    image: "https://images.unsplash.com/photo-**********-98eeb64c6a62?ixlib=rb-4.0.3&auto=format&fit=crop&w=1740&q=80",
    icon: Database,
    color: "from-blue-500 via-cyan-500 to-teal-500",
    features: [
      { title: "Controle Automatizado", description: "Entrada e saída automática de produtos" },
      { title: "Alertas Inteligentes", description: "Notificações de estoque baixo" },
      { title: "Equipamentos Esportivos", description: "Gestão completa de materiais" },
      { title: "Relatórios Detalhados", description: "Analytics completo do estoque" }
    ],
    stats: { items: "5000+", efficiency: "98%", savings: "40%" }
  },
  {
    id: 3,
    title: "Centro Financeiro Completo",
    subtitle: "Finanças sob controle total",
    description: "Gestão financeira completa com fluxo de caixa, orçamentos, relatórios fiscais e controle de receitas",
    image: "https://images.unsplash.com/photo-1554224155-6726b3ff858f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1740&q=80",
    icon: TrendingUp,
    color: "from-green-500 via-emerald-500 to-teal-500",
    features: [
      { title: "Fluxo de Caixa", description: "Controle completo de entradas e saídas" },
      { title: "Orçamentos", description: "Planejamento financeiro inteligente" },
      { title: "Relatórios Fiscais", description: "Conformidade total com legislação" },
      { title: "Análise de ROI", description: "Retorno sobre investimentos" }
    ],
    stats: { transactions: "10K+", accuracy: "99.9%", growth: "150%" }
  },
  {
    id: 4,
    title: "Pré-Cadastro de Atletas",
    subtitle: "Descoberta de novos talentos",
    description: "Sistema avançado de captação e avaliação de novos atletas com IA para identificação de talentos",
    image: "https://images.unsplash.com/photo-1431324155629-1a6deb1dec8d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1740&q=80",
    icon: Users,
    color: "from-purple-500 via-violet-500 to-indigo-500",
    features: [
      { title: "Captação Inteligente", description: "IA para identificação de talentos" },
      { title: "Avaliação Completa", description: "Testes físicos e técnicos" },
      { title: "Banco de Talentos", description: "Base de dados de candidatos" },
      { title: "Processo Automatizado", description: "Fluxo completo de seleção" }
    ],
    stats: { candidates: "2000+", success: "85%", talents: "300+" }
  },
  {
    id: 5,
    title: "Sistema de Alojamentos",
    subtitle: "Hospedagem profissional",
    description: "Gestão completa de alojamentos para atletas com controle de ocupação, manutenção e bem-estar",
    image: "https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&auto=format&fit=crop&w=1740&q=80",
    icon: Award,
    color: "from-orange-500 via-amber-500 to-yellow-500",
    features: [
      { title: "Controle de Ocupação", description: "Gestão de quartos e vagas" },
      { title: "Manutenção Preventiva", description: "Cuidado com as instalações" },
      { title: "Bem-estar dos Atletas", description: "Ambiente ideal para descanso" },
      { title: "Relatórios de Ocupação", description: "Analytics de utilização" }
    ],
    stats: { rooms: "200+", occupancy: "92%", satisfaction: "98%" }
  },
  {
    id: 6,
    title: "Sistema de Alimentação",
    subtitle: "Nutrição de alto rendimento",
    description: "Gestão completa da alimentação dos atletas com cardápios personalizados e controle nutricional",
    image: "https://images.unsplash.com/photo-1490645935967-10de6ba17061?ixlib=rb-4.0.3&auto=format&fit=crop&w=1740&q=80",
    icon: Heart,
    color: "from-pink-500 via-rose-500 to-red-500",
    features: [
      { title: "Cardápios Personalizados", description: "Nutrição específica por atleta" },
      { title: "Controle Nutricional", description: "Acompanhamento de macros e micros" },
      { title: "Gestão de Refeições", description: "Planejamento completo de cardápios" },
      { title: "Relatórios Nutricionais", description: "Analytics de consumo" }
    ],
    stats: { meals: "50K+", nutrition: "100%", health: "95%" }
  },
  {
    id: 7,
    title: "Avaliação de Atletas",
    subtitle: "Performance baseada em dados",
    description: "Sistema completo de avaliação física, técnica e psicológica com IA para análise de performance",
    image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1740&q=80",
    icon: BarChart3,
    color: "from-indigo-500 via-blue-500 to-cyan-500",
    features: [
      { title: "Avaliação Física", description: "Testes de capacidade e resistência" },
      { title: "Análise Técnica", description: "Habilidades específicas do esporte" },
      { title: "Perfil Psicológico", description: "Avaliação mental e emocional" },
      { title: "IA Preditiva", description: "Previsão de performance futura" }
    ],
    stats: { evaluations: "5000+", accuracy: "94%", improvement: "60%" }
  }
];

export default function LandingPage() {
  const headerRef = useRef<HTMLDivElement>(null);
  const featuresRef = useRef<HTMLDivElement>(null);
  const pricingRef = useRef<HTMLDivElement>(null);

  // Estados para o carrossel hero
  const [currentHeroSlide, setCurrentHeroSlide] = useState(0);
  const [isHeroPlaying, setIsHeroPlaying] = useState(true);
  const [isHeroHovered, setIsHeroHovered] = useState(false);
  const heroIntervalRef = useRef<any>(null);

  // Estados para o carrossel de módulos específicos
  const [currentModuleSlide, setCurrentModuleSlide] = useState(0);
  const [isModulePlaying, setIsModulePlaying] = useState(true);
  const [isModuleHovered, setIsModuleHovered] = useState(false);
  const [moduleTransitioning, setModuleTransitioning] = useState(false);
  const moduleIntervalRef = useRef<any>(null);

  // Estado para o header transparente
  const [isScrolled, setIsScrolled] = useState(false);

  // Controle do carrossel hero
  useEffect(() => {
    if (isHeroPlaying && !isHeroHovered) {
      heroIntervalRef.current = setInterval(() => {
        setCurrentHeroSlide((prev) => (prev + 1) % heroCarouselData.length);
      }, 6000);
    } else {
      if (heroIntervalRef.current) {
        clearInterval(heroIntervalRef.current);
      }
    }

    return () => {
      if (heroIntervalRef.current) {
        clearInterval(heroIntervalRef.current);
      }
    };
  }, [isHeroPlaying, isHeroHovered]);

  // Funções do carrossel hero
  const nextHeroSlide = () => {
    setCurrentHeroSlide((prev) => (prev + 1) % heroCarouselData.length);
  };

  const prevHeroSlide = () => {
    setCurrentHeroSlide((prev) => (prev - 1 + heroCarouselData.length) % heroCarouselData.length);
  };

  const goToHeroSlide = (index: number) => {
    setCurrentHeroSlide(index);
  };

  // Controle do carrossel de módulos específicos
  useEffect(() => {
    if (isModulePlaying && !isModuleHovered && !moduleTransitioning) {
      moduleIntervalRef.current = setInterval(() => {
        setModuleTransitioning(true);
        setTimeout(() => {
          setCurrentModuleSlide((prev) => (prev + 1) % specificModulesData.length);
          setModuleTransitioning(false);
        }, 300);
      }, 7000);
    } else {
      if (moduleIntervalRef.current) {
        clearInterval(moduleIntervalRef.current);
      }
    }

    return () => {
      if (moduleIntervalRef.current) {
        clearInterval(moduleIntervalRef.current);
      }
    };
  }, [isModulePlaying, isModuleHovered, moduleTransitioning]);

  // Funções do carrossel de módulos específicos
  const nextModuleSlide = () => {
    if (moduleTransitioning) return;
    setModuleTransitioning(true);
    setTimeout(() => {
      setCurrentModuleSlide((prev) => (prev + 1) % specificModulesData.length);
      setModuleTransitioning(false);
    }, 300);
  };

  const prevModuleSlide = () => {
    if (moduleTransitioning) return;
    setModuleTransitioning(true);
    setTimeout(() => {
      setCurrentModuleSlide((prev) => (prev - 1 + specificModulesData.length) % specificModulesData.length);
      setModuleTransitioning(false);
    }, 300);
  };

  const goToModuleSlide = (index: number) => {
    if (moduleTransitioning || index === currentModuleSlide) return;
    setModuleTransitioning(true);
    setTimeout(() => {
      setCurrentModuleSlide(index);
      setModuleTransitioning(false);
    }, 300);
  };

  // Detectar scroll para header transparente
  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      setIsScrolled(scrollPosition > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
     // Limpar triggers existentes para evitar conflitos
     ScrollTrigger.getAll().forEach(trigger => trigger.kill());

     // Animação apenas do header - remover outras para evitar conflitos com Framer Motion
     const headerTl = gsap.timeline();
     headerTl.from(".hero-title", {
       y: 50,
       opacity: 0,
       duration: 1,
       ease: "power3.out",
     });
     headerTl.from(".hero-subtitle", {
       y: 30,
       opacity: 0,
       duration: 0.8,
       ease: "power3.out",
     }, "-=0.6");
     headerTl.from(".hero-cta", {
       y: 20,
       opacity: 0,
       duration: 0.6,
       ease: "power3.out",
     }, "-=0.4");

     return () => {
       // Limpar instâncias do ScrollTrigger
       ScrollTrigger.getAll().forEach(trigger => trigger.kill());
     };
   }, []);

   // Removido fadeInUpVariants para evitar conflitos - usando animações inline

   return (
     <div className="flex flex-col min-h-screen bg-gradient-to-b from-primary/5 to-white">
       {/* Header/Navigation - Transparente Dinâmico */}
       <motion.header
         className={`fixed top-0 left-0 right-0 z-50 px-6 py-4 md:px-12 lg:px-20 transition-all duration-500 ease-out ${
           isScrolled
             ? 'bg-white/95 backdrop-blur-xl border-b border-gray-200/50 shadow-lg shadow-black/5'
             : 'bg-transparent border-b border-transparent'
         }`}
         initial={{ y: -100 }}
         animate={{ y: 0 }}
         transition={{ duration: 0.8, ease: "easeOut" }}
       >
         <div className="max-w-7xl mx-auto flex justify-between items-center">
           {/* Logo */}
           <motion.div
             className="flex items-center gap-3"
             whileHover={{ scale: 1.05 }}
             transition={{ duration: 0.2 }}
           >
             <div className={`w-12 h-12 rounded-2xl bg-gradient-to-br from-blue-600 to-purple-600 flex items-center justify-center shadow-lg transition-all duration-300 ${
               isScrolled ? 'shadow-blue-500/25' : 'shadow-white/25'
             }`}>
               <span className="text-white font-bold text-lg">GDN</span>
             </div>
             <div className="flex flex-col">
               <span className={`text-xl font-bold transition-colors duration-300 ${
                 isScrolled ? 'text-gray-900' : 'text-white'
               }`}>
                 Game Day Nexus
               </span>
               <span className={`text-xs font-medium transition-colors duration-300 ${
                 isScrolled ? 'text-blue-600' : 'text-blue-200'
               }`}>
                 Gestão Esportiva Inteligente
               </span>
             </div>
           </motion.div>

           {/* Navigation */}
           <nav className="hidden lg:flex items-center gap-8">
             {[
               { href: "#features", label: "Recursos" },
               { href: "#testimonials", label: "Depoimentos" },
               { href: "#pricing", label: "Preços" }
             ].map((item, index) => (
               <motion.a
                 key={item.href}
                 href={item.href}
                 className={`font-medium transition-all duration-300 hover:scale-105 ${
                   isScrolled
                     ? 'text-gray-600 hover:text-blue-600'
                     : 'text-white/80 hover:text-white'
                 }`}
                 whileHover={{ y: -2 }}
                 initial={{ opacity: 0, y: -20 }}
                 animate={{ opacity: 1, y: 0 }}
                 transition={{ duration: 0.5, delay: index * 0.1 }}
               >
                 {item.label}
               </motion.a>
             ))}
           </nav>

           {/* Action Buttons */}
           <div className="flex items-center gap-4">
             <Link to="/login">
               <motion.div
                 whileHover={{ scale: 1.05 }}
                 whileTap={{ scale: 0.95 }}
               >
                 <Button
                   variant="ghost"
                   className={`font-medium transition-all duration-300 ${
                     isScrolled
                       ? 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'
                       : 'text-white/80 hover:text-white hover:bg-white/10'
                   }`}
                 >
                   Entrar
                 </Button>
               </motion.div>
             </Link>

             <Link to="/login">
               <motion.div
                 whileHover={{ scale: 1.05, y: -2 }}
                 whileTap={{ scale: 0.95 }}
                 initial={{ opacity: 0, x: 20 }}
                 animate={{ opacity: 1, x: 0 }}
                 transition={{ duration: 0.6, delay: 0.3 }}
               >
                 <Button className={`font-semibold px-6 py-2.5 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl ${
                   isScrolled
                     ? 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white'
                     : 'bg-white text-blue-600 hover:bg-gray-50'
                 }`}>
                   <Sparkles className="w-4 h-4 mr-2" />
                   Começar Grátis
                   <ArrowRight className="w-4 h-4 ml-2" />
                 </Button>
               </motion.div>
             </Link>
           </div>

           {/* Mobile Menu Button */}
           <motion.button
             className={`lg:hidden p-2 rounded-xl transition-all duration-300 ${
               isScrolled
                 ? 'text-gray-600 hover:bg-gray-100'
                 : 'text-white hover:bg-white/10'
             }`}
             whileHover={{ scale: 1.1 }}
             whileTap={{ scale: 0.9 }}
           >
             <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
               <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
             </svg>
           </motion.button>
         </div>

         {/* Gradient border effect */}
         <div className={`absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-500/50 to-transparent transition-opacity duration-500 ${
           isScrolled ? 'opacity-100' : 'opacity-0'
         }`} />
       </motion.header>

       {/* Hero Section - Carrossel Espetacular */}
       <section ref={headerRef} className="relative min-h-screen flex items-center justify-center overflow-hidden pt-20">
         {/* Background dinâmico */}
         <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900">
           {/* Partículas animadas */}
           <div className="absolute inset-0">
             {[...Array(50)].map((_, i) => (
               <div
                 key={i}
                 className="absolute w-1 h-1 bg-white/20 rounded-full animate-pulse"
                 style={{
                   left: `${Math.random() * 100}%`,
                   top: `${Math.random() * 100}%`,
                   animationDelay: `${Math.random() * 3}s`,
                   animationDuration: `${2 + Math.random() * 3}s`
                 }}
               />
             ))}
           </div>

           {/* Efeitos de luz */}
           <div className="absolute top-0 left-0 w-96 h-96 bg-blue-500/20 rounded-full blur-3xl animate-pulse"></div>
           <div className="absolute top-1/2 right-0 w-80 h-80 bg-purple-500/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
           <div className="absolute bottom-0 left-1/3 w-72 h-72 bg-cyan-500/20 rounded-full blur-3xl animate-pulse delay-2000"></div>
         </div>

         {/* Conteúdo do carrossel */}
         <div
           className="relative z-10 w-full max-w-7xl mx-auto px-6 md:px-12 lg:px-20"
           onMouseEnter={() => setIsHeroHovered(true)}
           onMouseLeave={() => setIsHeroHovered(false)}
         >
           <div className="grid lg:grid-cols-2 gap-12 items-center min-h-[600px]">
             {/* Conteúdo textual */}
             <div className="space-y-8 text-white">
               <div className="space-y-4">
                 <Badge className="bg-white/20 text-white border-white/30 px-4 py-2">
                   <Sparkles className="w-4 h-4 mr-2" />
                   {heroCarouselData[currentHeroSlide].subtitle}
                 </Badge>

                 <h1 className="hero-title text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
                   {heroCarouselData[currentHeroSlide].title}
                 </h1>

                 <p className="hero-subtitle text-xl md:text-2xl text-blue-200 leading-relaxed max-w-2xl">
                   {heroCarouselData[currentHeroSlide].description}
                 </p>
               </div>

               {/* Estatísticas impressionantes */}
               <div className="grid grid-cols-3 gap-6">
                 {Object.entries(heroCarouselData[currentHeroSlide].stats).map(([key, value]) => (
                   <div key={key} className="text-center">
                     <div className="text-3xl md:text-4xl font-bold text-white">{value}</div>
                     <div className="text-sm text-blue-200 capitalize">{key}</div>
                   </div>
                 ))}
               </div>

               {/* Features do slide atual */}
               <div className="flex flex-wrap gap-3">
                 {heroCarouselData[currentHeroSlide].features.map((feature, index) => (
                   <Badge key={index} className="bg-white/10 text-white border-white/20 px-3 py-1">
                     <Star className="w-3 h-3 mr-1" />
                     {feature}
                   </Badge>
                 ))}
               </div>

               {/* Botões de ação */}
               <div className="hero-cta flex flex-col sm:flex-row gap-4">
                 <Link to="/login">
                   <Button size="lg" className={`bg-gradient-to-r ${heroCarouselData[currentHeroSlide].color} hover:scale-105 transform transition-all duration-300 shadow-xl text-white font-semibold px-8 py-4`}>
                     <Rocket className="w-5 h-5 mr-2" />
                     Começar Agora
                     <ArrowRight className="w-5 h-5 ml-2" />
                   </Button>
                 </Link>
                 <Button size="lg" variant="outline" className="border-white/30 text-black hover:bg-white/10 backdrop-blur-sm px-8 py-4">
                   <Eye className="w-5 h-5 mr-2" />
                   Ver Demo
                 </Button>
               </div>
             </div>

             {/* Imagem do slide */}
             <div className="relative">
               <div className="relative overflow-hidden rounded-3xl shadow-2xl transform hover:scale-105 transition-all duration-500">
                 <img
                   src={heroCarouselData[currentHeroSlide].image}
                   alt={heroCarouselData[currentHeroSlide].title}
                   className="w-full h-96 lg:h-[500px] object-cover"
                 />
                 <div className={`absolute inset-0 bg-gradient-to-t ${heroCarouselData[currentHeroSlide].color} opacity-30`}></div>

                 {/* Ícone flutuante */}
                 <div className="absolute top-6 right-6">
                   <div className={`w-16 h-16 rounded-full bg-gradient-to-r ${heroCarouselData[currentHeroSlide].color} flex items-center justify-center shadow-lg animate-float`}>
                     {(() => {
                       const IconComponent = heroCarouselData[currentHeroSlide].icon;
                       return <IconComponent className="w-8 h-8 text-white" />;
                     })()}
                   </div>
                 </div>
               </div>

               {/* Efeito de brilho */}
               <div className={`absolute -inset-1 bg-gradient-to-r ${heroCarouselData[currentHeroSlide].color} rounded-3xl blur opacity-30 animate-pulse`}></div>
             </div>
           </div>
         </div>

         {/* Controles do carrossel */}
         <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex items-center space-x-6 z-20">
           {/* Botão anterior */}
           <Button
             variant="ghost"
             size="icon"
             onClick={prevHeroSlide}
             className="w-14 h-14 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 text-white border border-white/20 transition-all duration-300"
           >
             <ChevronLeft className="w-6 h-6" />
           </Button>

           {/* Indicadores */}
           <div className="flex space-x-3">
             {heroCarouselData.map((_, index) => (
               <button
                 key={index}
                 onClick={() => goToHeroSlide(index)}
                 className={`w-4 h-4 rounded-full transition-all duration-300 ${
                   index === currentHeroSlide
                     ? 'bg-white scale-125 shadow-lg'
                     : 'bg-white/40 hover:bg-white/60'
                 }`}
               />
             ))}
           </div>

           {/* Botão próximo */}
           <Button
             variant="ghost"
             size="icon"
             onClick={nextHeroSlide}
             className="w-14 h-14 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 text-white border border-white/20 transition-all duration-300"
           >
             <ChevronRight className="w-6 h-6" />
           </Button>

           {/* Controle play/pause */}
           <Button
             variant="ghost"
             size="icon"
             onClick={() => setIsHeroPlaying(!isHeroPlaying)}
             className="w-14 h-14 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 text-white border border-white/20 ml-4 transition-all duration-300"
           >
             {isHeroPlaying ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6" />}
           </Button>
         </div>

         {/* Indicador de progresso */}
         <div className="absolute bottom-0 left-0 right-0 h-1 bg-white/20">
           <div
             className={`h-full bg-gradient-to-r ${heroCarouselData[currentHeroSlide].color} transition-all duration-300`}
             style={{ width: `${((currentHeroSlide + 1) / heroCarouselData.length) * 100}%` }}
           />
         </div>
       </section>

       {/* Features - Módulos do Sistema */}
       <section id="features" ref={featuresRef} className="py-20 px-6 md:px-12 lg:px-20 bg-gradient-to-br from-gray-50 to-blue-50">
         <div className="max-w-7xl mx-auto">
           <motion.div
             initial={{ opacity: 0, y: 50 }}
             whileInView={{ opacity: 1, y: 0 }}
             viewport={{ once: true, margin: "-100px" }}
             transition={{ duration: 0.8, ease: "easeOut" }}
             className="text-center mb-16"
           >
             <Badge className="bg-blue-100 text-blue-800 px-4 py-2 mb-4">
               <Zap className="w-4 h-4 mr-2" />
               Módulos Integrados
             </Badge>
             <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-6">
               Ecossistema Completo
             </h2>
             <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
               Cada módulo foi desenvolvido para trabalhar em perfeita harmonia, criando uma experiência única de gestão esportiva.
             </p>
           </motion.div>

           <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
             {modulesData.map((module, index) => {
               const IconComponent = module.icon;
               return (
                 <motion.div
                   key={module.id}
                   initial={{ opacity: 0, y: 50 }}
                   whileInView={{ opacity: 1, y: 0 }}
                   transition={{ duration: 0.6, delay: index * 0.1 }}
                   viewport={{ once: true }}
                   className="feature-item group"
                 >
                   <Card className="h-full bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2 overflow-hidden">
                     <CardContent className="p-8 relative">
                       {/* Background gradient */}
                       <div className={`absolute top-0 right-0 w-32 h-32 bg-gradient-to-br ${module.color} opacity-10 rounded-full -mr-16 -mt-16 group-hover:scale-150 transition-transform duration-500`}></div>

                       {/* Ícone principal */}
                       <div className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${module.color} flex items-center justify-center mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                         <IconComponent className="w-8 h-8 text-white" />
                       </div>

                       {/* Conteúdo */}
                       <h3 className="text-2xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-300">
                         {module.title}
                       </h3>
                       <p className="text-gray-600 mb-6 leading-relaxed">
                         {module.description}
                       </p>

                       {/* Features do módulo */}
                       <div className="space-y-2">
                         {module.features.map((feature, featureIndex) => (
                           <div key={featureIndex} className="flex items-center text-sm text-gray-500">
                             <div className={`w-2 h-2 rounded-full bg-gradient-to-r ${module.color} mr-3`}></div>
                             {feature}
                           </div>
                         ))}
                       </div>

                       {/* Botão de ação */}
                       <Button
                         className={`w-full mt-6 bg-gradient-to-r ${module.color} hover:scale-105 transform transition-all duration-300 shadow-lg text-white font-semibold`}
                       >
                         <Eye className="w-4 h-4 mr-2" />
                         Explorar Módulo
                       </Button>
                     </CardContent>
                   </Card>
                 </motion.div>
               );
             })}
           </div>

           {/* Seção de integração */}
           <motion.div
             initial={{ opacity: 0, y: 50 }}
             whileInView={{ opacity: 1, y: 0 }}
             transition={{ duration: 0.8, ease: "easeOut" }}
             viewport={{ once: true, margin: "-100px" }}
             className="mt-20 text-center"
           >
             <Card className="bg-gradient-to-r from-blue-600 to-purple-600 border-0 shadow-2xl overflow-hidden">
               <CardContent className="p-12 relative">
                 {/* Partículas de fundo */}
                 <div className="absolute inset-0">
                   {[...Array(20)].map((_, i) => (
                     <div
                       key={i}
                       className="absolute w-1 h-1 bg-white/30 rounded-full animate-pulse"
                       style={{
                         left: `${Math.random() * 100}%`,
                         top: `${Math.random() * 100}%`,
                         animationDelay: `${Math.random() * 2}s`
                       }}
                     />
                   ))}
                 </div>

                 <div className="relative z-10 text-white">
                   <div className="flex items-center justify-center mb-6">
                     <div className="w-20 h-20 rounded-full bg-white/20 flex items-center justify-center animate-float">
                       <Cpu className="w-10 h-10 text-white" />
                     </div>
                   </div>

                   <h3 className="text-3xl md:text-4xl font-bold mb-4">
                     Integração Perfeita
                   </h3>
                   <p className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
                     Todos os módulos trabalham juntos em tempo real, compartilhando dados e criando uma experiência única de gestão esportiva.
                   </p>

                   <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                     <div className="text-center">
                       <div className="text-3xl font-bold">100%</div>
                       <div className="text-sm text-blue-200">Integração</div>
                     </div>
                     <div className="text-center">
                       <div className="text-3xl font-bold">24/7</div>
                       <div className="text-sm text-blue-200">Sincronização</div>
                     </div>
                     <div className="text-center">
                       <div className="text-3xl font-bold">0s</div>
                       <div className="text-sm text-blue-200">Latência</div>
                     </div>
                     <div className="text-center">
                       <div className="text-3xl font-bold">∞</div>
                       <div className="text-sm text-blue-200">Possibilidades</div>
                     </div>
                   </div>
                 </div>
               </CardContent>
             </Card>
           </motion.div>
         </div>
       </section>

       {/* Módulos Específicos - Carrossel Avançado */}
       <section className="py-20 px-6 md:px-12 lg:px-20 bg-gradient-to-br from-slate-900 via-gray-900 to-black relative overflow-hidden">
         {/* Background effects */}
         <div className="absolute inset-0">
           <div className="absolute top-0 left-0 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
           <div className="absolute bottom-0 right-0 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
           <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-72 h-72 bg-cyan-500/10 rounded-full blur-3xl animate-pulse delay-2000"></div>
         </div>

         {/* Partículas flutuantes */}
         <div className="absolute inset-0">
           {[...Array(30)].map((_, i) => (
             <div
               key={i}
               className="absolute w-1 h-1 bg-white/20 rounded-full animate-pulse"
               style={{
                 left: `${Math.random() * 100}%`,
                 top: `${Math.random() * 100}%`,
                 animationDelay: `${Math.random() * 3}s`,
                 animationDuration: `${2 + Math.random() * 2}s`
               }}
             />
           ))}
         </div>

         <div className="max-w-7xl mx-auto relative z-10">
           {/* Header da seção */}
           <motion.div
             initial={{ opacity: 0, y: 50 }}
             whileInView={{ opacity: 1, y: 0 }}
             viewport={{ once: true, margin: "-100px" }}
             transition={{ duration: 0.8, ease: "easeOut" }}
             className="text-center mb-16"
           >
             <div className="inline-flex items-center px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full text-white border border-white/20 mb-6">
               <Cpu className="w-4 h-4 mr-2" />
               Módulos Específicos
             </div>
             <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
               Funcionalidades Avançadas
             </h2>
             <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
               Explore em detalhes cada módulo do nosso sistema e descubra como podemos revolucionar a gestão do seu clube.
             </p>
           </motion.div>

           {/* Carrossel de módulos específicos */}
           <div
             className="relative"
             onMouseEnter={() => setIsModuleHovered(true)}
             onMouseLeave={() => setIsModuleHovered(false)}
           >
             <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-white/5 to-white/10 backdrop-blur-xl border border-white/20 shadow-2xl">
               {/* Conteúdo do slide atual */}
               <div className="grid lg:grid-cols-2 gap-12 p-8 lg:p-16 min-h-[600px] items-center">
                 {/* Lado esquerdo - Conteúdo */}
                 <div className={`space-y-8 text-white transition-all duration-700 ${moduleTransitioning ? 'opacity-0 transform translate-x-8' : 'opacity-100 transform translate-x-0'}`}>
                   <div className="space-y-6">
                     {/* Badge e título */}
                     <div className="space-y-4">
                       <div className="inline-flex items-center px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full text-white border border-white/20">
                         {(() => {
                           const IconComponent = specificModulesData[currentModuleSlide].icon;
                           return <IconComponent className="w-4 h-4 mr-2" />;
                         })()}
                         {specificModulesData[currentModuleSlide].subtitle}
                       </div>

                       <h3 className="text-3xl lg:text-4xl font-bold leading-tight">
                         {specificModulesData[currentModuleSlide].title}
                       </h3>

                       <p className="text-xl text-gray-300 leading-relaxed">
                         {specificModulesData[currentModuleSlide].description}
                       </p>
                     </div>

                     {/* Estatísticas */}
                     <div className="grid grid-cols-3 gap-6">
                       {Object.entries(specificModulesData[currentModuleSlide].stats).map(([key, value]) => (
                         <div key={key} className="text-center">
                           <div className="text-3xl font-bold text-white">{value}</div>
                           <div className="text-sm text-gray-400 capitalize">{key}</div>
                         </div>
                       ))}
                     </div>

                     {/* Features detalhadas */}
                     <div className="space-y-4">
                       {specificModulesData[currentModuleSlide].features.map((feature, index) => (
                         <motion.div
                           key={index}
                           initial={{ opacity: 0, x: -20 }}
                           animate={{ opacity: 1, x: 0 }}
                           transition={{ duration: 0.5, delay: index * 0.1 }}
                           className="flex items-start space-x-4 p-4 rounded-xl bg-white/5 backdrop-blur-sm border border-white/10 hover:bg-white/10 transition-all duration-300"
                         >
                           <div className={`w-3 h-3 rounded-full bg-gradient-to-r ${specificModulesData[currentModuleSlide].color} mt-2 flex-shrink-0`}></div>
                           <div>
                             <h4 className="font-semibold text-white mb-1">{feature.title}</h4>
                             <p className="text-gray-400 text-sm">{feature.description}</p>
                           </div>
                         </motion.div>
                       ))}
                     </div>

                     {/* Botões de ação */}
                     <div className="flex space-x-4">
                       <Button className={`bg-gradient-to-r ${specificModulesData[currentModuleSlide].color} hover:scale-105 transform transition-all duration-300 shadow-lg text-white font-semibold px-6 py-3`}>
                         <Eye className="w-4 h-4 mr-2" />
                         Ver Demonstração
                       </Button>
                       <Button variant="outline" className="border-white/30 text-black hover:bg-white/10 backdrop-blur-sm px-6 py-3">
                         <Heart className="w-4 h-4 mr-2 text-black" />
                         Saiba Mais
                       </Button>
                     </div>
                   </div>
                 </div>

                 {/* Lado direito - Imagem */}
                 <div className={`relative transition-all duration-700 ${moduleTransitioning ? 'opacity-0 transform translate-x-8 scale-95' : 'opacity-100 transform translate-x-0 scale-100'}`}>
                   <div className="relative overflow-hidden rounded-2xl shadow-2xl transform hover:scale-105 transition-all duration-500">
                     <img
                       src={specificModulesData[currentModuleSlide].image}
                       alt={specificModulesData[currentModuleSlide].title}
                       className="w-full h-96 lg:h-[500px] object-cover"
                     />
                     <div className={`absolute inset-0 bg-gradient-to-t ${specificModulesData[currentModuleSlide].color} opacity-30`}></div>

                     {/* Ícone flutuante */}
                     <div className="absolute top-6 right-6">
                       <div className={`w-16 h-16 rounded-full bg-gradient-to-r ${specificModulesData[currentModuleSlide].color} flex items-center justify-center shadow-lg animate-float`}>
                         {(() => {
                           const IconComponent = specificModulesData[currentModuleSlide].icon;
                           return <IconComponent className="w-8 h-8 text-white" />;
                         })()}
                       </div>
                     </div>
                   </div>

                   {/* Efeito de brilho */}
                   <div className={`absolute -inset-1 bg-gradient-to-r ${specificModulesData[currentModuleSlide].color} rounded-2xl blur opacity-30 animate-pulse`}></div>
                 </div>
               </div>
             </div>

             {/* Controles do carrossel */}
             <div className="flex items-center justify-center mt-8 space-x-6">
               {/* Botão anterior */}
               <Button
                 variant="ghost"
                 size="icon"
                 onClick={prevModuleSlide}
                 disabled={moduleTransitioning}
                 className="w-14 h-14 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 text-white border border-white/20 transition-all duration-300 disabled:opacity-50"
               >
                 <ChevronLeft className="w-6 h-6" />
               </Button>

               {/* Indicadores */}
               <div className="flex space-x-3">
                 {specificModulesData.map((_, index) => (
                   <button
                     key={index}
                     onClick={() => goToModuleSlide(index)}
                     disabled={moduleTransitioning}
                     className={`w-4 h-4 rounded-full transition-all duration-300 ${
                       index === currentModuleSlide
                         ? 'bg-white scale-125 shadow-lg'
                         : 'bg-white/40 hover:bg-white/60'
                     } disabled:opacity-50`}
                   />
                 ))}
               </div>

               {/* Botão próximo */}
               <Button
                 variant="ghost"
                 size="icon"
                 onClick={nextModuleSlide}
                 disabled={moduleTransitioning}
                 className="w-14 h-14 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 text-white border border-white/20 transition-all duration-300 disabled:opacity-50"
               >
                 <ChevronRight className="w-6 h-6" />
               </Button>

               {/* Controle play/pause */}
               <Button
                 variant="ghost"
                 size="icon"
                 onClick={() => setIsModulePlaying(!isModulePlaying)}
                 className="w-14 h-14 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 text-white border border-white/20 ml-4 transition-all duration-300"
               >
                 {isModulePlaying ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6" />}
               </Button>
             </div>

             {/* Indicador de progresso */}
             <div className="mt-6 h-1 bg-white/20 rounded-full overflow-hidden">
               <div
                 className={`h-full bg-gradient-to-r ${specificModulesData[currentModuleSlide].color} transition-all duration-300 rounded-full`}
                 style={{ width: `${((currentModuleSlide + 1) / specificModulesData.length) * 100}%` }}
               />
             </div>
           </div>
         </div>
       </section>

       {/* Testimonials - Depoimentos Modernos */}
       <section id="testimonials" className="py-20 px-6 md:px-12 lg:px-20 bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900 relative overflow-hidden">
         {/* Background effects */}
         <div className="absolute inset-0">
           <div className="absolute top-0 left-0 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"></div>
           <div className="absolute bottom-0 right-0 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl"></div>
         </div>

         <div className="max-w-7xl mx-auto relative z-10">
           <motion.div
             initial={{ opacity: 0, y: 50 }}
             whileInView={{ opacity: 1, y: 0 }}
             viewport={{ once: true, margin: "-100px" }}
             transition={{ duration: 0.8, ease: "easeOut" }}
             className="text-center mb-16"
           >
             <Badge className="bg-white/20 text-white border-white/30 px-4 py-2 mb-4">
               <Heart className="w-4 h-4 mr-2" />
               Depoimentos
             </Badge>
             <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
               Clubes que Confiam em Nós
             </h2>
             <p className="text-xl text-blue-200 max-w-3xl mx-auto leading-relaxed">
               Mais de 500 clubes em todo o Brasil já transformaram sua gestão com nossa plataforma.
             </p>
           </motion.div>

           <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
             {[
               {
                 name: "Flamengo FC",
                 role: "Diretor Executivo",
                 avatar: "FC",
                 color: "from-red-500 to-red-600",
                 quote: "A plataforma revolucionou nossa gestão esportiva. Conseguimos integrar todos os departamentos e ter uma visão completa do clube.",
                 rating: 5,
                 improvement: "+40% eficiência"
               },
               {
                 name: "Santos Club",
                 role: "Coordenador de Base",
                 avatar: "SC",
                 color: "from-green-500 to-green-600",
                 quote: "O módulo de categorias de base é fantástico. Conseguimos acompanhar o desenvolvimento dos jovens talentos de forma muito mais eficiente.",
                 rating: 5,
                 improvement: "+60% desenvolvimento"
               },
               {
                 name: "Palmeiras Futebol",
                 role: "Analista de Performance",
                 avatar: "PF",
                 color: "from-blue-500 to-blue-600",
                 quote: "As estatísticas e análises de desempenho nos ajudaram a identificar pontos de melhoria que passavam despercebidos antes.",
                 rating: 5,
                 improvement: "+35% performance"
               }
             ].map((testimonial, index) => (
               <motion.div
                 key={index}
                 initial={{ opacity: 0, y: 50 }}
                 whileInView={{ opacity: 1, y: 0 }}
                 transition={{ duration: 0.6, delay: index * 0.2 }}
                 viewport={{ once: true }}
                 className="group"
               >
                 <Card className="h-full bg-white/10 backdrop-blur-md border border-white/20 shadow-2xl hover:shadow-3xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2">
                   <CardContent className="p-8 relative">
                     {/* Quote icon */}
                     <div className="absolute top-4 right-4 opacity-20">
                       <div className="text-6xl text-white font-serif">"</div>
                     </div>

                     {/* Rating stars */}
                     <div className="flex mb-4">
                       {[...Array(testimonial.rating)].map((_, i) => (
                         <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                       ))}
                     </div>

                     {/* Quote */}
                     <p className="text-white text-lg leading-relaxed mb-6 italic">
                       "{testimonial.quote}"
                     </p>

                     {/* Improvement badge */}
                     <Badge className="bg-green-500/20 text-green-300 border-green-500/30 mb-6">
                       <ArrowUp className="w-3 h-3 mr-1" />
                       {testimonial.improvement}
                     </Badge>

                     {/* Author */}
                     <div className="flex items-center">
                       <div className={`w-14 h-14 rounded-full bg-gradient-to-r ${testimonial.color} flex items-center justify-center text-white font-bold text-lg shadow-lg`}>
                         {testimonial.avatar}
                       </div>
                       <div className="ml-4">
                         <h4 className="font-bold text-white text-lg">{testimonial.name}</h4>
                         <p className="text-blue-200 text-sm">{testimonial.role}</p>
                       </div>
                     </div>
                   </CardContent>
                 </Card>
               </motion.div>
             ))}
           </div>

           {/* Stats section */}
           <motion.div
             initial={{ opacity: 0, y: 50 }}
             whileInView={{ opacity: 1, y: 0 }}
             transition={{ duration: 0.8 }}
             viewport={{ once: true }}
             className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-8 text-center"
           >
             {[
               { number: "500+", label: "Clubes Ativos", icon: Trophy },
               { number: "10K+", label: "Jogadores", icon: Users },
               { number: "50K+", label: "Partidas", icon: Activity },
               { number: "99.9%", label: "Uptime", icon: Wifi }
             ].map((stat, index) => {
               const IconComponent = stat.icon;
               return (
                 <div key={index} className="text-white">
                   <div className="w-16 h-16 rounded-full bg-white/10 flex items-center justify-center mx-auto mb-4 animate-float">
                     <IconComponent className="w-8 h-8" />
                   </div>
                   <div className="text-3xl md:text-4xl font-bold mb-2">{stat.number}</div>
                   <div className="text-blue-200">{stat.label}</div>
                 </div>
               );
             })}
           </motion.div>
         </div>
       </section>

       {/* Pricing - Planos Modernos */}
       <section id="pricing" ref={pricingRef} className="py-20 px-6 md:px-12 lg:px-20 bg-gradient-to-br from-gray-50 to-white">
         <div className="max-w-7xl mx-auto">
           <motion.div
             initial={{ opacity: 0, y: 50 }}
             whileInView={{ opacity: 1, y: 0 }}
             viewport={{ once: true, margin: "-100px" }}
             transition={{ duration: 0.8, ease: "easeOut" }}
             className="text-center mb-16"
           >
             <Badge className="bg-purple-100 text-purple-800 px-4 py-2 mb-4">
               <Database className="w-4 h-4 mr-2" />
               Planos Flexíveis
             </Badge>
             <h2 className="pricing-title text-4xl md:text-5xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent mb-6">
               Escolha Seu Plano
             </h2>
             <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
               Planos que crescem com seu clube. Comece grátis e evolua conforme suas necessidades.
             </p>
           </motion.div>

           <div className="grid md:grid-cols-3 gap-8 lg:gap-12">
             {/* Plano Básico */}
             <motion.div
               initial={{ opacity: 0, y: 50 }}
               whileInView={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.6, delay: 0.1, ease: "easeOut" }}
               viewport={{ once: true, margin: "-50px" }}
               className="pricing-card group"
             >
               <Card className="h-full bg-white border-2 border-gray-200 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2 relative overflow-hidden">
                 <div className="absolute top-0 inset-x-0 h-1 bg-gradient-to-r from-blue-500 to-blue-600"></div>
                 <CardContent className="p-8">
                   <div className="text-center mb-8">
                     <div className="w-16 h-16 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                       <Users className="w-8 h-8 text-white" />
                     </div>
                     <h3 className="text-2xl font-bold text-gray-900 mb-2">Básico</h3>
                     <div className="mb-4">
                       <span className="text-5xl font-bold text-gray-900">R$299</span>
                       <span className="text-gray-500 text-lg">/mês</span>
                     </div>
                     <p className="text-gray-600">Ideal para clubes pequenos e equipes em formação.</p>
                   </div>

                   <ul className="space-y-4 mb-8">
                     {[
                       "Gestão de elenco",
                       "Partidas e estatísticas",
                       "Treinamentos básicos",
                       "1 Temporada",
                       "Até 30 jogadores"
                     ].map((feature, index) => (
                       <li key={index} className="flex items-center">
                         <div className="w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                           <Check className="w-3 h-3 text-blue-600" />
                         </div>
                         <span className="text-gray-700">{feature}</span>
                       </li>
                     ))}
                   </ul>

                   <Button className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-3 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                     <Rocket className="w-4 h-4 mr-2" />
                     Começar Grátis
                   </Button>
                 </CardContent>
               </Card>
             </motion.div>

             {/* Plano Profissional - Destaque */}
             <motion.div
               initial={{ opacity: 0, y: 50 }}
               whileInView={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
               viewport={{ once: true, margin: "-50px" }}
               className="pricing-card group relative"
             >
               {/* Badge Popular */}
               <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                 <Badge className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-2 text-sm font-bold shadow-lg">
                   <Star className="w-4 h-4 mr-1" />
                   MAIS POPULAR
                 </Badge>
               </div>

               <Card className="h-full bg-gradient-to-br from-purple-600 to-blue-600 border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 transform scale-105 hover:scale-110 hover:-translate-y-4 relative overflow-hidden">
                 {/* Partículas de fundo */}
                 <div className="absolute inset-0">
                   {[...Array(15)].map((_, i) => (
                     <div
                       key={i}
                       className="absolute w-1 h-1 bg-white/30 rounded-full animate-pulse"
                       style={{
                         left: `${Math.random() * 100}%`,
                         top: `${Math.random() * 100}%`,
                         animationDelay: `${Math.random() * 2}s`
                       }}
                     />
                   ))}
                 </div>

                 <CardContent className="p-8 relative z-10 text-white">
                   <div className="text-center mb-8">
                     <div className="w-16 h-16 rounded-full bg-white/20 flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                       <Trophy className="w-8 h-8 text-white" />
                     </div>
                     <h3 className="text-2xl font-bold mb-2">Profissional</h3>
                     <div className="mb-4">
                       <span className="text-5xl font-bold">R$699</span>
                       <span className="text-purple-200 text-lg">/mês</span>
                     </div>
                     <p className="text-purple-100">Perfeito para clubes em crescimento com necessidades completas.</p>
                   </div>

                   <ul className="space-y-4 mb-8">
                     {[
                       "Tudo do plano Básico",
                       "Departamento médico",
                       "Gestão financeira",
                       "5 Temporadas",
                       "Até 100 jogadores",
                       "Suporte prioritário"
                     ].map((feature, index) => (
                       <li key={index} className="flex items-center">
                         <div className="w-5 h-5 rounded-full bg-white/20 flex items-center justify-center mr-3">
                           <Check className="w-3 h-3 text-white" />
                         </div>
                         <span className="text-white">{feature}</span>
                       </li>
                     ))}
                   </ul>

                   <Button className="w-full bg-white text-purple-600 hover:bg-gray-100 font-bold py-3 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                     <Flame className="w-4 h-4 mr-2" />
                     Assinar Agora
                     <ArrowRight className="w-4 h-4 ml-2" />
                   </Button>
                 </CardContent>
               </Card>
             </motion.div>

             {/* Plano Enterprise */}
             <motion.div
               initial={{ opacity: 0, y: 50 }}
               whileInView={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.6, delay: 0.3, ease: "easeOut" }}
               viewport={{ once: true, margin: "-50px" }}
               className="pricing-card group"
             >
               <Card className="h-full bg-white border-2 border-gray-200 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2 relative overflow-hidden">
                 <div className="absolute top-0 inset-x-0 h-1 bg-gradient-to-r from-green-500 to-emerald-600"></div>
                 <CardContent className="p-8">
                   <div className="text-center mb-8">
                     <div className="w-16 h-16 rounded-full bg-gradient-to-r from-green-500 to-emerald-600 flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                       <Award className="w-8 h-8 text-white" />
                     </div>
                     <h3 className="text-2xl font-bold text-gray-900 mb-2">Enterprise</h3>
                     <div className="mb-4">
                       <span className="text-5xl font-bold text-gray-900">R$999</span>
                       <span className="text-gray-500 text-lg">/mês</span>
                     </div>
                     <p className="text-gray-600">Solução completa para clubes profissionais de alto rendimento.</p>
                   </div>

                   <ul className="space-y-4 mb-8">
                     {[
                       "Tudo do plano Profissional",
                       "Base juvenil completa",
                       "Analytics avançado",
                       "Temporadas ilimitadas",
                       "Jogadores ilimitados",
                       "Suporte 24/7"
                     ].map((feature, index) => (
                       <li key={index} className="flex items-center">
                         <div className="w-5 h-5 rounded-full bg-green-100 flex items-center justify-center mr-3">
                           <Check className="w-3 h-3 text-green-600" />
                         </div>
                         <span className="text-gray-700">{feature}</span>
                       </li>
                     ))}
                   </ul>

                   <Button className="w-full bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold py-3 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                     <Globe className="w-4 h-4 mr-2" />
                     Contate-nos
                   </Button>
                 </CardContent>
               </Card>
             </motion.div>
           </div>

           {/* Garantia e benefícios */}
           <motion.div
             initial={{ opacity: 0, y: 50 }}
             whileInView={{ opacity: 1, y: 0 }}
             transition={{ duration: 0.8, ease: "easeOut" }}
             viewport={{ once: true, margin: "-100px" }}
             className="mt-16 text-center"
           >
             <div className="grid md:grid-cols-3 gap-8">
               <div className="flex items-center justify-center space-x-3">
                 <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                   <Check className="w-6 h-6 text-green-600" />
                 </div>
                 <div className="text-left">
                   <div className="font-semibold text-gray-900">30 dias grátis</div>
                   <div className="text-sm text-gray-600">Teste sem compromisso</div>
                 </div>
               </div>
               <div className="flex items-center justify-center space-x-3">
                 <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                   <Lock className="w-6 h-6 text-blue-600" />
                 </div>
                 <div className="text-left">
                   <div className="font-semibold text-gray-900">Dados seguros</div>
                   <div className="text-sm text-gray-600">Criptografia militar</div>
                 </div>
               </div>
               <div className="flex items-center justify-center space-x-3">
                 <div className="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center">
                   <Heart className="w-6 h-6 text-purple-600" />
                 </div>
                 <div className="text-left">
                   <div className="font-semibold text-gray-900">Suporte dedicado</div>
                   <div className="text-sm text-gray-600">Equipe especializada</div>
                 </div>
               </div>
             </div>
           </motion.div>
         </div>
       </section>

       {/* Call to action */}
       <section className="py-20 px-6 md:px-12 lg:px-20 bg-team-blue text-white">
         <div className="max-w-5xl mx-auto text-center">
           <motion.h2
             initial={{ opacity: 0, y: 30 }}
             whileInView={{ opacity: 1, y: 0 }}
             transition={{ duration: 0.5 }}
             viewport={{ once: true }}
             className="text-3xl md:text-4xl font-bold mb-6"
           >
             Pronto para transformar a gestão do seu clube?
           </motion.h2>
           <motion.p
             initial={{ opacity: 0, y: 20 }}
             whileInView={{ opacity: 1, y: 0 }}
             transition={{ duration: 0.5, delay: 0.2 }}
             viewport={{ once: true }}
             className="text-xl text-blue-100 mb-10 max-w-3xl mx-auto"
           >
             Junte-se a centenas de clubes que já melhoraram seu desempenho com o Game Day Nexus.
           </motion.p>
           <motion.div
             initial={{ opacity: 0, y: 20 }}
             whileInView={{ opacity: 1, y: 0 }}
             transition={{ duration: 0.5, delay: 0.4 }}
             viewport={{ once: true }}
             className="flex flex-col sm:flex-row gap-4 justify-center"
           >
             <Link to="/">
               <Button size="lg" className="bg-white text-team-blue hover:bg-gray-100 font-medium px-8">
                 Começar agora
               </Button>
             </Link>
             <Button size="lg" variant="outline" className="border-white text-black hover:bg-white/20">
               Agendar demonstração
             </Button>
           </motion.div>
         </div>
       </section>

       {/* Footer */}
       <footer className="bg-gray-900 text-gray-300 py-12 px-6 md:px-12 lg:px-20">
         <div className="max-w-7xl mx-auto">
           <div className="grid md:grid-cols-4 gap-8">
             <div>
               <div className="flex items-center gap-2 mb-4">
                 <div className="w-10 h-10 rounded-full bg-white flex items-center justify-center">
                   <span className="text-team-blue font-bold">GDN</span>
                 </div>
                 <span className="font-bold text-xl text-white">Game Day Nexus</span>
               </div>
               <p className="text-gray-400 mb-4">
                 A plataforma completa de gestão esportiva para clubes de futebol.
               </p>
             </div>

             <div>
               <h3 className="font-semibold text-white mb-4">Produto</h3>
               <ul className="space-y-2">
                 <li><a href="#features" className="hover:text-white transition-colors">Recursos</a></li>
                 <li><a href="#pricing" className="hover:text-white transition-colors">Planos</a></li>
                 <li><a href="#" className="hover:text-white transition-colors">Atualizações</a></li>
                 <li><a href="#" className="hover:text-white transition-colors">FAQ</a></li>
               </ul>
             </div>

             <div>
               <h3 className="font-semibold text-white mb-4">Empresa</h3>
               <ul className="space-y-2">
                 <li><a href="#" className="hover:text-white transition-colors">Sobre nós</a></li>
                 <li><a href="#" className="hover:text-white transition-colors">Contato</a></li>
                 <li><a href="#" className="hover:text-white transition-colors">Blog</a></li>
                 <li><a href="#" className="hover:text-white transition-colors">Termos</a></li>
               </ul>
             </div>

             <div>
               <h3 className="font-semibold text-white mb-4">Contato</h3>
               <ul className="space-y-2">
                 <li className="flex items-center">
                   <span><EMAIL></span>
                 </li>
                 <li className="flex items-center">
                   <span>+55 (11) 3456-7890</span>
                 </li>
               </ul>
             </div>
           </div>

           <div className="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
             <p>&copy; 2025 Game Day Nexus. Todos os direitos reservados.</p>
             <div className="flex gap-4 mt-4 md:mt-0">
               <a href="#" className="hover:text-white transition-colors">Instagram</a>
               <a href="#" className="hover:text-white transition-colors">Twitter</a>
               <a href="#" className="hover:text-white transition-colors">LinkedIn</a>
               <a href="#" className="hover:text-white transition-colors">YouTube</a>
             </div>
           </div>
         </div>
       </footer>
     </div>
   );
 }