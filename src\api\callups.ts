import { supabase } from "@/integrations/supabase/client";
import { hasPermission } from "./permissions";
import { withPermission, withAuditLog } from "./middleware";
import { jsPDF } from "jspdf";
import html2canvas from "html2canvas";
import { ClubInfo } from "./api";
import { formatCPF, truncatePlayerName } from "@/utils/formatters";
import { filterPlayersByRoleCategory } from "@/utils/callupRoles";
import { generateMatchAccommodationPDF } from "@/utils/pdfGenerator";

// Permissions constants
export const CALLUP_PERMISSIONS = {
  VIEW: "callups.view",
  CREATE: "callups.create",
  EDIT: "callups.edit",
  DELETE: "callups.delete",
};

// Types
export type Callup = {
  id: number;
  club_id: number;
  category_id: number;
  tournament_type: string;
  match_date: string;
  home_club_logo?: string;
  away_club_logo?: string;
  competition_image?: string;
  stadium_image?: string; // Nova imagem do estádio
  match_location: string;
  hotel_image?: string;
  bus_image?: string;
  uniform_image?: string;
  match_schedule?: string;
  hotel_control?: string;
  sponsor_image1?: string;
  sponsor_image2?: string;
  sponsor_image3?: string;
  sponsor_image4?: string;
  created_at?: string;
  updated_at?: string;
};

export type CallupPlayer = {
  id: number;
  club_id: number;
  callup_id: number;
  player_id?: string;
  user_id?: string;
  role: string;
  created_at?: string;
  player_name?: string; // For joined data
  player_number?: number; // Número da camisa
  player_cpf?: string; // CPF do jogador
  player_birthdate?: string | null; // Data de nascimento do jogador
  collaborator_cpf?: string | null; // CPF do colaborador
  collaborator_birthdate?: string | null; // Data de nascimento do colaborador
  user_name?: string; // For joined data
  name?: string; // Nome manual
};

// Functions for managing callups
export async function getCallups(clubId: number, categoryId?: number): Promise<Callup[]> {
  let query = supabase
    .from("callups")
    .select("*")
    .eq("club_id", clubId)
    .order("match_date", { ascending: false });

  if (categoryId) {
    query = query.eq("category_id", categoryId);
  }

  const { data, error } = await query;

  if (error) {
    console.error("Erro ao buscar convocações:", error);
    throw new Error(`Erro ao buscar convocações: ${error.message}`);
  }

  return data || [];
}

// Function to get upcoming callups (future callups that haven't passed 1 day)
export async function getUpcomingCallups(clubId: number, categoryId?: number): Promise<Callup[]> {
  const today = new Date();
  const oneDayAgo = new Date(today.getTime() - 24 * 60 * 60 * 1000);

  let query = supabase
    .from("callups")
    .select("*, categories(*)")
    .eq("club_id", clubId)
    .gte("match_date", oneDayAgo.toISOString())
    .order("match_date", { ascending: true });

  if (categoryId) {
    query = query.eq("category_id", categoryId);
  }

  const { data, error } = await query;

  if (error) {
    console.error("Erro ao buscar convocações futuras:", error);
    throw new Error(`Erro ao buscar convocações futuras: ${error.message}`);
  }

  return data || [];
}

// Function to get upcoming callups for a specific player
export async function getPlayerUpcomingCallups(clubId: number, playerId: string): Promise<Callup[]> {
  try {
    // Primeiro, buscamos as categorias do jogador
    const { data: playerCategories, error: categoriesError } = await supabase
      .from("player_categories")
      .select("category_id")
      .eq("club_id", clubId.toString())
      .eq("player_id", playerId);

    if (categoriesError) {
      console.error("Erro ao buscar categorias do jogador:", categoriesError);
      throw new Error(`Erro ao buscar categorias do jogador: ${categoriesError.message}`);
    }

    if (!playerCategories || playerCategories.length === 0) {
      return [];
    }

    const categoryIds = playerCategories.map((pc: { category_id: number }) => pc.category_id);

    // Buscamos as convocações para as categorias do jogador
    const { data, error } = await supabase
      .from("callups")
      .select("*, categories(*)")
      .eq("club_id", clubId.toString())
      .in("category_id", categoryIds)
      .order("match_date", { ascending: true });

    if (error) {
      console.error("Erro ao buscar convocações do jogador:", error);
      throw new Error(`Erro ao buscar convocações do jogador: ${error.message}`);
    }

    if (!data) return [];

    // Filtramos as convocações manualmente para garantir a lógica correta
    const now = new Date();
    return data.filter((callup: any) => {
      if (!callup.match_date) return false;
      
      const matchDate = new Date(callup.match_date);
      const oneDayAfterMatchDate = new Date(matchDate.getTime() + 24 * 60 * 60 * 1000);
      
      // Inclui a convocação se a data atual for anterior a 1 dia após o jogo
      return now < oneDayAfterMatchDate;
    });
  } catch (error) {
    console.error("Erro em getPlayerUpcomingCallups:", error);
    throw error;
  }
}

export async function getCallupById(clubId: number, callupId: number): Promise<Callup> {
  const { data, error } = await supabase
    .from("callups")
    .select("*")
    .eq("club_id", clubId)
    .eq("id", callupId)
    .single();

  if (error) {
    console.error(`Erro ao buscar convocação ${callupId}:`, error);
    throw new Error(`Erro ao buscar convocação: ${error.message}`);
  }

  return data;
}

export async function createCallup(
  clubId: number,
  callupData: Omit<Callup, "id" | "club_id" | "created_at" | "updated_at">,
  userId?: string
): Promise<Callup> {
  console.log("Creating callup with data:", JSON.stringify(callupData, null, 2));

  // Log image URLs specifically
  if (callupData.home_club_logo) console.log("home_club_logo:", callupData.home_club_logo);
  if (callupData.away_club_logo) console.log("away_club_logo:", callupData.away_club_logo);
  if (callupData.competition_image) console.log("competition_image:", callupData.competition_image);
  if (callupData.uniform_image) console.log("uniform_image:", callupData.uniform_image);
  if (callupData.hotel_image) console.log("hotel_image:", callupData.hotel_image);
  if (callupData.bus_image) console.log("bus_image:", callupData.bus_image);

  // Se userId não for fornecido, não verificamos permissões (para compatibilidade)
  if (!userId) {
    const insertData = {
      club_id: clubId,
      ...callupData,
    };

    console.log("Creating callup without permission check:", insertData);

    const { data, error } = await supabase
      .from("callups")
      .insert(insertData)
      .select()
      .single();

    if (error) {
      console.error("Erro ao criar convocação:", error);
      throw new Error(`Erro ao criar convocação: ${error.message}`);
    }

    console.log("Callup created successfully:", data);
    return data;
  }

  // Com userId, verificamos permissões e registramos no log de auditoria
  return withPermission(
    clubId,
    userId,
    CALLUP_PERMISSIONS.CREATE,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "callup.create",
        { category_id: callupData.category_id, tournament_type: callupData.tournament_type },
        async () => {
          const insertData = {
            club_id: clubId,
            ...callupData,
          };

          console.log("Creating callup with permission check:", insertData);

          const { data, error } = await supabase
            .from("callups")
            .insert(insertData)
            .select()
            .single();

          if (error) {
            console.error(`Erro ao criar convocação:`, error);
            throw new Error(`Erro ao criar convocação: ${error.message}`);
          }

          console.log("Callup created successfully with permission check:", data);
          return data;
        }
      );
    }
  );
}

export async function updateCallup(
  clubId: number,
  callupId: number,
  callupData: Partial<Omit<Callup, "id" | "club_id" | "created_at" | "updated_at">>,
  userId?: string
): Promise<Callup> {
  console.log("Updating callup with data:", JSON.stringify(callupData, null, 2));

  // Log image URLs specifically
  if (callupData.home_club_logo) console.log("home_club_logo:", callupData.home_club_logo);
  if (callupData.away_club_logo) console.log("away_club_logo:", callupData.away_club_logo);
  if (callupData.competition_image) console.log("competition_image:", callupData.competition_image);
  if (callupData.uniform_image) console.log("uniform_image:", callupData.uniform_image);
  if (callupData.hotel_image) console.log("hotel_image:", callupData.hotel_image);
  if (callupData.bus_image) console.log("bus_image:", callupData.bus_image);

  // Se userId não for fornecido, não verificamos permissões (para compatibilidade)
  if (!userId) {
    const updateData = {
      ...callupData,
      updated_at: new Date().toISOString(),
    };

    console.log("Updating callup without permission check:", updateData);

    const { data, error } = await supabase
      .from("callups")
      .update(updateData)
      .eq("club_id", clubId)
      .eq("id", callupId)
      .select()
      .single();

    if (error) {
      console.error(`Erro ao atualizar convocação ${callupId}:`, error);
      throw new Error(`Erro ao atualizar convocação: ${error.message}`);
    }

    console.log("Callup updated successfully:", data);
    return data;
  }

  // Com userId, verificamos permissões e registramos no log de auditoria
  return withPermission(
    clubId,
    userId,
    CALLUP_PERMISSIONS.EDIT,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "callup.update",
        { callup_id: callupId },
        async () => {
          const updateData = {
            ...callupData,
            updated_at: new Date().toISOString(),
          };

          console.log("Updating callup with permission check:", updateData);

          const { data, error } = await supabase
            .from("callups")
            .update(updateData)
            .eq("club_id", clubId)
            .eq("id", callupId)
            .select()
            .single();

          if (error) {
            console.error(`Erro ao atualizar convocação:`, error);
            throw new Error(`Erro ao atualizar convocação: ${error.message}`);
          }

          console.log("Callup updated successfully with permission check:", data);
          return data;
        }
      );
    }
  );
}

export async function deleteCallup(
  clubId: number,
  callupId: number,
  userId?: string
): Promise<void> {
  // Se userId não for fornecido, não verificamos permissões (para compatibilidade)
  if (!userId) {
    const { error } = await supabase
      .from("callups")
      .delete()
      .eq("club_id", clubId)
      .eq("id", callupId);

    if (error) {
      console.error(`Erro ao excluir convocação ${callupId}:`, error);
      throw new Error(`Erro ao excluir convocação: ${error.message}`);
    }

    return;
  }

  // Com userId, verificamos permissões e registramos no log de auditoria
  return withPermission(
    clubId,
    userId,
    CALLUP_PERMISSIONS.DELETE,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "callup.delete",
        { callup_id: callupId },
        async () => {
          // Primeiro excluir os jogadores associados
          const { error: playersError } = await supabase
            .from("callup_players")
            .delete()
            .eq("club_id", clubId)
            .eq("callup_id", callupId);

          if (playersError) {
            throw new Error(`Erro ao excluir jogadores da convocação: ${playersError.message}`);
          }

          // Depois excluir a convocação
          const { error } = await supabase
            .from("callups")
            .delete()
            .eq("club_id", clubId)
            .eq("id", callupId);

          if (error) {
            throw new Error(`Erro ao excluir convocação: ${error.message}`);
          }
        }
      );
    }
  );
}

export async function getCallupPlayers(
  clubId: number,
  callupId: number
): Promise<CallupPlayer[]> {
  try {
    // Buscar os jogadores da convocação com join para players incluindo CPF e data de nascimento
    const { data, error } = await supabase
      .from("callup_players")
      .select(`
        *,
        players:player_id (id, name, number, cpf_number, birthdate)
      `)
      .eq("club_id", clubId)
      .eq("callup_id", callupId);

    if (error) {
      console.error(`Erro ao buscar jogadores da convocação ${callupId}:`, error);
      throw new Error(`Erro ao buscar jogadores da convocação: ${error.message}`);
    }

    // Buscar informações dos usuários para os membros da comissão
    const userIds = data
      .filter(item => item.user_id && !item.player_id && !item.name)
      .map(item => item.user_id)
      .filter(id => id && typeof id === 'string' && id.length > 10); // UUIDs são longos

    let userNames: Record<string, string> = {};

    if (userIds.length > 0) {
      try {
        const { data: usersData } = await supabase
          .from('profiles')
          .select('id, name')
          .in('id', userIds);

        if (usersData) {
          userNames = usersData.reduce((acc: Record<string, string>, user: any) => {
            if (user.name) {
              acc[user.id] = user.name;
            }
            return acc;
          }, {});
        }
      } catch (userError) {
        console.warn("Erro ao buscar nomes de usuários:", userError);
      }
    }

    // Buscar informações dos colaboradores tanto por ID quanto por nome
    const collaboratorIds = data
      .filter(item => item.user_id && !item.player_id && !isNaN(Number(item.user_id)))
      .map(item => Number(item.user_id));

    const collaboratorNames = data
      .filter(item => !item.player_id && item.name)
      .map(item => item.name as string);

    let collaboratorInfo: Record<string, { name: string; cpf: string | null; birthdate: string | null }> = {};

    if (collaboratorIds.length > 0) {
      try {
        const { data: collaboratorsData } = await supabase
          .from('collaborators')
          .select('id, full_name, cpf, birth_date')
          .in('id', collaboratorIds);

        if (collaboratorsData) {
          collaboratorsData.forEach((collab: any) => {
            collaboratorInfo[collab.id.toString()] = {
              name: collab.full_name,
              cpf: collab.cpf || null,
              birthdate: collab.birth_date || null
            };
          });
        }
      } catch (collabError) {
        console.warn("Erro ao buscar colaboradores por ID:", collabError);
      }
    }

    if (collaboratorNames.length > 0) {
      try {
        const { data: collaboratorsByName } = await supabase
          .from('collaborators')
          .select('id, full_name, cpf, birth_date')
          .in('full_name', collaboratorNames);

        if (collaboratorsByName) {
          collaboratorsByName.forEach((collab: any) => {
            collaboratorInfo[collab.full_name] = {
              name: collab.full_name,
              cpf: collab.cpf || null,
              birthdate: collab.birth_date || null
            };
          });
        }
      } catch (collabError) {
        console.warn("Erro ao buscar colaboradores por nome:", collabError);
      }
    }

    // Processar os dados para um formato mais amigável
    return (data || []).map((item: any) => {
      // Determinar o nome do usuário ou colaborador
      let userName = null;
      let playerCpf = null;
      let playerBirth = null;
      let collaboratorCpf = null;
      let collaboratorBirth = null;

      // Se tem nome manual, usar ele primeiro
      if (item.name) {
        const infoByName = collaboratorInfo[item.name];
        if (infoByName) {
          userName = infoByName.name;
          collaboratorCpf = infoByName.cpf;
          collaboratorBirth = infoByName.birthdate;
        } else {
          userName = item.name;
        }
      } else if (item.user_id) {
        // Verificar se é um colaborador (ID numérico) ou usuário (UUID)
        if (!isNaN(Number(item.user_id))) {
          const info = collaboratorInfo[item.user_id] || collaboratorInfo[item.name];
          if (info) {
            userName = info.name;
            collaboratorCpf = info.cpf;
            collaboratorBirth = info.birthdate;
          }
        } else {
          userName = userNames[item.user_id];
        }
      }

      // Se é um jogador, pegar o CPF
      if (item.players?.cpf_number) {
        playerCpf = item.players.cpf_number;
        playerBirth = item.players?.birthdate || null;
      }

      return {
        id: item.id,
        club_id: item.club_id,
        callup_id: item.callup_id,
        player_id: item.player_id,
        user_id: item.user_id,
        role: item.role,
        created_at: item.created_at,
        player_name: item.players?.name || null,
        player_number: item.players?.number || null,
        player_cpf: playerCpf,
        player_birthdate: playerBirth,
        collaborator_cpf: collaboratorCpf,
        collaborator_birthdate: collaboratorBirth,
        user_name: userName,
        name: item.name || null
      };
    });
  } catch (error) {
    console.error(`Erro ao buscar jogadores da convocação ${callupId}:`, error);
    throw new Error(`Erro ao buscar jogadores da convocação: ${error instanceof Error ? error.message : String(error)}`);
  }
}

export async function addPlayerToCallup(
  clubId: number,
  callupId: number,
  playerId?: string | null,
  role?: string | null,
  currentUserId?: string,
  userId?: string | number | null,
  userName?: string | null,
  playerName?: string | null
): Promise<CallupPlayer> {
  // Validar que temos pelo menos um identificador
  if (!playerId && !userId && !userName && !playerName) {
    throw new Error("É necessário fornecer ao menos um identificador (ID ou nome)");
  }

  // Preparar os dados para inserção
  const insertData: any = {
    club_id: clubId,
    callup_id: callupId,
    role: role || "Atleta",
    player_id: playerId || null,
    name: userName || playerName || null
  };

  // Só adiciona user_id se for um UUID válido
  if (userId) {
    const userIdStr = String(userId);
    // Verifica se o userId é um UUID
    const isUuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(userIdStr);
    if (isUuid) {
      insertData.user_id = userIdStr;
    } else {
      // Se não for um UUID, armazena o ID no campo name
      insertData.name = userName || playerName || `Colaborador ${userIdStr}`;
    }
  }

  // Função para adicionar o jogador
  const addPlayer = async () => {
    const { data, error } = await supabase
      .from("callup_players")
      .insert(insertData)
      .select()
      .single();

    if (error) {
      console.error("Erro ao adicionar à convocação:", error);
      throw new Error(`Erro ao adicionar à convocação: ${error.message}`);
    }

    // Se for um colaborador, garante que o nome seja mantido
    if (userName && !userId) {
      return {
        ...data,
        user_name: userName,
        name: userName
      };
    }

    return {
      ...data,
      player_name: playerName || data.name || null,
      user_name: userName || data.name || null,
      name: userName || playerName || data.name || null
    };
  };

  // Se não tem currentUserId, adiciona sem verificar permissões (modo compatibilidade)
  if (!currentUserId) {
    return addPlayer();
  }

  // Se tem currentUserId, verifica permissões e registra no log
  try {
    const result = await withAuditLog(
      clubId,
      currentUserId,
      "ADD_PLAYER_TO_CALLUP",
      {
        callupId,
        playerId: playerId || null,
        userId: userId ? String(userId) : null,
        role: role || null,
        name: userName || playerName || null
      },
      async () => {
        return withPermission(
          clubId,
          currentUserId,
          'manage_callups',
          addPlayer
        );
      }
    );

    return result;
  } catch (error) {
    console.error("Erro ao adicionar jogador à convocação:", error);
    throw error;
  }
}

export async function removePlayerFromCallup(
  clubId: number,
  callupId: number,
  callupPlayerId: number,
  userId?: string
): Promise<void> {
  // Se userId não for fornecido, não verificamos permissões (para compatibilidade)
  if (!userId) {
    const { error } = await supabase
      .from("callup_players")
      .delete()
      .eq("club_id", clubId)
      .eq("callup_id", callupId)
      .eq("id", callupPlayerId);

    if (error) {
      console.error(`Erro ao remover jogador da convocação:`, error);
      throw new Error(`Erro ao remover jogador da convocação: ${error.message}`);
    }

    return;
  }

  // Com userId, verificamos permissões e registramos no log de auditoria
  return withPermission(
    clubId,
    userId,
    CALLUP_PERMISSIONS.EDIT,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "callup.remove_player",
        { callup_id: callupId, callup_player_id: callupPlayerId },
        async () => {
          const { error } = await supabase
            .from("callup_players")
            .delete()
            .eq("club_id", clubId)
            .eq("callup_id", callupId)
            .eq("id", callupPlayerId);

          if (error) {
            throw new Error(`Erro ao remover jogador da convocação: ${error.message}`);
          }
        }
      );
    }
  );
}

// Função para gerar PDF da convocação
export async function generateCallupPDF(
  clubId: number,
  callupId: number,
  openForPrint: boolean = false
): Promise<void> {
  try {
    // Buscar dados da convocação
    const callup = await getCallupById(clubId, callupId);

    // Buscar jogadores da convocação
    const players = await getCallupPlayers(clubId, callupId);

    // Buscar informações do clube
    const { data: clubInfo, error: clubError } = await supabase
      .from("club_info")
      .select("*")
      .eq("id", clubId)
      .single();

    if (clubError) {
      throw new Error(`Erro ao buscar informações do clube: ${clubError.message}`);
    }

    // Nome do arquivo
    const filename = `convocacao-${callup.tournament_type.toLowerCase().replace(/\s+/g, '-')}.pdf`;

    // Criar um elemento temporário para renderizar o relatório
    const reportElement = document.createElement('div');
    reportElement.style.width = '210mm'; // Largura A4
    reportElement.style.padding = '15mm';
    reportElement.style.backgroundColor = 'white';
    reportElement.style.fontFamily = 'Arial, sans-serif';
    reportElement.style.fontSize = '12px';
    reportElement.style.lineHeight = '1.3';

    // Filtrar jogadores por papel
    const athletes = players.filter(p => ["Atleta", "Titular", "Reserva"].includes(p.role));
    const technicalStaff = players.filter(p =>
      ["Técnico", "Auxiliar Técnico", "Preparador Físico", "Analista de Desempenho", "Fisioterapeuta", "Médico", "Preparador de goleiro", "Massagista"].includes(p.role)
    );
    const supportStaff = players.filter(p =>
      [
        "Roupeiro",
        "Comunicação",
        "Segurança",
        "Supervisor",
        "Nutricionista",
        "Fotógrafo",
        "Motorista",
        "Analista de Desempenho",
        "Assessor de Imprensa"
      ].includes(p.role)
    );
    const executives = players.filter(p =>
      ["Diretor", "Presidente"].includes(p.role)
    );

    // Renderizar o HTML do relatório
    reportElement.innerHTML = `
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2px; border-bottom: 2px solid #ddd; padding-bottom: 20px;">
        <div style="display: flex; align-items: center;">
          <div>
            <h1 style="font-size: 28px; margin: 0; color: #333; font-weight: bold;">${clubInfo.name || "Clube"}</h1>
            <h2 style="font-size: 20px; margin: 5px 0 0 0; color: #555;">Convocação Oficial</h2>
          </div>
        </div>
        ${(callup.home_club_logo || clubInfo.logo) && callup.away_club_logo ?
          `<div style="display: flex; align-items: center; gap: 20px;">
            ${callup.home_club_logo || clubInfo.logo ?
              `<img src="${callup.home_club_logo || clubInfo.logo}" alt="Nosso Clube" style="height: 70px; width: auto;">` :
              ``
            }
            <span style="font-size: 28px; font-weight: bold; color: #333;">X</span>
            <img src="${callup.away_club_logo}" alt="Clube Adversário" style="height: 70px; width: auto;">
          </div>` :
          callup.away_club_logo ?
            `<img src="${callup.away_club_logo}" alt="Clube Visitante" style="height: 80px; width: auto;">` :
            (callup.home_club_logo || clubInfo.logo) ?
              `<img src="${callup.home_club_logo || clubInfo.logo}" alt="Nosso Clube" style="height: 80px; width: auto;">` :
              ``
        }
      </div>

      <div style="margin-bottom: 20px;">
        <h3 style="font-size: 18px; margin-bottom: 10px; color: #333;">Informações do Jogo</h3>
        <div style="display: flex; flex-wrap: wrap; gap: 15px;">
          <div style="flex: 1; min-width: 200px; border: 1px solid #ddd; border-radius: 5px; padding: 10px;">
            <p style="font-weight: bold; margin: 0 0 5px 0;">${callup.tournament_type || "Competição"}</p>
            <p style="margin: 0;">${new Date(callup.match_date).toLocaleDateString('pt-BR', { day: '2-digit', month: 'long', year: 'numeric' })}</p>
          </div>
          <div style="flex: 1; min-width: 200px; border: 1px solid #ddd; border-radius: 5px; padding: 10px;">
            <p style="font-weight: bold; margin: 0 0 5px 0;">Local</p>
            <p style="margin: 0;">${callup.match_location || "Local do jogo"}</p>
          </div>
        </div>
      </div>

      ${(callup.competition_image || callup.stadium_image || callup.hotel_image || callup.bus_image) ?
        `<div style="margin-bottom: 20px;">
          <h3 style="font-size: 18px; margin-bottom: 10px; color: #333;">Informações da Partida</h3>
          <div style="display: flex; flex-wrap: wrap; gap: 15px; justify-content: space-between;">
            ${callup.competition_image ?
              `<div style="flex: 1; min-width: 120px; max-width: 200px; text-align: center;">
                <p style="font-weight: bold; margin: 0 0 10px 0; font-size: 14px;">Competição</p>
                <div style="display: flex; justify-content: center;">
                  <img src="${callup.competition_image}" alt="Competição" style="max-height: 40px; max-width: 100%; object-fit: contain;">
                </div>
              </div>` :
              ``
            }
            ${callup.stadium_image ?
              `<div style="flex: 1; min-width: 120px; max-width: 200px; text-align: center;">
                <p style="font-weight: bold; margin: 0 0 10px 0; font-size: 14px;">Estádio</p>
                <div style="display: flex; justify-content: center;">
                  <img src="${callup.stadium_image}" alt="Estádio" style="max-height: 40px; max-width: 100%; object-fit: contain;">
                </div>
              </div>` :
              ``
            }
            ${callup.hotel_image ?
              `<div style="flex: 1; min-width: 120px; max-width: 200px; text-align: center;">
                <p style="font-weight: bold; margin: 0 0 10px 0; font-size: 14px;">Hotel</p>
                <div style="display: flex; justify-content: center;">
                  <img src="${callup.hotel_image}" alt="Hotel" style="max-height: 40px; max-width: 100%; object-fit: contain;">
                </div>
              </div>` :
              ``
            }
            ${callup.bus_image ?
              `<div style="flex: 1; min-width: 120px; max-width: 200px; text-align: center;">
                <p style="font-weight: bold; margin: 0 0 10px 0; font-size: 14px;">Ônibus</p>
                <div style="display: flex; justify-content: center;">
                  <img src="${callup.bus_image}" alt="Ônibus" style="max-height: 40px; max-width: 100%; object-fit: contain;">
                </div>
              </div>` :
              ``
            }
          </div>
        </div>` :
        ``
      }

      ${callup.uniform_image ?
        `<div style="margin-bottom: 20px;">
          <h3 style="font-size: 18px; margin-bottom: 10px; color: #333;">Uniforme</h3>
          <div style="display: flex; justify-content: center;">
            <div style="border: 1px solid #ddd; border-radius: 8px; padding: 15px; text-align: center; max-width: 200px;">
              <div style="display: flex; justify-content: center;">
                <img src="${callup.uniform_image}" alt="Uniforme" style="max-height: 80px; max-width: 100%; object-fit: contain;">
              </div>
            </div>
          </div>
        </div>` :
        ``
      }

      <div style="display: flex; flex-wrap: wrap; gap: 20px; margin-bottom: 10px;">
        ${athletes.length > 0 ?
          `<div style="flex: 1; min-width: 250px; border: 1px solid #ddd; border-radius: 5px; overflow: hidden;">
            <div style="background-color: #2563eb; padding: 2px 10px 4px; border-bottom: 1px solid #ddd; position: relative;">
              <h3 style="margin: 0; padding: 4px; font-size: 16px; color: white; line-height: 1.1; position: relative; top: -2px;">Jogadores Convocados</h3>
            </div>
            <div style="padding: 2px;">
              <ul style="list-style-type: none; padding: 0; margin: 0;">
                ${[...athletes]
                  .sort((a, b) => {
                    const nameA = (a.player_name || a.user_name || a.name || '').toLowerCase();
                    const nameB = (b.player_name || b.user_name || b.name || '').toLowerCase();
                    return nameA.localeCompare(nameB, 'pt-BR');
                  })
                  .map(player => {
                  const fullPlayerName = player.player_name || player.user_name || player.name || 'Nome não informado';
                  const truncatedPlayerName = truncatePlayerName(fullPlayerName, 25);
                  const numberPrefix = player.player_number ? `${player.player_number} - ` : '';
                  const cpfContent = player.player_cpf ? formatCPF(player.player_cpf) : '';
                  return `<li style="padding: 1px 0; border-bottom: 1px solid #eee; font-size: 12px; line-height: 1.2; display: flex; justify-content: space-between;">`+
                    `<span>${numberPrefix}${truncatedPlayerName}</span>`+
                    `${cpfContent ? `<span>CPF - ${cpfContent}</span>` : ''}`+
                  `</li>`;
                }).join('')}
              </ul>
            </div>
          </div>` :
          ``
        }

        ${callup.match_schedule ?
          `<div style="flex: 1; min-width: 250px; border: 1px solid #ddd; border-radius: 5px; overflow: hidden;">
            <div style="background-color: #16a34a; padding: 2px 10px 4px; border-bottom: 1px solid #ddd; position: relative;">
              <h3 style="margin: 0; padding: 4px; font-size: 16px; color: white; line-height: 1.1; position: relative; top: -2px;">Programação do Jogo</h3>
            </div>
            <div style="padding: 2px;">
              <div style="white-space: pre-line;">${callup.match_schedule}</div>
            </div>
          </div>` :
          ``
        }

        ${callup.hotel_control ?
          `<div style="flex: 1; min-width: 250px; border: 1px solid #ddd; border-radius: 5px; overflow: hidden;">
            <div style="background-color: #dc2626; padding: 2px 10px 4px; border-bottom: 1px solid #ddd; position: relative;">
              <h3 style="margin: 0; padding: 4px; font-size: 16px; color: white; line-height: 1.1; position: relative; top: -2px;">Controle de Hospedagem</h3>
            </div>
            <div style="padding: 10px;">
              <div style="white-space: pre-line;">${callup.hotel_control}</div>
            </div>
          </div>` :
          ``
        }

        ${technicalStaff.length > 0 ?
          `<div style="flex: 1; min-width: 250px; border: 1px solid #ddd; border-radius: 5px; overflow: hidden;">
            <div style="background-color: #7c3aed; padding: 2px 10px 4px; border-bottom: 1px solid #ddd; position: relative;">
              <h3 style="margin: 0; padding: 4px; font-size: 16px; color: white; line-height: 1.1; position: relative; top: -2px;">Comissão Técnica</h3>
            </div>
            <div style="padding: 10px;">
              <ul style="list-style-type: none; padding: 0; margin: 0;">
                ${technicalStaff.map(staff => {
                  const fullStaffName = staff.player_name || staff.user_name || staff.name || 'Nome não informado';
                  const truncatedStaffName = truncatePlayerName(fullStaffName, 25);
                  return `<li style="padding: 5px 0; border-bottom: 1px solid #eee; font-size: 12px; line-height: 1.2;">
                    <span style="font-weight: bold;">${staff.role}:</span> ${truncatedStaffName}
                  </li>`;
                }).join('')}
              </ul>
            </div>
          </div>` :
          ``
        }

        ${supportStaff.length > 0 ?
          `<div style="flex: 1; min-width: 250px; border: 1px solid #ddd; border-radius: 5px; overflow: hidden;">
            <div style="background-color: #ea580c; padding: 2px 10px 4px; border-bottom: 1px solid #ddd; position: relative;">
              <h3 style="margin: 0; padding: 4px; font-size: 16px; color: white; line-height: 1.1; position: relative; top: -2px;">Staff</h3>
            </div>
            <div style="padding: 2px;">
              <ul style="list-style-type: none; padding: 0; margin: 0;">
                ${supportStaff.map(staff => {
                  const fullStaffName = staff.player_name || staff.user_name || staff.name || 'Nome não informado';
                  const truncatedStaffName = truncatePlayerName(fullStaffName, 25);
                  return `<li style="padding: 5px 0; border-bottom: 1px solid #eee; font-size: 12px; line-height: 1.2;">
                    <span style="font-weight: bold;">${staff.role}:</span> ${truncatedStaffName}
                  </li>`;
                }).join('')}
              </ul>
            </div>
          </div>` :
          ``
        }
      </div>

      ${(executives.length > 0 || callup.sponsor_image1 || callup.sponsor_image2) ?
        `<div style="display: flex; flex-wrap: wrap; gap: 20px; margin-top: 20px; align-items: flex-start;">
          ${executives.length > 0 ?
            `<div style="flex: 1; min-width: 250px; border: 1px solid #ddd; border-radius: 5px; overflow: hidden;">
              <div style="background-color: #0891b2; padding: 2px 10px 4px; border-bottom: 1px solid #ddd; position: relative;">
                <h3 style="margin: 0; padding: 4px; font-size: 16px; color: white; line-height: 1.1; position: relative; top: -2px;">Diretoria Executiva</h3>
              </div>
              <div style="padding: 2px;">
                <ul style="list-style-type: none; padding: 0; margin: 0;">
                  ${executives.map(exec => {
                    const fullExecName = exec.player_name || exec.user_name || exec.name || 'Nome não informado';
                    const truncatedExecName = truncatePlayerName(fullExecName, 55);
                    return `<li style="padding: 5px 0; border-bottom: 1px solid #eee; font-size: 12px; line-height: 1.2;">
                      <span style="font-weight: bold;">${exec.role}:</span> ${truncatedExecName}
                    </li>`;
                  }).join('')}
                </ul>
              </div>
            </div>` :
            ``
          }
          ${(callup.sponsor_image1 || callup.sponsor_image2) ?
            `<div style="flex: 1; min-width: 250px; border: 1px solid #ddd; border-radius: 5px; overflow: hidden;">
              <div style="background-color:rgb(118, 117, 163); padding: 2px 10px 4px; border-bottom: 1px solid #ddd; position: relative;">
                <h3 style="margin: 0; padding: 4px; font-size: 16px; color: white; line-height: 1.1; position: relative; top: -2px;">Patrocinadores</h3>
              </div>
              <div style="padding: 10px; display: flex; flex-wrap: wrap; justify-content: center; align-items: center; gap: 10px;">
                ${callup.sponsor_image1 ?
                  `<img src="${callup.sponsor_image1}" alt="Patrocinador 1" style="max-height: 50px; max-width: 100%; object-fit: contain;">` :
                  ``
                }
                ${callup.sponsor_image2 ?
                  `<img src="${callup.sponsor_image2}" alt="Patrocinador 2" style="max-height: 50px; max-width: 100%; object-fit: contain;">` :
                  ``
                }
              </div>
            </div>` :
            ``
          }
        </div>` :
        ``
      }
    `;

    // Adicionar o elemento ao corpo do documento temporariamente
    document.body.appendChild(reportElement);

    try {
      // Capturar o elemento como uma imagem
      const canvas = await html2canvas(reportElement, {
        scale: 3, // Melhor qualidade para impressão
        useCORS: true, // Permitir imagens de outros domínios
        logging: false,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: reportElement.scrollWidth,
        height: reportElement.scrollHeight,
        scrollX: 0,
        scrollY: 0,
      });

      // Criar o PDF
      const pdf = new jsPDF({
        format: 'a4',
        orientation: 'portrait',
        unit: 'mm',
        compress: true,
      });

      // Adicionar a imagem ao PDF
      const imgData = canvas.toDataURL('image/jpeg', 0.95); // JPEG com alta qualidade
      const imgWidth = 210; // A4 width in mm
      const pageHeight = 297; // A4 height in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      let heightLeft = imgHeight;
      let position = 0;

      // Adicionar a primeira página
      pdf.addImage(imgData, 'JPEG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      // Adicionar páginas adicionais se necessário
      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(imgData, 'JPEG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      if (openForPrint) {
        // Abrir o PDF em uma nova janela para impressão
        const pdfBlob = pdf.output('blob');
        const pdfUrl = URL.createObjectURL(pdfBlob);
        const printWindow = window.open(pdfUrl, '_blank');

        if (printWindow) {
          printWindow.addEventListener('load', () => {
            printWindow.print();
          });
        } else {
          console.warn("Não foi possível abrir uma nova janela para impressão");
          // Fallback para download se não conseguir abrir a janela
          pdf.save(filename);
        }
      } else {
        // Salvar o PDF como download
        pdf.save(filename);
      }
    } finally {
      // Remover o elemento temporário
      document.body.removeChild(reportElement);
    }
  } catch (error) {
    console.error("Erro ao gerar PDF:", error);
    throw error;
  }
}

// Função para gerar PDF de alojamento da partida
export async function generateCallupAccommodationPDF(
  clubId: number,
  callupId: number
): Promise<void> {
  // Buscar dados necessários
  const callup = await getCallupById(clubId, callupId);
  const players = await getCallupPlayers(clubId, callupId);
  const { data: clubInfo, error } = await supabase
    .from("club_info")
    .select("*")
    .eq("id", clubId)
    .single();

  if (error) {
    throw new Error(`Erro ao buscar informações do clube: ${error.message}`);
  }

  await generateMatchAccommodationPDF(callup, players, clubInfo as ClubInfo);
}