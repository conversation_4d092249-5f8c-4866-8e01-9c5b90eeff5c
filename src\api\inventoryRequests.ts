import { supabase } from "@/integrations/supabase/client";
import { withPermission, withAuditLog } from "./middleware";
import { INVENTORY_PERMISSIONS } from "@/constants/permissions";

// Função para gerar assinatura digital automática
async function generateAutomaticSignature(
  clubId: number,
  userId: string,
  requestId: number,
  userRole?: string
): Promise<string | null> {
  try {
    // Buscar informações do usuário
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("name, role")
      .eq("id", userId)
      .single();

    if (userError || !userData) {
      console.error("Erro ao buscar dados do usuário:", userError);
      return null;
    }

    // Criar texto da assinatura digital
    const currentDate = new Date().toLocaleDateString('pt-BR');
    const currentTime = new Date().toLocaleTimeString('pt-BR');
    const userName = userData.name || 'Usuário';
    const role = userRole || userData.role || 'Usuário';

    const signatureText = `Documento assinado digitalmente por: ${userName}\nCargo/Função: ${role}\nData: ${currentDate}\nHorário: ${currentTime}\nConforme MP 2.200-2/2001`;

    // Verificar se estamos no browser (canvas disponível)
    if (typeof document === 'undefined') {
      console.log("Canvas não disponível no servidor, pulando geração de assinatura automática");
      return null;
    }

    // Criar um canvas para gerar a imagem da assinatura
    const canvas = document.createElement('canvas');
    canvas.width = 450;
    canvas.height = 150;
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      return null;
    }

    // Configurar o canvas
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Configurar texto
    ctx.fillStyle = '#000000';
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';

    // Desenhar o texto da assinatura
    const lines = signatureText.split('\n');
    const lineHeight = 20;
    const startY = (canvas.height - (lines.length * lineHeight)) / 2 + lineHeight;

    lines.forEach((line, index) => {
      ctx.fillText(line, canvas.width / 2, startY + (index * lineHeight));
    });

    // Converter para blob
    const signatureDataUrl = canvas.toDataURL('image/png');

    // Upload da assinatura para o Supabase Storage
    const fileName = `signatures/${clubId}/${requestId}_auto_${Date.now()}.png`;

    // Converter data URL para blob
    const response = await fetch(signatureDataUrl);
    const blob = await response.blob();

    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('profileimages')
      .upload(fileName, blob, {
        contentType: 'image/png',
        upsert: true
      });

    if (uploadError) {
      console.error("Erro ao fazer upload da assinatura automática:", uploadError);
      return null;
    }

    // Obter URL pública
    const { data: urlData } = supabase.storage
      .from('profileimages')
      .getPublicUrl(fileName);

    return urlData.publicUrl;
  } catch (error) {
    console.error("Erro ao gerar assinatura automática:", error);
    return null;
  }
}

// Função para gerar assinatura digital automática de entrega
async function generateAutomaticDeliverySignature(
  clubId: number,
  userId: string,
  requestId: number,
  userRole?: string
): Promise<string | null> {
  try {
    // Buscar informações do usuário
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("name, role")
      .eq("id", userId)
      .single();

    if (userError || !userData) {
      console.error("Erro ao buscar dados do usuário:", userError);
      return null;
    }

    // Criar texto da assinatura digital de entrega
    const currentDate = new Date().toLocaleDateString('pt-BR');
    const currentTime = new Date().toLocaleTimeString('pt-BR');
    const userName = userData.name || 'Usuário';
    const role = userRole || userData.role || 'Usuário';

    const signatureText = `Entrega confirmada digitalmente por: ${userName}\nCargo/Função: ${role}\nData: ${currentDate}\nHorário: ${currentTime}\nConforme MP 2.200-2/2001`;

    // Verificar se estamos no browser (canvas disponível)
    if (typeof document === 'undefined') {
      console.log("Canvas não disponível no servidor, pulando geração de assinatura automática de entrega");
      return null;
    }

    // Criar um canvas para gerar a imagem da assinatura
    const canvas = document.createElement('canvas');
    canvas.width = 450;
    canvas.height = 150;
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      return null;
    }

    // Configurar o canvas
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Configurar texto
    ctx.fillStyle = '#000000';
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';

    // Desenhar o texto da assinatura
    const lines = signatureText.split('\n');
    const lineHeight = 20;
    const startY = (canvas.height - (lines.length * lineHeight)) / 2 + lineHeight;

    lines.forEach((line, index) => {
      ctx.fillText(line, canvas.width / 2, startY + (index * lineHeight));
    });

    // Converter para blob
    const signatureDataUrl = canvas.toDataURL('image/png');

    // Upload da assinatura para o Supabase Storage
    const fileName = `signatures/${clubId}/${requestId}_delivery_auto_${Date.now()}.png`;

    // Converter data URL para blob
    const response = await fetch(signatureDataUrl);
    const blob = await response.blob();

    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('profileimages')
      .upload(fileName, blob, {
        contentType: 'image/png',
        upsert: true
      });

    if (uploadError) {
      console.error("Erro ao fazer upload da assinatura automática de entrega:", uploadError);
      return null;
    }

    // Obter URL pública
    const { data: urlData } = supabase.storage
      .from('profileimages')
      .getPublicUrl(fileName);

    return urlData.publicUrl;
  } catch (error) {
    console.error("Erro ao gerar assinatura automática de entrega:", error);
    return null;
  }
}

import { InventoryProduct } from "./inventory";

// Types
export type InventoryRequest = {
  id: number;
  club_id: number;
  department_id: number | null;
  requested_by: string;
  requester_type: string;
  requester_id: string | null;
  category: string;
  withdrawal_date: string;
  status: 'pending' | 'approved' | 'rejected' | 'completed';
  requester_notes: string | null;
  delivery_notes: string | null;
  delivery_method: 'pickup' | 'delivery';
  delivery_location: string | null;
  requester_signature_url: string | null;
  delivery_signature_url: string | null;
  created_at: string;
  updated_at: string;
  // Additional fields for joins
  department_name?: string;
  requester_name?: string;
};

export type InventoryRequestItem = {
  id: number;
  club_id: number;
  request_id: number;
  product_id: number;
  quantity: number;
  returned_quantity: number;
  created_at: string;
  // Additional fields for joins
  product_name?: string;
  product_department?: string;
  product_location?: string;
  available_quantity?: number;
};

// Constants
export const REQUESTER_TYPES = [
  'player',
  'staff',
  'collaborator',
  'medical',
  'administrative',
  'cozinha',
  'manutencao',
  'dep_medico'
];

export const OPERATIONAL_DEPARTMENTS = [
  'Departamento Futebol de Base',
  'Departamento Profissional',
  'Cozinha',
  'Departamento Médico',
  'Departamento Administrativo',
  'Atletas',
  'Membros da Equipe Técnica',
  'Diretoria',
  'Manutenção'
];

export const PLAYER_REQUEST_CATEGORIES = [
  'Farmácia',
  'Correspondencia',
  'Suplemento',
  'Produto de Limpeza'
];

// Functions for inventory requests
export async function getInventoryRequests(
  clubId: number,
  status?: string,
  userId?: string,
  startDate?: Date,
  endDate?: Date,
  requesterId?: string,
  requesterType?: string
): Promise<InventoryRequest[]> {
  // If userId is not provided, don't check permissions (for compatibility)
  if (!userId) {
    return getRequestsWithoutPermissionCheck(
      clubId,
      status,
      startDate,
      endDate,
      requesterId,
      requesterType
    );
  }

  // With userId, check permissions
  return withPermission(
    clubId,
    userId,
    INVENTORY_PERMISSIONS.REQUESTS.VIEW,
    async () => {
      return getRequestsWithoutPermissionCheck(
        clubId,
        status,
        startDate,
        endDate,
        requesterId,
        requesterType
      );
    }
  );
}

// Helper function to get requests without permission check
async function getRequestsWithoutPermissionCheck(
  clubId: number,
  status?: string,
  startDate?: Date,
  endDate?: Date,
  requesterId?: string,
  requesterType?: string
): Promise<InventoryRequest[]> {
  // Simplificar a consulta para evitar problemas com joins complexos
  let query = supabase
    .from("inventory_requests")
    .select(`
      *,
      departments:department_id (
        id,
        name
      )
    `)
    .eq("club_id", clubId)
    .order("created_at", { ascending: false });

  if (status) {
    query = query.eq("status", status);
  }
  if (startDate) {
    query = query.gte("created_at", startDate.toISOString());
  }
  if (endDate) {
    query = query.lte("created_at", endDate.toISOString());
  }
  if (requesterId) {
    query = query.eq("requester_id", requesterId);
  }
  if (requesterType) {
    query = query.eq("requester_type", requesterType);
  }

  const { data, error } = await query;

  if (error) {
    console.error("Error fetching inventory requests:", error);
    throw new Error(`Error fetching inventory requests: ${error.message}`);
  }

  // Processar os dados para adicionar informações do solicitante
  const processedData = await Promise.all((data || []).map(async (item) => {
    let requesterName = "Unknown";

    try {
      if (item.requester_type === 'player' && item.requester_id) {
        // Buscar informações do jogador
        const { data: playerData, error: playerError } = await supabase
          .from("players")
          .select("name")
          .eq("id", item.requester_id)
          .maybeSingle();

        if (!playerError && playerData) {
          requesterName = playerData.name;
        }
      } else if (item.requester_type === 'collaborator' && item.requester_id) {
        // Buscar informações do colaborador
        const { data: collaboratorData, error: collaboratorError } = await supabase
          .from("collaborators")
          .select("full_name")
          .eq("id", item.requester_id)
          .maybeSingle();

        if (!collaboratorError && collaboratorData) {
          requesterName = collaboratorData.full_name;
        }
      } else {
        try {
          // Buscar o nome do usuário na tabela public.users
          const { data: userData, error: userError } = await supabase
            .from("users")
            .select("name")
            .eq("id", item.requested_by)
            .single();

          if (!userError && userData) {
            requesterName = userData.name || "Usuário sem nome";
          } else {
            // Tentar buscar o email do usuário como fallback
            const { data: authUserData } = await supabase
              .rpc('get_user_email', { user_id: item.requested_by });

            if (authUserData) {
              requesterName = authUserData;
            } else {
              requesterName = `Usuário ID: ${item.requested_by ? item.requested_by.substring(0, 8) : 'Unknown'}`;
            }
          }
        } catch (err) {
          console.error("Erro ao buscar nome do usuário:", err);
          requesterName = `Usuário ID: ${item.requested_by ? item.requested_by.substring(0, 8) : 'Unknown'}`;
        }
      }
    } catch (e) {
      console.error("Error formatting requester name:", e);
      requesterName = "Unknown";
    }

    return {
      ...item,
      department_name: item.departments?.name || "Unknown Department",
      requester_name: requesterName
    };
  }));

  return processedData;
}

export async function getMyInventoryRequests(
  clubId: number,
  userId: string
): Promise<InventoryRequest[]> {
  return withPermission(
    clubId,
    userId,
    INVENTORY_PERMISSIONS.REQUESTS.CREATE,
    async () => {
      const { data, error } = await supabase
        .from("inventory_requests")
        .select(`
          *,
          departments:department_id (
            id,
            name
          )
        `)
        .eq("club_id", clubId)
        .eq("requested_by", userId)
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching my inventory requests:", error);
        throw new Error(`Error fetching inventory requests: ${error.message}`);
      }

      return (data || []).map((item) => ({
        ...item,
        department_name: item.departments?.name || "Unknown Department",
        requester_name: item.requester_type === 'player' ? 'Atleta' : 'Usuário'
      }));
    }
  );
}

export async function getInventoryRequestById(
  clubId: number,
  requestId: number,
  userId?: string
): Promise<InventoryRequest> {
  // If userId is not provided, don't check permissions (for compatibility)
  if (!userId) {
    return getRequestByIdWithoutPermissionCheck(clubId, requestId);
  }

  // With userId, check permissions
  return withPermission(
    clubId,
    userId,
    INVENTORY_PERMISSIONS.REQUESTS.VIEW,
    async () => {
      return getRequestByIdWithoutPermissionCheck(clubId, requestId);
    }
  );
}

// Helper function to get request by ID without permission check
async function getRequestByIdWithoutPermissionCheck(
  clubId: number,
  requestId: number
): Promise<InventoryRequest> {
  // Simplificar a consulta para evitar problemas com joins complexos
  const { data, error } = await supabase
    .from("inventory_requests")
    .select(`
      *,
      departments:department_id (
        id,
        name
      )
    `)
    .eq("club_id", clubId)
    .eq("id", requestId)
    .single();

  if (error) {
    console.error("Error fetching inventory request:", error);
    throw new Error(`Error fetching inventory request: ${error.message}`);
  }

  // Format data to include department and requester names
  let requesterName = "Unknown";

  try {
    if (data.requester_type === 'player' && data.requester_id) {
      // Buscar informações do jogador
      const { data: playerData, error: playerError } = await supabase
        .from("players")
        .select("name")
        .eq("id", data.requester_id)
        .maybeSingle();

      if (!playerError && playerData) {
        requesterName = playerData.name;
      }
    } else if (data.requester_type === 'collaborator' && data.requester_id) {
      // Buscar informações do colaborador
      const { data: collaboratorData, error: collaboratorError } = await supabase
        .from("collaborators")
        .select("full_name")
        .eq("id", data.requester_id)
        .maybeSingle();

      if (!collaboratorError && collaboratorData) {
        requesterName = collaboratorData.full_name;
      }
    } else {
      try {
        // Buscar o nome do usuário na tabela public.users
        const { data: userData, error: userError } = await supabase
          .from("users")
          .select("name")
          .eq("id", data.requested_by)
          .single();

        if (!userError && userData) {
          requesterName = userData.name || "Usuário sem nome";
        } else {
          // Tentar buscar o email do usuário como fallback
          const { data: authUserData } = await supabase
            .from("users")
            .select("email")
            .eq("id", data.requested_by)
            .single();

          if (authUserData && authUserData.email) {
            requesterName = authUserData.email;
          } else {
            requesterName = `Usuário ID: ${data.requested_by ? data.requested_by.substring(0, 8) : 'Unknown'}`;
          }
        }
      } catch (err) {
        console.error("Erro ao buscar nome do usuário:", err);
        requesterName = `Usuário ID: ${data.requested_by ? data.requested_by.substring(0, 8) : 'Unknown'}`;
      }
    }
  } catch (e) {
    console.error("Error formatting requester name:", e);
    requesterName = "Unknown";
  }

  return {
    ...data,
    department_name: data.departments?.name || "Unknown Department",
    requester_name: requesterName
  };
}

export async function getInventoryRequestItems(
  clubId: number,
  requestId: number,
  userId?: string
): Promise<InventoryRequestItem[]> {
  // If userId is not provided, don't check permissions (for compatibility)
  if (!userId) {
    return getRequestItemsWithoutPermissionCheck(clubId, requestId);
  }

  // With userId, check permissions
  return withPermission(
    clubId,
    userId,
    INVENTORY_PERMISSIONS.REQUESTS.VIEW,
    async () => {
      return getRequestItemsWithoutPermissionCheck(clubId, requestId);
    }
  );
}

// Helper function to get request items without permission check
async function getRequestItemsWithoutPermissionCheck(
  clubId: number,
  requestId: number
): Promise<InventoryRequestItem[]> {
  const { data, error } = await supabase
    .from("inventory_request_items")
    .select(`
      *,
      inventory_products:product_id (
        id,
        name,
        department,
        location,
        quantity
      )
    `)
    .eq("club_id", clubId)
    .eq("request_id", requestId);

  if (error) {
    console.error("Error fetching inventory request items:", error);
    throw new Error(`Error fetching inventory request items: ${error.message}`);
  }

  // Format data to include product details
  return (data || []).map(item => ({
    ...item,
    product_name: item.inventory_products?.name || "Unknown Product",
    product_department: item.inventory_products?.department || "Unknown Department",
    product_location: item.inventory_products?.location || null,
    available_quantity: item.inventory_products?.quantity || 0
  }));
}

export async function createInventoryRequest(
  clubId: number,
  requestData: Omit<InventoryRequest, 'id' | 'club_id' | 'created_at' | 'updated_at'>,
  userId?: string
): Promise<InventoryRequest> {
  // If userId is not provided, don't check permissions (for compatibility)
  if (!userId) {
    return createRequestWithoutPermissionCheck(clubId, requestData);
  }

  // With userId, check permissions and log audit
  return withPermission(
    clubId,
    userId,
    INVENTORY_PERMISSIONS.REQUESTS.CREATE,
    async () => {
      let finalData = { ...requestData };

      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('role')
        .eq('id', userId)
        .single();

      if (userError || !userData) {
        throw new Error(`Erro ao verificar papel do usuário: ${userError?.message}`);
      }

      if (userData.role === 'player') {
        if (!PLAYER_REQUEST_CATEGORIES.includes(finalData.category)) {
          throw new Error('Jogadores só podem solicitar Farmácia, Correspondencia, Suplemento ou Produto de Limpeza');
        }

        finalData.requester_type = 'Atleta';
        finalData.requested_by = userId;

        if (!finalData.requester_id) {
          const { data: playerData } = await supabase
            .from('players')
            .select('id')
            .eq('user_id', userId)
            .eq('club_id', clubId)
            .maybeSingle();
          finalData.requester_id = playerData?.id || null;
        }
      }

      return withAuditLog(
        clubId,
        userId,
        "inventory.requests.create",
        { requester_type: finalData.requester_type },
        async () => {
          return createRequestWithoutPermissionCheck(clubId, finalData);
        }
      );
    }
  );
}

// Helper function to create request without permission check
async function createRequestWithoutPermissionCheck(
  clubId: number,
  requestData: Omit<InventoryRequest, 'id' | 'club_id' | 'created_at' | 'updated_at'>
): Promise<InventoryRequest> {
  // Primeiro, criar a solicitação sem assinatura
  const { data, error } = await supabase
    .from("inventory_requests")
    .insert({
      club_id: clubId,
      department_id: requestData.department_id,
      requested_by: requestData.requested_by,
      requester_type: requestData.requester_type,
      requester_id: requestData.requester_id,
      category: requestData.category,
      withdrawal_date: requestData.withdrawal_date,
      status: requestData.status || 'pending',
      requester_notes: requestData.requester_notes,
      delivery_notes: requestData.delivery_notes,
      delivery_method: requestData.delivery_method,
      delivery_location: requestData.delivery_location,
      requester_signature_url: null, // Será preenchido automaticamente
      delivery_signature_url: requestData.delivery_signature_url
    })
    .select()
    .single();

  if (error) {
    console.error("Error creating inventory request:", error);
    throw new Error(`Error creating inventory request: ${error.message}`);
  }

  // Gerar assinatura digital automática
  try {
    const automaticSignatureUrl = await generateAutomaticSignature(
      clubId,
      requestData.requested_by,
      data.id
    );

    if (automaticSignatureUrl) {
      // Atualizar a solicitação com a assinatura automática
      const { data: updatedData, error: updateError } = await supabase
        .from("inventory_requests")
        .update({
          requester_signature_url: automaticSignatureUrl
        })
        .eq("id", data.id)
        .select()
        .single();

      if (updateError) {
        console.error("Error updating request with automatic signature:", updateError);
        // Não falhar a criação da solicitação se a assinatura falhar
        return data;
      }

      return updatedData;
    }
  } catch (signatureError) {
    console.error("Error generating automatic signature:", signatureError);
    // Não falhar a criação da solicitação se a assinatura falhar
  }

  return data;
}

export async function updateInventoryRequest(
  clubId: number,
  requestId: number,
  requestData: Partial<Omit<InventoryRequest, 'id' | 'club_id' | 'created_at' | 'updated_at'>>,
  userId?: string
): Promise<InventoryRequest> {
  // If userId is not provided, don't check permissions (for compatibility)
  if (!userId) {
    return updateRequestWithoutPermissionCheck(clubId, requestId, requestData);
  }

  // With userId, check permissions and log audit
  return withPermission(
    clubId,
    userId,
    INVENTORY_PERMISSIONS.REQUESTS.EDIT,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "inventory.requests.update",
        { request_id: requestId, ...requestData },
        async () => {
          return updateRequestWithoutPermissionCheck(clubId, requestId, requestData);
        }
      );
    }
  );
}

// Helper function to update request without permission check
async function updateRequestWithoutPermissionCheck(
  clubId: number,
  requestId: number,
  requestData: Partial<Omit<InventoryRequest, 'id' | 'club_id' | 'created_at' | 'updated_at'>>
): Promise<InventoryRequest> {
  const { data, error } = await supabase
    .from("inventory_requests")
    .update({
      ...requestData,
      updated_at: new Date().toISOString()
    })
    .eq("club_id", clubId)
    .eq("id", requestId)
    .select()
    .single();

  if (error) {
    console.error("Error updating inventory request:", error);
    throw new Error(`Error updating inventory request: ${error.message}`);
  }

  return data;
}

export async function deleteInventoryRequest(
  clubId: number,
  requestId: number,
  userId?: string
): Promise<boolean> {
  // If userId is not provided, don't check permissions (for compatibility)
  if (!userId) {
    return deleteRequestWithoutPermissionCheck(clubId, requestId);
  }

  // With userId, check permissions and log audit
  return withPermission(
    clubId,
    userId,
    INVENTORY_PERMISSIONS.REQUESTS.DELETE,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "inventory.requests.delete",
        { request_id: requestId },
        async () => {
          return deleteRequestWithoutPermissionCheck(clubId, requestId);
        }
      );
    }
  );
}

// Função para cancelar uma solicitação de estoque
export async function cancelInventoryRequest(
  clubId: number,
  requestId: number,
  userId?: string
): Promise<boolean> {
  // If userId is not provided, don't check permissions (for compatibility)
  if (!userId) {
    return cancelRequestWithoutPermissionCheck(clubId, requestId);
  }

  // With userId, check permissions and log audit
  return withPermission(
    clubId,
    userId,
    INVENTORY_PERMISSIONS.REQUESTS.EDIT,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "inventory.requests.cancel",
        { request_id: requestId },
        async () => {
          return cancelRequestWithoutPermissionCheck(clubId, requestId);
        }
      );
    }
  );
}

// Helper function to cancel request without permission check
async function cancelRequestWithoutPermissionCheck(
  clubId: number,
  requestId: number
): Promise<boolean> {
  // Atualizar o status da solicitação para 'rejected'
  const { data, error } = await supabase
    .from("inventory_requests")
    .update({
      status: 'rejected',
      updated_at: new Date().toISOString()
    })
    .eq("club_id", clubId)
    .eq("id", requestId)
    .select()
    .single();

  if (error) {
    console.error("Error canceling inventory request:", error);
    throw new Error(`Error canceling inventory request: ${error.message}`);
  }

  return true;
}

// Helper function to delete request without permission check
async function deleteRequestWithoutPermissionCheck(
  clubId: number,
  requestId: number
): Promise<boolean> {
  // First delete all request items
  const { error: itemsError } = await supabase
    .from("inventory_request_items")
    .delete()
    .eq("club_id", clubId)
    .eq("request_id", requestId);

  if (itemsError) {
    console.error("Error deleting inventory request items:", itemsError);
    throw new Error(`Error deleting inventory request items: ${itemsError.message}`);
  }

  // Then delete the request
  const { error } = await supabase
    .from("inventory_requests")
    .delete()
    .eq("club_id", clubId)
    .eq("id", requestId);

  if (error) {
    console.error("Error deleting inventory request:", error);
    throw new Error(`Error deleting inventory request: ${error.message}`);
  }

  return true;
}

export async function addInventoryRequestItem(
  clubId: number,
  requestId: number,
  productId: number,
  quantity: number,
  userId?: string
): Promise<InventoryRequestItem> {
  // If userId is not provided, don't check permissions (for compatibility)
  if (!userId) {
    return addRequestItemWithoutPermissionCheck(clubId, requestId, productId, quantity);
  }

  // With userId, check permissions and log audit
  return withPermission(
    clubId,
    userId,
    INVENTORY_PERMISSIONS.REQUESTS.EDIT,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "inventory.requests.add_item",
        { request_id: requestId, product_id: productId, quantity },
        async () => {
          return addRequestItemWithoutPermissionCheck(clubId, requestId, productId, quantity);
        }
      );
    }
  );
}

// Helper function to add request item without permission check
async function addRequestItemWithoutPermissionCheck(
  clubId: number,
  requestId: number,
  productId: number,
  quantity: number
): Promise<InventoryRequestItem> {
  // Check product availability
  const { data: productData, error: productError } = await supabase
    .from("inventory_products")
    .select("quantity")
    .eq("club_id", clubId)
    .eq("id", productId)
    .single();

  if (productError) {
    console.error("Error checking product quantity:", productError);
    throw new Error(`Error checking product quantity: ${productError.message}`);
  }

  if (!productData || productData.quantity <= 0) {
    throw new Error("Produto sem estoque disponível");
  }
  
  // Check if item already exists
  const { data: existingItem, error: checkError } = await supabase
    .from("inventory_request_items")
    .select("id, quantity")
    .eq("club_id", clubId)
    .eq("request_id", requestId)
    .eq("product_id", productId)
    .maybeSingle();

  if (checkError) {
    console.error("Error checking existing inventory request item:", checkError);
    throw new Error(`Error checking existing inventory request item: ${checkError.message}`);
  }

  if (existingItem) {
    // Update existing item
    const { data, error } = await supabase
      .from("inventory_request_items")
      .update({
        quantity: existingItem.quantity + quantity
      })
      .eq("id", existingItem.id)
      .select(`
        *,
        inventory_products:product_id (
          id,
          name,
          department,
          location,
          quantity
        )
      `)
      .single();

    if (error) {
      console.error("Error updating inventory request item:", error);
      throw new Error(`Error updating inventory request item: ${error.message}`);
    }

    return {
      ...data,
      product_name: data.inventory_products?.name || "Unknown Product",
      product_department: data.inventory_products?.department || "Unknown Department",
      product_location: data.inventory_products?.location || null,
      available_quantity: data.inventory_products?.quantity || 0
    };
  } else {
    // Create new item
    const { data, error } = await supabase
      .from("inventory_request_items")
      .insert({
        club_id: clubId,
        request_id: requestId,
        product_id: productId,
        quantity: quantity,
        returned_quantity: 0
      })
      .select(`
        *,
        inventory_products:product_id (
          id,
          name,
          department,
          location,
          quantity
        )
      `)
      .single();

    if (error) {
      console.error("Error adding inventory request item:", error);
      throw new Error(`Error adding inventory request item: ${error.message}`);
    }

    return {
      ...data,
      product_name: data.inventory_products?.name || "Unknown Product",
      product_department: data.inventory_products?.department || "Unknown Department",
      product_location: data.inventory_products?.location || null,
      available_quantity: data.inventory_products?.quantity || 0
    };
  }
}

export async function removeInventoryRequestItem(
  clubId: number,
  itemId: number,
  userId?: string
): Promise<boolean> {
  // If userId is not provided, don't check permissions (for compatibility)
  if (!userId) {
    return removeRequestItemWithoutPermissionCheck(clubId, itemId);
  }

  // With userId, check permissions and log audit
  return withPermission(
    clubId,
    userId,
    INVENTORY_PERMISSIONS.REQUESTS.EDIT,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "inventory.requests.remove_item",
        { item_id: itemId },
        async () => {
          return removeRequestItemWithoutPermissionCheck(clubId, itemId);
        }
      );
    }
  );
}

// Helper function to remove request item without permission check
async function removeRequestItemWithoutPermissionCheck(
  clubId: number,
  itemId: number
): Promise<boolean> {
  const { error } = await supabase
    .from("inventory_request_items")
    .delete()
    .eq("club_id", clubId)
    .eq("id", itemId);

  if (error) {
    console.error("Error removing inventory request item:", error);
    throw new Error(`Error removing inventory request item: ${error.message}`);
  }

  return true;
}

export async function processInventoryRequest(
  clubId: number,
  requestId: number,
  userId?: string
): Promise<boolean> {
  // If userId is not provided, don't check permissions (for compatibility)
  if (!userId) {
    return processRequestWithoutPermissionCheck(clubId, requestId, userId);
  }

  // With userId, check permissions and log audit
  return withPermission(
    clubId,
    userId,
    INVENTORY_PERMISSIONS.REQUESTS.PROCESS,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "inventory.requests.process",
        { request_id: requestId },
        async () => {
          return processRequestWithoutPermissionCheck(clubId, requestId, userId);
        }
      );
    }
  );
}

// Helper function to process request without permission check
async function processRequestWithoutPermissionCheck(
  clubId: number,
  requestId: number,
  userId?: string
): Promise<boolean> {
  // Obter o ID do usuário e garantir que seja um UUID válido
  const userUUID = userId || (await supabase.auth.getUser()).data.user?.id;
  if (!userUUID) {
    throw new Error("Usuário não autenticado");
  }

  try {
    // Garantir que userUUID é um UUID válido
    if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(userUUID)) {
      throw new Error("ID de usuário inválido");
    }

    const { data, error } = await supabase.rpc(
      'process_inventory_request',
      {
        p_request_id: requestId,
        p_user_id: userUUID
      }
    );

    if (error) {
      console.error("Error processing inventory request:", error);
      throw new Error(`Error processing inventory request: ${error.message}`);
    }

    return data;
  } catch (err) {
    console.error("Error processing inventory request:", err);
    throw new Error(`Error processing inventory request: ${err instanceof Error ? err.message : String(err)}`);
  }
}

export async function returnInventoryRequestItem(
  clubId: number,
  itemId: number,
  returnQuantity: number,
  userId?: string
): Promise<boolean> {
  // If userId is not provided, don't check permissions (for compatibility)
  if (!userId) {
    return returnRequestItemWithoutPermissionCheck(clubId, itemId, returnQuantity, userId);
  }

  // With userId, check permissions and log audit
  return withPermission(
    clubId,
    userId,
    INVENTORY_PERMISSIONS.REQUESTS.PROCESS,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "inventory.requests.return_item",
        { item_id: itemId, return_quantity: returnQuantity },
        async () => {
          return returnRequestItemWithoutPermissionCheck(clubId, itemId, returnQuantity, userId);
        }
      );
    }
  );
}

// Helper function to return request item without permission check
async function returnRequestItemWithoutPermissionCheck(
  clubId: number,
  itemId: number,
  returnQuantity: number,
  userId?: string
): Promise<boolean> {
  // Obter o ID do usuário e garantir que seja um UUID válido
  const userUUID = userId || (await supabase.auth.getUser()).data.user?.id;
  if (!userUUID) {
    throw new Error("Usuário não autenticado");
  }

  try {
    // Garantir que userUUID é um UUID válido
    if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(userUUID)) {
      throw new Error("ID de usuário inválido");
    }

    const { data, error } = await supabase.rpc(
      'return_inventory_request_item',
      {
        p_request_item_id: itemId,
        p_return_quantity: returnQuantity,
        p_user_id: userUUID
      }
    );

    if (error) {
      console.error("Error returning inventory request item:", error);
      throw new Error(`Error returning inventory request item: ${error.message}`);
    }

    return data;
  } catch (err) {
    console.error("Error returning inventory request item:", err);
    throw new Error(`Error returning inventory request item: ${err instanceof Error ? err.message : String(err)}`);
  }
}

export async function getLowStockShoppingList(
  clubId: number,
  threshold: number = 0,
  userId?: string,
  department?: string
): Promise<InventoryProduct[]> {
  // If userId is not provided, don't check permissions (for compatibility)
  if (!userId) {
    return getLowStockProductsWithoutPermissionCheck(clubId, threshold, department);
  }

  // With userId, check permissions
  return withPermission(
    clubId,
    userId,
    INVENTORY_PERMISSIONS.VIEW,
    async () => {
      return getLowStockProductsWithoutPermissionCheck(clubId, threshold, department);
    }
  );
}

// Helper function to get low stock products without permission check
async function getLowStockProductsWithoutPermissionCheck(
  clubId: number,
  threshold: number = 0,
  department?: string
): Promise<InventoryProduct[]> {
  // Primeiro, vamos buscar todos os produtos
  let query = supabase
    .from("inventory_products")
    .select("*")
    .eq("club_id", clubId)
    .order("quantity");

  // Se um departamento foi especificado, filtramos por ele
  if (department) {
    query = query.eq("department", department);
  }

  const { data, error } = await query;

  if (error) {
    console.error("Error fetching low stock products:", error);
    throw new Error(`Error fetching low stock products: ${error.message}`);
  }

  // Se threshold for 0, usamos a quantidade mínima de cada produto
  if (threshold === 0) {
    // Filtramos manualmente os produtos com estoque baixo
    const lowStockProducts = (data || []).filter(product =>
      product.quantity <= (product.minimum_quantity || 0)
    );
    return lowStockProducts;
  } else {
    // Compatibilidade com código antigo que usa threshold global
    const lowStockProducts = (data || []).filter(product =>
      product.quantity <= threshold
    );
    return lowStockProducts;
  }
}

// Add product image upload function
export async function updateProductImage(
  clubId: number,
  productId: number,
  imageUrl: string,
  userId?: string
): Promise<InventoryProduct> {
  // If userId is not provided, don't check permissions (for compatibility)
  if (!userId) {
    return updateProductImageWithoutPermissionCheck(clubId, productId, imageUrl);
  }

  // With userId, check permissions and log audit
  return withPermission(
    clubId,
    userId,
    INVENTORY_PERMISSIONS.EDIT,
    async () => {
      return withAuditLog(
        clubId,
        userId,
        "inventory.update_image",
        { product_id: productId },
        async () => {
          return updateProductImageWithoutPermissionCheck(clubId, productId, imageUrl);
        }
      );
    }
  );
}

// Helper function to update product image without permission check
async function updateProductImageWithoutPermissionCheck(
  clubId: number,
  productId: number,
  imageUrl: string
): Promise<InventoryProduct> {
  const { data, error } = await supabase
    .from("inventory_products")
    .update({
      image_url: imageUrl,
      updated_at: new Date().toISOString()
    })
    .eq("club_id", clubId)
    .eq("id", productId)
    .select()
    .single();

  if (error) {
    console.error("Error updating product image:", error);
    throw new Error(`Error updating product image: ${error.message}`);
  }

  return data;
}

// Função para processar solicitação com assinatura automática
export async function processInventoryRequestWithAutoSignature(
  clubId: number,
  requestId: number,
  deliveryNotes?: string,
  userId?: string
): Promise<boolean> {
  try {
    if (!userId) {
      throw new Error("ID do usuário é obrigatório");
    }

    // Gerar assinatura automática de entrega
    const automaticSignatureUrl = await generateAutomaticDeliverySignature(
      clubId,
      userId,
      requestId
    );

    // Atualizar a solicitação com a assinatura de entrega e observações
    await updateInventoryRequest(
      clubId,
      requestId,
      {
        delivery_signature_url: automaticSignatureUrl,
        delivery_notes: deliveryNotes || null
      },
      userId
    );

    // Processar a solicitação (reduzir quantidades do estoque)
    let processed = await processInventoryRequest(clubId, requestId, userId);

    // If processing failed, still mark the request as completed
    if (!processed) {
      console.warn(
        "process_inventory_request returned false, forcing status to completed"
      );

      try {
        await updateInventoryRequest(
          clubId,
          requestId,
          { status: "completed" },
          userId
        );
        processed = true;
      } catch (updateError) {
        console.error(
          "Failed to manually mark request as completed:",
          updateError
        );
        processed = false;
      }
    }

    return processed;
  } catch (error) {
    console.error("Erro ao processar solicitação com assinatura automática:", error);
    throw error;
  }
}