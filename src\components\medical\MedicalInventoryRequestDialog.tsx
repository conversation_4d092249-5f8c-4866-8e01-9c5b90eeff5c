import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, <PERSON><PERSON>T<PERSON>le, DialogFooter } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useUser } from "@/context/UserContext";
import {
  getInventoryProducts,
  createInventoryRequest,
  addInventoryRequestItem,
  getMedicalProfessionalByUserId
} from "@/api/api";
import { Loader2, Plus, Trash2 } from "lucide-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

// Schema for validation
const requestSchema = z.object({
  notes: z.string().optional(),
  urgency: z.enum(["normal", "urgent"], {
    required_error: "Nível de urgência é obrigatório",
  }),
});

type RequestFormValues = z.infer<typeof requestSchema>;

interface MedicalInventoryRequestDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: number;
  onSuccess: () => void;
}

export function MedicalInventoryRequestDialog({
  open,
  onOpenChange,
  clubId,
  onSuccess,
}: MedicalInventoryRequestDialogProps) {
  const { user } = useUser();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [products, setProducts] = useState<any[]>([]);
  const [selectedProduct, setSelectedProduct] = useState<number | null>(null);
  const [selectedQuantity, setSelectedQuantity] = useState<number>(1);
  const [requestItems, setRequestItems] = useState<any[]>([]);
  const [medicalProfessional, setMedicalProfessional] = useState<any>(null);

  // Initialize form
  const form = useForm<RequestFormValues>({
    resolver: zodResolver(requestSchema),
    defaultValues: {
      notes: "",
      urgency: "normal",
    },
  });

  // Load data when dialog opens
  useEffect(() => {
    if (open) {
      loadData();
      // Reset form and items
      form.reset({
        notes: "",
        urgency: "normal",
      });
      setRequestItems([]);
    }
  }, [open]);

  // Load products and medical professional data
  const loadData = async () => {
    try {
      setIsLoading(true);

      // Load products from Farmácia department
      const productsData = await getInventoryProducts(clubId, "Farmácia");
      // Filter out products with quantity <= 0
      const availableProducts = productsData.filter(product => product.quantity > 0);
      setProducts(availableProducts);

      // Load medical professional data
      if (user?.id) {
        const medicalProfessionalData = await getMedicalProfessionalByUserId(clubId, user.id);
        setMedicalProfessional(medicalProfessionalData);
      }
    } catch (error) {
      console.error("Error loading data:", error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao carregar os dados. Tente novamente.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Add product to request
  const handleAddProduct = () => {
    if (!selectedProduct || selectedQuantity <= 0) {
      toast({
        title: "Erro",
        description: "Selecione um produto e uma quantidade válida.",
        variant: "destructive",
      });
      return;
    }

    const product = products.find(p => p.id === selectedProduct);
    if (product && product.quantity <= 0) {
      toast({
        title: "Sem estoque",
        description: "Este produto está sem estoque disponível.",
        variant: "destructive",
      });
      return;
    }

    // Check if product is already in the list
    const existingItem = requestItems.find(item => item.product_id === selectedProduct);
    if (existingItem) {
      // Update quantity
      const updatedItems = requestItems.map(item => {
        if (item.product_id === selectedProduct) {
          return {
            ...item,
            quantity: item.quantity + selectedQuantity
          };
        }
        return item;
      });
      setRequestItems(updatedItems);
    } else {
      // Add new item
      const product = products.find(p => p.id === selectedProduct);
      if (product) {
        const newItem = {
          product_id: selectedProduct,
          quantity: selectedQuantity,
          product_name: product.name,
          product_department: product.department,
          product_location: product.location,
          available_quantity: product.quantity
        };
        setRequestItems([...requestItems, newItem]);
      }
    }

    // Reset selection
    setSelectedProduct(null);
    setSelectedQuantity(1);
  };

  // Remove product from request
  const handleRemoveProduct = (productId: number) => {
    setRequestItems(requestItems.filter(item => item.product_id !== productId));
  };

  // Save request
  const onSubmit = async (data: RequestFormValues) => {
    if (requestItems.length === 0) {
      toast({
        title: "Erro",
        description: "Adicione pelo menos um produto à solicitação.",
        variant: "destructive",
      });
      return;
    }

    if (!medicalProfessional) {
      toast({
        title: "Erro",
        description: "Não foi possível identificar o profissional médico. Tente novamente.",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);

      // Create new request
      const requestData = {
        department_id: null,
        requester_type: "medical",
        requester_id: medicalProfessional.id.toString(),
        category: "Farmácia",
        withdrawal_date: new Date().toISOString().split("T")[0],
        requester_notes: `[${data.urgency === "urgent" ? "URGENTE" : "NORMAL"}] ${data.notes || ""}`,
        delivery_method: "pickup",
        delivery_location: null,
        requested_by: user?.id || "",
        status: "pending" as const,
        delivery_notes: null,
        requester_signature_url: null,
        delivery_signature_url: null
      };

      const newRequest = await createInventoryRequest(
        clubId,
        requestData,
        user?.id
      );

      // Add items to the request
      for (const item of requestItems) {
        await addInventoryRequestItem(
          clubId,
          newRequest.id,
          item.product_id,
          item.quantity,
          user?.id
        );
      }

      toast({
        title: "Solicitação criada",
        description: "A solicitação foi criada com sucesso e será analisada pela administração.",
      });

      onSuccess();
      onOpenChange(false);
    } catch (error) {
      console.error("Error saving request:", error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao salvar a solicitação. Tente novamente.",
        variant: "destructive",
      });
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Nova Solicitação de Medicamentos</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="border p-4 rounded-md">
              <h3 className="text-lg font-medium mb-2">Adicionar Medicamentos</h3>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                  <FormLabel>Medicamento</FormLabel>
                  <Select
                    value={selectedProduct?.toString() || ""}
                    onValueChange={(value) => setSelectedProduct(parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o medicamento" />
                    </SelectTrigger>
                    <SelectContent>
                      {products.length > 0 ? (
                        products.map((product) => (
                          <SelectItem
                            key={product.id}
                            value={product.id.toString()}
                            disabled={product.quantity === 0}
                          >
                            {product.name} ({product.quantity} disponíveis)
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="no-products" disabled>
                          Nenhum medicamento disponível
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <FormLabel>Quantidade</FormLabel>
                  <Input
                    type="number"
                    min="1"
                    value={selectedQuantity}
                    onChange={(e) => setSelectedQuantity(parseInt(e.target.value) || 1)}
                  />
                </div>

                <div className="flex items-end">
                  <Button
                    type="button"
                    onClick={handleAddProduct}
                    className="gap-1"
                  >
                    <Plus className="h-4 w-4" />
                    Adicionar
                  </Button>
                </div>
              </div>

              <div className="border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Medicamento</TableHead>
                      <TableHead>Quantidade</TableHead>
                      <TableHead>Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {requestItems.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={3} className="text-center py-4">
                          Nenhum medicamento adicionado
                        </TableCell>
                      </TableRow>
                    ) : (
                      requestItems.map((item, index) => (
                        <TableRow key={`item-${index}`}>
                          <TableCell>{item.product_name}</TableCell>
                          <TableCell>{item.quantity}</TableCell>
                          <TableCell>
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              onClick={() => handleRemoveProduct(item.product_id)}
                            >
                              <Trash2 className="h-4 w-4 text-red-500" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </div>

            <FormField
              control={form.control}
              name="urgency"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nível de Urgência</FormLabel>
                  <Select
                    value={field.value}
                    onValueChange={field.onChange}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o nível de urgência" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="normal">Normal</SelectItem>
                      <SelectItem value="urgent">Urgente</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Observações</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Observações adicionais sobre a solicitação"
                      {...field}
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                disabled={isLoading || requestItems.length === 0}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Enviando...
                  </>
                ) : (
                  "Enviar Solicitação"
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
