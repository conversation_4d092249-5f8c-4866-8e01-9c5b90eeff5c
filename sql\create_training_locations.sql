-- Migration to create training locations and schedule tables

-- 1. Tabela principal de locais de treino
CREATE TABLE IF NOT EXISTS training_locations (
  id SERIAL PRIMARY KEY,
  club_id INTEGER NOT NULL REFERENCES club_info(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  address TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  CONSTRAINT unique_location_per_club UNIQUE(club_id, name)
);

-- 2. Tabela de horários disponíveis por local
CREATE TABLE IF NOT EXISTS training_location_hours (
  id SERIAL PRIMARY KEY,
  location_id INTEGER NOT NULL REFERENCES training_locations(id) ON DELETE CASCADE,
  day_of_week INTEGER NOT NULL CHECK(day_of_week BETWEEN 0 AND 6), -- 0=domingo
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  CONSTRAINT ck_start_before_end CHECK (start_time < end_time)
);

-- 3. Índices para desempenho
CREATE INDEX IF NOT EXISTS idx_training_locations_club_id ON training_locations(club_id);
CREATE INDEX IF NOT EXISTS idx_training_location_hours_location ON training_location_hours(location_id);
CREATE INDEX IF NOT EXISTS idx_training_location_hours_day ON training_location_hours(day_of_week);

-- 4. Habilitar Row Level Security
ALTER TABLE training_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE training_location_hours ENABLE ROW LEVEL SECURITY;

-- 5. Políticas para training_locations
DROP POLICY IF EXISTS training_locations_select ON training_locations;
DROP POLICY IF EXISTS training_locations_insert ON training_locations;
DROP POLICY IF EXISTS training_locations_update ON training_locations;
DROP POLICY IF EXISTS training_locations_delete ON training_locations;

CREATE POLICY training_locations_select ON training_locations
  FOR SELECT
  USING (club_id = get_current_club_id());

CREATE POLICY training_locations_insert ON training_locations
  FOR INSERT
  WITH CHECK (club_id = get_current_club_id());

CREATE POLICY training_locations_update ON training_locations
  FOR UPDATE
  USING (club_id = get_current_club_id())
  WITH CHECK (club_id = get_current_club_id());

CREATE POLICY training_locations_delete ON training_locations
  FOR DELETE
  USING (club_id = get_current_club_id());

-- 6. Políticas para training_location_hours
DROP POLICY IF EXISTS training_location_hours_select ON training_location_hours;
DROP POLICY IF EXISTS training_location_hours_insert ON training_location_hours;
DROP POLICY IF EXISTS training_location_hours_update ON training_location_hours;
DROP POLICY IF EXISTS training_location_hours_delete ON training_location_hours;

CREATE POLICY training_location_hours_select ON training_location_hours
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM training_locations tl
      WHERE tl.id = training_location_hours.location_id
        AND tl.club_id = get_current_club_id()
    )
  );

CREATE POLICY training_location_hours_insert ON training_location_hours
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM training_locations tl
      WHERE tl.id = training_location_hours.location_id
        AND tl.club_id = get_current_club_id()
    )
  );

CREATE POLICY training_location_hours_update ON training_location_hours
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM training_locations tl
      WHERE tl.id = training_location_hours.location_id
        AND tl.club_id = get_current_club_id()
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM training_locations tl
      WHERE tl.id = training_location_hours.location_id
        AND tl.club_id = get_current_club_id()
    )
  );

CREATE POLICY training_location_hours_delete ON training_location_hours
  FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM training_locations tl
      WHERE tl.id = training_location_hours.location_id
        AND tl.club_id = get_current_club_id()
    )
  );

-- Comentários para documentação
COMMENT ON TABLE training_locations IS 'Locais disponíveis para realização de treinos';
COMMENT ON COLUMN training_locations.name IS 'Nome do local de treino';
COMMENT ON COLUMN training_locations.address IS 'Endereço do local de treino';
COMMENT ON TABLE training_location_hours IS 'Horários disponíveis para cada local de treino';
COMMENT ON COLUMN training_location_hours.day_of_week IS 'Dia da semana (0=domingo, 6=sábado)';
COMMENT ON COLUMN training_location_hours.start_time IS 'Horário de início disponível';
COMMENT ON COLUMN training_location_hours.end_time IS 'Horário de término disponível';