import { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { DataTable } from "@/components/ui/data-table";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { InventoryRequest } from "@/api/inventoryRequests";
import { getMyInventoryRequests } from "@/api/api";
import { SolicitacaoEstoqueDialog } from "@/components/estoque/SolicitacaoEstoqueDialog";

export default function SolicitarEstoque() {
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const [requests, setRequests] = useState<InventoryRequest[]>([]);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  async function fetchRequests() {
    if (!clubId || !user?.id) return;
    try {
      setLoading(true);
      const data = await getMyInventoryRequests(clubId, user.id);
      setRequests(data);
    } catch (error) {
      console.error("Erro ao carregar solicitações:", error);
    } finally {
      setLoading(false);
    }
  }

  useEffect(() => {
    fetchRequests();
  }, [clubId, user?.id]);

  const columns = [
    { key: "id", header: "ID" },
    { key: "category", header: "Categoria" },
    {
      key: "withdrawal_date",
      header: "Data de Retirada",
      render: (row: InventoryRequest) => new Date(row.withdrawal_date).toLocaleDateString(),
    },
    { key: "status", header: "Status" },
  ];

  return (
    <div className="container mx-auto py-6">
      <Card>
        <CardHeader className="flex items-center justify-between">
          <div>
            <CardTitle>Minhas Solicitações</CardTitle>
            <CardDescription>Crie e acompanhe suas solicitações de estoque</CardDescription>
          </div>
          <Button onClick={() => setDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" /> Nova Solicitação
          </Button>
        </CardHeader>
        <CardContent>
          <DataTable data={requests} columns={columns} isLoading={loading} />
        </CardContent>
      </Card>

      <SolicitacaoEstoqueDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        request={null}
        onSuccess={fetchRequests}
      />
    </div>
  );
}
