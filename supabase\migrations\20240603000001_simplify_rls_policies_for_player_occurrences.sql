-- Enable RLS on player_occurrences table
ALTER TABLE public.player_occurrences ENABLE ROW LEVEL SECURITY;

-- Allow authenticated users to read their own club's player occurrences
CREATE POLICY "Enable read access for authenticated users"
ON public.player_occurrences
FOR SELECT
TO authenticated
USING (true);

-- Allow authenticated users to insert their own club's player occurrences
CREATE POLICY "Enable insert for authenticated users"
ON public.player_occurrences
FOR INSERT
TO authenticated
WITH CHECK (true);

-- Allow authenticated users to update their own club's player occurrences
CREATE POLICY "Enable update for authenticated users"
ON public.player_occurrences
FOR UPDATE
TO authenticated
USING (true)
WITH CHECK (true);

-- Allow authenticated users to delete their own club's player occurrences
CREATE POLICY "Enable delete for authenticated users"
ON public.player_occurrences
FOR DELETE
TO authenticated
USING (true);

-- Add a trigger to set the created_by and updated_by fields
CREATE OR REPLACE FUNCTION public.handle_player_occurrence()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    NEW.created_by = auth.uid();
    NEW.updated_by = auth.uid();
  ELSIF TG_OP = 'UPDATE' THEN
    NEW.updated_by = auth.uid();
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger
CREATE TRIGGER handle_player_occurrence
BEFORE INSERT OR UPDATE ON public.player_occurrences
FOR EACH ROW EXECUTE FUNCTION public.handle_player_occurrence();

-- Grant necessary permissions
GRANT ALL ON public.player_occurrences TO authenticated;
GRANT ALL ON SEQUENCE public.player_occurrences_id_seq TO authenticated;
