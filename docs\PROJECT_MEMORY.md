# Documentação SaaS Fut — Gestão de Times de Futebol

## 1. Visão Geral do Projeto

O SaaS Fut é uma plataforma completa para gestão de clubes de futebol, contemplando módulos para elenco, staff, partidas, treinos, finanças, saúde, base, comunicação e analytics. O front-end é React + TypeScript, com dados inicialmente mockados e centralizados via API de serviços ([src/api/api.ts](src/api/api.ts)), prontos para futura integração com backend real.

---

## 2. Estrutura do Projeto

```
/src
  /api
    api.ts         # Central de funções de serviço (mock/fake API)
    index.ts       # Reexporta tudo da API
    README.md      # Instruções para devs sobre padrão de consumo
  /components      # Componentes reutilizáveis
  /pages           # Páginas principais (Elenco, Partidas, Financeiro, etc.)
/documentos
  PROJECT_MEMORY.md # Histórico e decisões técnicas (duplicado, migrar tudo para a raiz!)
PROJECT_MEMORY.md   # Arquivo principal de memória/documentação (usar este!)
```

---

## 3. Funcionamento Geral

- **Consumo de Dados:**  
  Todas as páginas e componentes consomem dados exclusivamente via funções de serviço exportadas de [src/api/api.ts](src/api/api.ts).  
  **Nunca** use mocks locais, dados hardcoded ou imports de arquivos de dados externos nos componentes/pages.

- **Tipos:**  
  Todos os estados usam tipos exportados da API (Player, Staff, UpcomingMatch, MatchHistory, Training, Exercise, PlayerPerformance, etc.).

- **Centralização:**  
  Qualquer alteração futura de dados (ex: integração com backend real) deve ser feita apenas nas funções da API, sem necessidade de alterar componentes/pages.

---

## 4. Guia das Funções da API ([src/api/api.ts](src/api/api.ts))

### Usuário e Autenticação

- `login(email, password): Promise<User>`
  - Simula login, retorna objeto do usuário.
- `register(name, email, password): Promise<User>`
  - Simula cadastro, retorna novo usuário.

### Jogadores e Elenco

- `getPlayers(): Promise<Player[]>`
  - Lista todos os jogadores do elenco.
- `getPlayerById(id: string): Promise<Player | null>`
  - Retorna detalhes completos de um jogador, incluindo contrato, nascimento, jogos recentes e lesões.

### Staff

- `getStaff(): Promise<Staff[]>`
  - Lista todos os membros do staff.

### Partidas

- `getUpcomingMatches(): Promise<UpcomingMatch[]>`
  - Lista próximas partidas.
- `getMatchHistory(): Promise<MatchHistory[]>`
  - Lista partidas anteriores com estatísticas detalhadas.

### Treinamentos

- `getTrainings(): Promise<Training[]>`
  - Lista sessões de treino.
- `getExercises(): Promise<Exercise[]>`
  - Lista exercícios disponíveis.
- `getPlayerPerformance(playerId: string): Promise<PlayerPerformance[]>`
  - Retorna desempenho do jogador em treinos.

### Financeiro

- `getSalaries(): Promise<Salary[]>`
  - Lista salários dos jogadores.
- `getContracts(): Promise<Contract[]>`
  - Lista contratos ativos.
- `getFinancialTransactions(): Promise<FinancialTransaction[]>`
  - Lista transações financeiras do clube.

### Clube

- `getClubInfo(): Promise<ClubInfo>`
  - Retorna informações do clube (nome, logo, cores).

### Médico

- `getMedicalRecords(): Promise<MedicalRecord[]>`
  - Lista registros médicos de atletas.
- `getRehabSchedule(): Promise<RehabSession[]>`
  - Lista sessões de reabilitação agendadas para atletas.

### Agenda

- `getAgendaEvents(): Promise<AgendaEvent[]>`
  - Lista eventos gerais do clube (treinos, jogos, reuniões, etc).

---

## 5. Padrão de Consumo

- Sempre faça import das funções de serviço:
  ```typescript
  import { getPlayers } from '../api';
  ```
- Nunca declare dados fixos ou mocks em componentes/pages.
- Sempre alinhe tipos de estado aos tipos exportados da API.

---

## 6. Como evoluir o projeto

- Para integrar com backend real, basta trocar a implementação das funções da API para chamadas HTTP reais (ex: usando fetch/axios).
- Para adicionar novos módulos, crie funções de serviço na API e consuma-as nas páginas.
- Atualize sempre este arquivo com decisões técnicas e padrões adotados.

---

## 7. Histórico e Decisões Técnicas

- Todas as páginas migradas para consumir dados exclusivamente via funções da API.
- Tipos corrigidos e alinhados, eliminando lints e erros de propriedades inexistentes.
- Mock de getPlayers e getPlayerById corrigidos para refletir todos os campos opcionais e cenários reais.
- Funções para salários, contratos e transações financeiras adicionadas.
- Diretório `/documentos` criado, mas toda documentação principal deve ficar na raiz (`PROJECT_MEMORY.md`).
- Projeto buildado e testado manualmente, sem erros de tipagem ou propriedades inexistentes.

---

## 8. Próximos Passos Sugeridos

- Adicionar testes automatizados (Jest/RTL) para fluxos críticos.
- Documentar endpoints reais quando integrar backend.
- Migrar histórico do `/documentos/PROJECT_MEMORY.md` para este arquivo e manter apenas um ponto de verdade.
- Atualizar sempre este arquivo a cada milestone relevante.

---

> Última atualização: 17/04/2025

---

Se quiser exemplos de uso, fluxos de tela, ou checklist de QA/manual, só pedir!  
Se desejar, posso migrar todo conteúdo do `/documentos/PROJECT_MEMORY.md` para este arquivo e remover duplicidade.

---

**Pronto para onboarding, evolução e integração!**  
Se precisar de mais detalhes sobre qualquer função, fluxo ou padrão, só avisar!
