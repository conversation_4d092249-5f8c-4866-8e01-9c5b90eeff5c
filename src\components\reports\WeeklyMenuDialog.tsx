import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, Di<PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

export interface WeeklyMenuMeal {
  day: string;
  breakfast: string;
  breakfastStart?: string;
  breakfastEnd?: string;
  lunch: string;
  lunchStart?: string;
  lunchEnd?: string;
  snack: string;
  snackStart?: string;
  snackEnd?: string;
  dinner: string;
  dinnerStart?: string;
  dinnerEnd?: string;
}

interface WeeklyMenuDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: (meals: WeeklyMenuMeal[]) => void;
  defaultMeals?: WeeklyMenuMeal[];
}

export function WeeklyMenuDialog({ open, onOpenChange, onConfirm, defaultMeals }: WeeklyMenuDialogProps) {
  const weekDays = ['Segunda-feira', 'Ter<PERSON>-feira', 'Quarta-feira', '<PERSON>uinta-feira', 'Sexta-feira', '<PERSON>ábado', 'Domingo'];
  const [meals, setMeals] = useState<WeeklyMenuMeal[]>([]);

  useEffect(() => {
    if (open) {
      if (defaultMeals && defaultMeals.length) {
        setMeals(defaultMeals);
      } else {
        setMeals(
          weekDays.map(day => ({
            day,
            breakfast: '',
            breakfastStart: '',
            breakfastEnd: '',
            lunch: '',
            lunchStart: '',
            lunchEnd: '',
            snack: '',
            snackStart: '',
            snackEnd: '',
            dinner: '',
            dinnerStart: '',
            dinnerEnd: ''
          }))
        );
      }
    }
  }, [open]);

  const handleChange = (index: number, field: keyof WeeklyMenuMeal, value: string) => {
    setMeals(prev => {
      const updated = [...prev];
      updated[index] = { ...updated[index], [field]: value };
      return updated;
    });
  };

  const handleBlur = (index: number, field: keyof WeeklyMenuMeal) => {
    if (index !== 0) return;
    setMeals(prev => {
      const updated = [...prev];
      const value = updated[0][field];
      for (let i = 1; i < updated.length; i++) {
        if (!updated[i][field]) {
          updated[i] = { ...updated[i], [field]: value };
        }
      }
      return updated;
    });
  };

  const handleConfirm = () => {
    onConfirm(meals);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle>Preencher Cardápio Semanal</DialogTitle>
        </DialogHeader>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr>
                <th className="text-left p-2">Dia</th>
                <th className="text-left p-2">Café da Manhã</th>
                <th className="text-left p-2">Almoço</th>
                <th className="text-left p-2">Lanche</th>
                <th className="text-left p-2">Jantar</th>
              </tr>
            </thead>
            <tbody>
              {meals.map((meal, idx) => (
                <tr key={meal.day} className="border-t">
                  <td className="p-2 font-medium whitespace-nowrap">{meal.day}</td>
                  <td className="p-2 space-y-1">
                    <Input
                      value={meal.breakfast}
                      onChange={e => handleChange(idx, 'breakfast', e.target.value)}
                      onBlur={() => handleBlur(idx, 'breakfast')}
                      placeholder="Descrição"
                    />
                    <div className="flex gap-1">
                      <Input
                        type="time"
                        value={meal.breakfastStart}
                        onChange={e => handleChange(idx, 'breakfastStart', e.target.value)}
                        onBlur={() => handleBlur(idx, 'breakfastStart')}
                        className="h-8"
                      />
                      <Input
                        type="time"
                        value={meal.breakfastEnd}
                        onChange={e => handleChange(idx, 'breakfastEnd', e.target.value)}
                        onBlur={() => handleBlur(idx, 'breakfastEnd')}
                        className="h-8"
                      />
                    </div>
                  </td>
                  <td className="p-2 space-y-1">
                    <Input
                      value={meal.lunch}
                      onChange={e => handleChange(idx, 'lunch', e.target.value)}
                      onBlur={() => handleBlur(idx, 'lunch')}
                      placeholder="Descrição"
                    />
                    <div className="flex gap-1">
                      <Input
                        type="time"
                        value={meal.lunchStart}
                        onChange={e => handleChange(idx, 'lunchStart', e.target.value)}
                        onBlur={() => handleBlur(idx, 'lunchStart')}
                        className="h-8"
                      />
                      <Input
                        type="time"
                        value={meal.lunchEnd}
                        onChange={e => handleChange(idx, 'lunchEnd', e.target.value)}
                        onBlur={() => handleBlur(idx, 'lunchEnd')}
                        className="h-8"
                      />
                    </div>
                  </td>
                  <td className="p-2 space-y-1">
                    <Input
                      value={meal.snack}
                      onChange={e => handleChange(idx, 'snack', e.target.value)}
                      onBlur={() => handleBlur(idx, 'snack')}
                      placeholder="Descrição"
                    />
                    <div className="flex gap-1">
                      <Input
                        type="time"
                        value={meal.snackStart}
                        onChange={e => handleChange(idx, 'snackStart', e.target.value)}
                        onBlur={() => handleBlur(idx, 'snackStart')}
                        className="h-8"
                      />
                      <Input
                        type="time"
                        value={meal.snackEnd}
                        onChange={e => handleChange(idx, 'snackEnd', e.target.value)}
                        onBlur={() => handleBlur(idx, 'snackEnd')}
                        className="h-8"
                      />
                    </div>
                  </td>
                  <td className="p-2 space-y-1">
                    <Input
                      value={meal.dinner}
                      onChange={e => handleChange(idx, 'dinner', e.target.value)}
                      onBlur={() => handleBlur(idx, 'dinner')}
                      placeholder="Descrição"
                    />
                    <div className="flex gap-1">
                      <Input
                        type="time"
                        value={meal.dinnerStart}
                        onChange={e => handleChange(idx, 'dinnerStart', e.target.value)}
                        onBlur={() => handleBlur(idx, 'dinnerStart')}
                        className="h-8"
                      />
                      <Input
                        type="time"
                        value={meal.dinnerEnd}
                        onChange={e => handleChange(idx, 'dinnerEnd', e.target.value)}
                        onBlur={() => handleBlur(idx, 'dinnerEnd')}
                        className="h-8"
                      />
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>Cancelar</Button>
          <Button onClick={handleConfirm}>Gerar</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}