import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import { parseISO } from "date-fns"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Format a date string to a localized format
 * @param dateString Date string to format
 * @param options Intl.DateTimeFormatOptions
 * @returns Formatted date string
 */
export function formatDate(
  dateString: string | undefined | null,
  options: Intl.DateTimeFormatOptions = {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  }
): string {
  if (!dateString) return '-';

  try {
    // Use parseISO to avoid timezone issues with date strings in YYYY-MM-DD format
    const date = parseISO(dateString);
    return new Intl.DateTimeFormat('pt-BR', options).format(date);
  } catch (error) {
    console.error('Error formatting date:', error);
    return dateString;
  }
}

/**
 * Format a date string using UTC timezone to avoid off-by-one issues
 * with dates stored without timezone information (e.g. birthdates).
 * @param dateString Date string to format
 * @param options Intl.DateTimeFormatOptions
 */
export function formatDateUTC(
  dateString: string | undefined | null,
  options: Intl.DateTimeFormatOptions = {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  }
): string {
  if (!dateString) return '-';

  try {
    const date = parseISO(dateString);
    return new Intl.DateTimeFormat('pt-BR', { timeZone: 'UTC', ...options }).format(date);
  } catch (error) {
    console.error('Error formatting UTC date:', error);
    return dateString ?? '-';
  }
}

/**
 * Format a number as currency (BRL)
 * @param value Number to format
 * @returns Formatted currency string
 */
export function formatCurrency(
  value: number | string | undefined | null
): string {
  if (value === undefined || value === null) return 'R$ 0,00';

  const numericValue = typeof value === 'string' ? parseFloat(value) : value;

  if (isNaN(numericValue)) return 'R$ 0,00';

  return `R$ ${numericValue.toLocaleString('pt-BR', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })}`;
}

/**
 * Calculate age from birthdate
 * @param birthdate Birthdate string
 * @returns Age in years
 */
export function calculateAge(birthdate: string | undefined | null): number {
  if (!birthdate) return 0;

  try {
    const birth = new Date(birthdate);
    const today = new Date();

    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();

    // If birthday hasn't occurred yet this year, subtract 1
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }

    return age;
  } catch (error) {
    console.error('Error calculating age:', error);
    return 0;
  }
}
