import { create } from "zustand";
import {
  AdministrativeDocument,
  getAdministrativeDocuments,
  getAdministrativeDocumentById,
  createAdministrativeDocument,
  updateAdministrativeDocument,
  deleteAdministrativeDocument,
  signAdministrativeDocument,
  generateDocumentPDF
} from "@/api/api";
import { getClubInfo } from "@/api/api";

interface AdministrativeDocumentsState {
  documents: AdministrativeDocument[];
  total: number;
  selectedDocument: AdministrativeDocument | null;
  loading: boolean;
  error: string | null;
  fetchDocuments: (clubId: number, page?: number, pageSize?: number) => Promise<void>;
  getDocumentById: (clubId: number, id: number) => Promise<AdministrativeDocument>;
  addDocument: (clubId: number, document: Omit<AdministrativeDocument, "id" | "created_at" | "updated_at" | "document_number">) => Promise<void>;
  updateDocument: (clubId: number, id: number, updates: Partial<AdministrativeDocument>) => Promise<void>;
  removeDocument: (clubId: number, id: number) => Promise<void>;
  signDocument: (clubId: number, id: number, userId: string) => Promise<void>;
  generatePDF: (document: AdministrativeDocument) => Promise<Blob>;
  setSelectedDocument: (document: AdministrativeDocument | null) => void;
}

export const useAdministrativeDocumentsStore = create<AdministrativeDocumentsState>((set, get) => ({
  documents: [],
  total: 0,
  selectedDocument: null,
  loading: false,
  error: null,

  fetchDocuments: async (clubId: number, page: number = 1, pageSize: number = 10): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const offset = (page - 1) * pageSize;
      const { data, total } = await getAdministrativeDocuments(clubId, pageSize, offset);
      set({ documents: data, total, loading: false });
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao buscar documentos", loading: false });
    }
  },

  getDocumentById: async (clubId: number, id: number): Promise<AdministrativeDocument> => {
    set({ loading: true, error: null });
    try {
      const document = await getAdministrativeDocumentById(clubId, id);
      set({ loading: false });
      return document;
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao buscar documento", loading: false });
      throw err;
    }
  },

  addDocument: async (
    clubId: number,
    document: Omit<AdministrativeDocument, "id" | "created_at" | "updated_at" | "document_number">
  ): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const newDocument = await createAdministrativeDocument(clubId, document);
      set((state) => ({
        documents: [newDocument, ...state.documents],
        loading: false
      }));
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao adicionar documento", loading: false });
    }
  },

  updateDocument: async (
    clubId: number,
    id: number,
    updates: Partial<AdministrativeDocument>
  ): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const updatedDocument = await updateAdministrativeDocument(clubId, id, updates);
      set((state) => ({
        documents: state.documents.map(doc => doc.id === id ? updatedDocument : doc),
        selectedDocument: state.selectedDocument?.id === id ? updatedDocument : state.selectedDocument,
        loading: false
      }));
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao atualizar documento", loading: false });
    }
  },

  removeDocument: async (clubId: number, id: number): Promise<void> => {
    set({ loading: true, error: null });
    try {
      await deleteAdministrativeDocument(clubId, id);
      set((state) => ({
        documents: state.documents.filter(doc => doc.id !== id),
        selectedDocument: state.selectedDocument?.id === id ? null : state.selectedDocument,
        loading: false
      }));
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao remover documento", loading: false });
    }
  },

  signDocument: async (clubId: number, id: number, userId: string, signatureUrl?: string): Promise<void> => {
    set({ loading: true, error: null });
    try {
      const signedDocument = await signAdministrativeDocument(clubId, id, userId, signatureUrl);
      set((state) => ({
        documents: state.documents.map(doc => doc.id === id ? signedDocument : doc),
        selectedDocument: state.selectedDocument?.id === id ? signedDocument : state.selectedDocument,
        loading: false
      }));
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao assinar documento", loading: false });
    }
  },

  generatePDF: async (document: AdministrativeDocument): Promise<Blob> => {
    set({ loading: true, error: null });
    try {
      const clubInfo = await getClubInfo(document.club_id);
      const pdfBlob = await generateDocumentPDF(document, clubInfo);
      set({ loading: false });
      return pdfBlob;
    } catch (err: unknown) {
      set({ error: err instanceof Error ? err.message : "Erro ao gerar PDF", loading: false });
      throw err;
    }
  },

  setSelectedDocument: (document: AdministrativeDocument | null) => {
    set({ selectedDocument: document });
  }
}));