-- Enable RLS on player_occurrences table
ALTER TABLE public.player_occurrences ENABLE ROW LEVEL SECURITY;

-- Allow authenticated users to read their own club's player occurrences
CREATE POLICY "Enable read access for authenticated users from same club"
ON public.player_occurrences
FOR SELECT
TO authenticated
USING (
  club_id IN (
    SELECT club_id 
    FROM auth.users 
    WHERE id = auth.uid()
  )
);

-- Allow authenticated users to insert their own club's player occurrences
CREATE POLICY "Enable insert for authenticated users from same club"
ON public.player_occurrences
FOR INSERT
TO authenticated
WITH CHECK (
  club_id IN (
    SELECT club_id 
    FROM auth.users 
    WHERE id = auth.uid()
  )
);

-- Allow authenticated users to update their own club's player occurrences
CREATE POLICY "Enable update for authenticated users from same club"
ON public.player_occurrences
FOR UPDATE
TO authenticated
USING (
  club_id IN (
    SELECT club_id 
    FROM auth.users 
    WHERE id = auth.uid()
  )
)
WITH CHECK (
  club_id IN (
    SELECT club_id 
    FROM auth.users 
    WHERE id = auth.uid()
  )
);

-- Allow authenticated users to delete their own club's player occurrences
CREATE POLICY "Enable delete for authenticated users from same club"
ON public.player_occurrences
FOR DELETE
TO authenticated
USING (
  club_id IN (
    SELECT club_id 
    FROM auth.users 
    WHERE id = auth.uid()
  )
);
