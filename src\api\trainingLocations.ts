import { supabase } from "@/integrations/supabase/client";
import type { Database } from "@/integrations/supabase/types";

export type TrainingLocation = Database["public"]["Tables"]["training_locations"]["Row"];
export type TrainingLocationHour = Database["public"]["Tables"]["training_location_hours"]["Row"];

export async function getTrainingLocations(clubId: number): Promise<TrainingLocation[]> {
  const { data, error } = await supabase
    .from("training_locations")
    .select("*")
    .eq("club_id", clubId)
    .order("name");
  if (error) {
    console.error("Erro ao buscar locais:", error);
    throw new Error(`Erro ao buscar locais: ${error.message}`);
  }
  return data || [];
}

export async function createTrainingLocation(
  clubId: number,
  location: Omit<TrainingLocation, "id" | "club_id" | "created_at" | "updated_at">
): Promise<TrainingLocation> {
  const { data, error } = await supabase
    .from("training_locations")
    .insert({ ...location, club_id: clubId })
    .select()
    .single();
  if (error) {
    console.error("Erro ao criar local:", error);
    throw new Error(`Erro ao criar local: ${error.message}`);
  }
  return data!;
}

export async function updateTrainingLocation(
  clubId: number,
  id: number,
  location: Partial<TrainingLocation>
): Promise<TrainingLocation> {
  const { data, error } = await supabase
    .from("training_locations")
    .update({ ...location, updated_at: new Date().toISOString() })
    .eq("club_id", clubId)
    .eq("id", id)
    .select()
    .single();
  if (error) {
    console.error("Erro ao atualizar local:", error);
    throw new Error(`Erro ao atualizar local: ${error.message}`);
  }
  return data!;
}

export async function deleteTrainingLocation(clubId: number, id: number): Promise<void> {
  const { error } = await supabase
    .from("training_locations")
    .delete()
    .eq("club_id", clubId)
    .eq("id", id);
  if (error) {
    console.error("Erro ao excluir local:", error);
    throw new Error(`Erro ao excluir local: ${error.message}`);
  }
}

export async function getLocationHours(locationId: number): Promise<TrainingLocationHour[]> {
  const { data, error } = await supabase
    .from("training_location_hours")
    .select("*")
    .eq("location_id", locationId)
    .order("start_time");
  if (error) {
    console.error("Erro ao buscar horários:", error);
    throw new Error(`Erro ao buscar horários: ${error.message}`);
  }
  return data || [];
}

export async function saveLocationHours(
  locationId: number,
  hours: Omit<TrainingLocationHour, "id" | "location_id" | "created_at">[]
): Promise<void> {
  const { error: delError } = await supabase
    .from("training_location_hours")
    .delete()
    .eq("location_id", locationId);
  if (delError) {
    console.error("Erro ao limpar horários:", delError);
    throw new Error(`Erro ao limpar horários: ${delError.message}`);
  }
  if (hours.length === 0) return;
  const { error } = await supabase
    .from("training_location_hours")
    .insert(hours.map(h => ({ ...h, location_id: locationId })));
  if (error) {
    console.error("Erro ao salvar horários:", error);
    throw new Error(`Erro ao salvar horários: ${error.message}`);
  }
}