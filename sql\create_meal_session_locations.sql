CREATE TABLE IF NOT EXISTS meal_session_locations (
    id SERIAL PRIMARY KEY,
    club_id INTEGER NOT NULL REFERENCES club_info(id) ON DELETE CASCADE,
    meal_session_id INTEGER NOT NULL REFERENCES meal_sessions(id) ON DELETE CASCADE,
    location_id INTEGER NOT NULL REFERENCES meal_locations(id) ON DELETE CASCADE,
    meal_type_id INTEGER NOT NULL REFERENCES meal_types(id) ON DELETE CASCADE,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_meal_session_locations_session_id ON meal_session_locations(meal_session_id);
