import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useState, useEffect } from "react";
import { getOpponents, getCompetitions, Opponent, Competition, getCategories, Category } from "@/api/api";
import { updateUpcomingMatch } from "@/api/matches";
import { toast } from "@/hooks/use-toast";
import { useSeasonStore } from "@/store/useSeasonStore";
import { Loader2, Pencil, Trash2 } from "lucide-react";
import { useUser } from "@/context/UserContext";
import { UpcomingMatch } from "@/api/matches";

interface EditarPartidaDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: number;
  match: UpcomingMatch;
  onMatchUpdated: () => void;
  onDelete: (matchId: string) => void;
}

export function EditarPartidaDialog({ 
  open, 
  onOpenChange, 
  clubId, 
  match,
  onMatchUpdated,
  onDelete
}: EditarPartidaDialogProps) {
  const [opponentId, setOpponentId] = useState(match.opponent);
  const [competition, setCompetition] = useState(match.competition);
  const [date, setDate] = useState(match.date.split('T')[0]);
  const [time, setTime] = useState('');
  const [location, setLocation] = useState(match.location || '');
  const [type, setType] = useState<"casa" | "fora">(match.type as "casa" | "fora" || "casa");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [opponents, setOpponents] = useState<Opponent[]>([]);
  const [competitions, setCompetitions] = useState<Competition[]>([]);
  const { activeSeason } = useSeasonStore();
  const { user } = useUser();

  // Carregar adversários e competições
  useEffect(() => {
    if (open && clubId) {
      const fetchData = async () => {
        try {
          const [opponentsData, competitionsData] = await Promise.all([
            getOpponents(clubId),
            getCompetitions(clubId, activeSeason?.id)
          ]);
          setOpponents(opponentsData);
          setCompetitions(competitionsData);
        } catch (error) {
          console.error("Erro ao carregar dados:", error);
          toast({
            title: "Erro ao carregar dados",
            description: "Não foi possível carregar os dados necessários.",
            variant: "destructive"
          });
        }
      };
      fetchData();
    }
  }, [open, clubId, activeSeason?.id]);

  // Extrair hora do campo date
  useEffect(() => {
    if (match.date) {
      const dateObj = new Date(match.date);
      const hours = String(dateObj.getHours()).padStart(2, '0');
      const minutes = String(dateObj.getMinutes()).padStart(2, '0');
      setTime(`${hours}:${minutes}`);
    }
  }, [match.date]);

  const handleSave = async () => {
    if (!activeSeason) {
      setError("Selecione uma temporada antes de salvar as alterações.");
      return;
    }
    if (!opponentId) {
      setError("O adversário é obrigatório.");
      return;
    }
    if (!competition) {
      setError("A competição é obrigatória.");
      return;
    }
    if (!date) {
      setError("A data é obrigatória.");
      return;
    }
    if (!time) {
      setError("O horário é obrigatório.");
      return;
    }
    if (!location.trim()) {
      setError("O local é obrigatório.");
      return;
    }

    setError("");
    setIsLoading(true);

    try {
      // Formatar data no formato YYYY-MM-DD
      const formattedDate = new Date(date).toISOString().split('T')[0];
      
      await updateUpcomingMatch(clubId, match.id, {
        opponent: opponentId,
        competition,
        date: formattedDate,
        time: time,
        location: location.trim(),
        type,
        season_id: activeSeason.id,
        category_id: match.category_id,
        ida_volta: match.ida_volta
      });

      toast({
        title: "Partida atualizada",
        description: "As alterações na partida foram salvas com sucesso.",
      });

      onMatchUpdated();
      onOpenChange(false);
    } catch (error) {
      console.error("Erro ao atualizar partida:", error);
      setError("Ocorreu um erro ao atualizar a partida. Tente novamente.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!window.confirm("Tem certeza que deseja excluir esta partida? Esta ação não pode ser desfeita.")) {
      return;
    }

    setIsDeleting(true);
    try {
      onDelete(match.id);
      onOpenChange(false);
    } catch (error) {
      console.error("Erro ao excluir partida:", error);
      toast({
        title: "Erro",
        description: "Não foi possível excluir a partida. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Editar Partida</DialogTitle>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="opponent" className="text-right">
              Adversário
            </Label>
            <select
              id="opponent"
              value={opponentId}
              onChange={(e) => setOpponentId(e.target.value)}
              className="col-span-3 p-2 border rounded"
            >
              <option value="">Selecione um adversário</option>
              {opponents.map((opponent) => (
                <option key={opponent.id} value={opponent.name}>
                  {opponent.name}
                </option>
              ))}
            </select>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="competition" className="text-right">
              Competição
            </Label>
            <select
              id="competition"
              value={competition}
              onChange={(e) => setCompetition(e.target.value)}
              className="col-span-3 p-2 border rounded"
            >
              <option value="">Selecione uma competição</option>
              {competitions.map((comp) => (
                <option key={comp.id} value={comp.name}>
                  {comp.name}
                </option>
              ))}
            </select>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="date" className="text-right">
              Data
            </Label>
            <Input
              id="date"
              type="date"
              value={date}
              onChange={(e) => setDate(e.target.value)}
              className="col-span-3"
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="time" className="text-right">
              Horário
            </Label>
            <Input
              id="time"
              type="time"
              value={time}
              onChange={(e) => setTime(e.target.value)}
              className="col-span-3"
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="location" className="text-right">
              Local
            </Label>
            <Input
              id="location"
              value={location}
              onChange={(e) => setLocation(e.target.value)}
              className="col-span-3"
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right">Tipo</Label>
            <div className="col-span-3 flex gap-4">
              <label className="flex items-center space-x-2">
                <input
                  type="radio"
                  checked={type === "casa"}
                  onChange={() => setType("casa")}
                  className="h-4 w-4"
                />
                <span>Em Casa</span>
              </label>
              <label className="flex items-center space-x-2">
                <input
                  type="radio"
                  checked={type === "fora"}
                  onChange={() => setType("fora")}
                  className="h-4 w-4"
                />
                <span>Fora de Casa</span>
              </label>
            </div>
          </div>

          {error && (
            <div className="text-red-500 text-sm mt-2">{error}</div>
          )}
        </div>

        <DialogFooter className="flex justify-between">
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={isLoading || isDeleting}
          >
            {isDeleting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Excluindo...
              </>
            ) : (
              <>
                <Trash2 className="mr-2 h-4 w-4" />
                Excluir Partida
              </>
            )}
          </Button>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isLoading || isDeleting}
            >
              Cancelar
            </Button>
            <Button
              onClick={handleSave}
              disabled={isLoading || isDeleting}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Salvando...
                </>
              ) : (
                <>
                  <Pencil className="mr-2 h-4 w-4" />
                  Salvar Alterações
                </>
              )}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
