# Sistema de Partida em Tempo Real - Documentação

## Visão Geral

O novo sistema de gerenciamento de partidas em tempo real resolve os problemas de:
- ⏱️ **Tempo perdido ao recarregar a página**
- 🌐 **Problemas de conexão lenta afetando o cronômetro**
- 💾 **Perda de estado da partida**
- 🔄 **Timeline de eventos não persistente**

## Arquitetura

### 1. Banco de Dados (`match_live_state`)

**Tabela principal:**
```sql
match_live_state (
  id, club_id, match_id,
  is_live, is_paused, is_finished,
  match_start_time, total_elapsed_seconds, pause_start_time,
  score_home, score_away,
  created_at, updated_at
)
```

**Funções do banco:**
- `start_match_live()` - Inicia partida
- `pause_match_live()` - Pausa partida
- `resume_match_live()` - Retoma partida
- `finish_match_live()` - Finaliza partida
- `update_match_score()` - Atualiza placar
- `get_match_live_state()` - Obtém estado atual

### 2. API (`src/api/matchLiveState.ts`)

**Classe principal:** `MatchLiveStateManager`

**Recursos:**
- ✅ Sincronização automática a cada 3 segundos
- ✅ Detecção de conexão online/offline
- ✅ Retry automático com backoff
- ✅ Recuperação de estado ao recarregar
- ✅ Cálculo de tempo baseado no servidor

### 3. Interface (`src/pages/Partidas.tsx`)

**Melhorias:**
- ✅ Estado persistente no banco
- ✅ Indicador visual de sincronização
- ✅ Recuperação automática de estado
- ✅ Tempo preciso independente de conexão

## Como Usar

### 1. Executar Script SQL

Primeiro, execute o arquivo SQL no Supabase:

```bash
# No Supabase SQL Editor, execute:
sql/match-live-state.sql
```

### 2. Iniciar Partida

1. Selecione uma partida na lista
2. Vá para a aba "Durante o Jogo"
3. Clique em "Iniciar Partida"
4. O cronômetro começará e será sincronizado automaticamente

### 3. Gerenciar Partida

**Controles disponíveis:**
- ▶️ **Iniciar** - Começa o cronômetro
- ⏸️ **Pausar** - Pausa o tempo (mantém estado)
- ▶️ **Retomar** - Continua de onde parou
- ⏹️ **Finalizar** - Encerra e salva resultado

**Eventos durante o jogo:**
- ⚽ Gols (com tempo automático)
- 🟨🟥 Cartões (com tempo automático)
- 🔄 Substituições (com tempo automático)
- 📝 Notas (com tempo automático)

### 4. Indicadores Visuais

**Status de sincronização:**
- 🟢 **Verde** - Sincronizado corretamente
- 🟡 **Amarelo** - Algumas falhas de sincronização
- 🔴 **Vermelho** - Offline ou muitas falhas

## Vantagens do Novo Sistema

### ⏱️ Tempo Preciso
- Baseado em timestamps do servidor
- Não afetado por lag de conexão
- Mantém precisão mesmo offline temporariamente

### 💾 Estado Persistente
- Sobrevive a recarregamentos de página
- Mantém histórico de eventos
- Recuperação automática de estado

### 🌐 Robustez de Conexão
- Funciona com conexão instável
- Retry automático em falhas
- Sincronização inteligente

### 🔄 Sincronização Automática
- Atualização a cada 3 segundos
- Detecção de reconexão
- Múltiplas sessões sincronizadas

## Fluxo de Funcionamento

### Iniciar Partida
1. Usuário clica "Iniciar Partida"
2. Sistema chama `start_match_live()`
3. Banco salva timestamp de início
4. Sincronização automática inicia
5. Interface atualiza em tempo real

### Durante a Partida
1. Timer calculado pelo servidor
2. Eventos salvos com tempo atual
3. Placar atualizado automaticamente
4. Estado sincronizado continuamente

### Pausar/Retomar
1. Pausa salva tempo decorrido
2. Retomar cria novo timestamp
3. Cálculo mantém tempo total correto

### Finalizar
1. Tempo final calculado
2. Estado marcado como finalizado
3. Resultado salvo na tabela matches
4. Sincronização para

## Resolução de Problemas

### Problema: Tempo não atualiza
**Solução:** Verificar indicador de sincronização
- Se vermelho: problema de conexão
- Se amarelo: falhas temporárias
- Aguardar reconexão automática

### Problema: Estado perdido
**Solução:** Recarregar página
- Estado será recuperado automaticamente
- Sincronização retomará

### Problema: Múltiplas sessões dessincronizadas
**Solução:** Sistema sincroniza automaticamente
- Todas as sessões recebem mesmo estado
- Última ação prevalece

## Monitoramento

### Logs Disponíveis
```javascript
// No console do navegador:
// - "Conexão restaurada - reiniciando sincronização"
// - "Conexão perdida - pausando sincronização"
// - "Erro na sincronização (tentativa X)"
// - "Muitas falhas de sincronização - pausando temporariamente"
```

### Informações de Debug
```javascript
// Acessar informações de sincronização:
matchStateManager.getSyncInfo()
// Retorna: { lastSyncTime, syncFailureCount, isOnline, isActive }
```

## Migração do Sistema Antigo

O sistema antigo (baseado em `useState` local) foi completamente substituído:

**Antes:**
- Estado local volátil
- Timer baseado em `setInterval`
- Perda de dados ao recarregar

**Depois:**
- Estado persistente no banco
- Timer baseado em servidor
- Recuperação automática

## Próximos Passos

1. ✅ Executar SQL no banco
2. ✅ Testar funcionalidades básicas
3. ✅ Validar sincronização
4. ✅ Testar cenários de falha
5. 📋 Treinar usuários no novo sistema
