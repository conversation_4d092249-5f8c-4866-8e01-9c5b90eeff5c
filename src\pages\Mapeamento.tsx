import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "@/components/ui/use-toast";
import { Loader2, Save, FileDown, Trash2, Copy } from "lucide-react";
import { useCurrentClubId } from "@/context/ClubContext";
import { getCategories, Category, getCategoryPlayers } from "@/api/categories";
import { Player, getPlayerCategoryByUserId } from "@/api/players";
import { usePermission } from "@/hooks/usePermission";
import { useUser } from "@/context/UserContext";
import {
  getCategoryMappings,
  getMainCategoryMapping,
  saveMainCategoryMapping,
  createCategoryMapping,
  deleteCategoryMapping,
  duplicateCategoryMapping,
  FieldMapping,
  MappingPosition,
  CategoryMapping,
} from "@/api/categoryMappings";
import { generateMappingPDF } from "@/utils/pdfGenerator";
import { getClubInfo } from "@/api/api";
import InteractiveFootballField from "@/components/mapping/InteractiveFootballField";
import PlayerSelectionModal from "@/components/mapping/PlayerSelectionModal";

const Mapeamento: React.FC = () => {
  const clubId = useCurrentClubId();
  const { role } = usePermission();
  const { user } = useUser();

  // Estados principais
  const [categories, setCategories] = useState<Category[]>([]);
  const [players, setPlayers] = useState<Player[]>([]);
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null);
  const [mappings, setMappings] = useState<CategoryMapping[]>([]);
  const [selectedMappingId, setSelectedMappingId] = useState<number | null>(null);
  const [currentMapping, setCurrentMapping] = useState<FieldMapping>({});

  // Estados de loading
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  // Estado para verificar se é um jogador
  const [isPlayerView, setIsPlayerView] = useState(false);
  const [playerCategoryId, setPlayerCategoryId] = useState<number | null>(null);
  
  // Estados do modal
  const [isPlayerModalOpen, setIsPlayerModalOpen] = useState(false);
  const [selectedPosition, setSelectedPosition] = useState<{
    key: string;
    type: string;
    label: string;
  } | null>(null);

  // Verificar se é um jogador e carregar sua categoria
  useEffect(() => {
    const checkPlayerRole = async () => {
      if (role === "player" && user?.id && clubId) {
        setIsPlayerView(true);
        try {
          const categoryId = await getPlayerCategoryByUserId(clubId, user.id);
          if (categoryId) {
            setPlayerCategoryId(categoryId);
            setSelectedCategoryId(categoryId);
          }
        } catch (error) {
          console.error("Erro ao carregar categoria do jogador:", error);
          toast({
            title: "Erro",
            description: "Não foi possível carregar sua categoria.",
            variant: "destructive",
          });
        }
      } else {
        setIsPlayerView(false);
      }
    };

    checkPlayerRole();
  }, [role, user?.id, clubId]);

  // Carregar dados iniciais
  useEffect(() => {
    loadInitialData();
  }, [clubId, isPlayerView]);

  // Carregar mapeamentos quando categoria mudar
  useEffect(() => {
    if (selectedCategoryId) {
      loadCategoryMappings();
    }
  }, [selectedCategoryId]);

  const loadInitialData = async () => {
    try {
      setIsLoading(true);

      const categoriesData = await getCategories(clubId);
      setCategories(categoriesData);

      // Selecionar primeira categoria automaticamente apenas se não for jogador
      if (categoriesData.length > 0 && !isPlayerView) {
        setSelectedCategoryId(categoriesData[0].id);
      }
    } catch (error) {
      console.error("Erro ao carregar dados:", error);
      toast({
        title: "Erro",
        description: "Erro ao carregar dados iniciais",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadCategoryMappings = async () => {
    if (!selectedCategoryId) return;

    try {
      // Carregar jogadores da categoria e mapeamentos em paralelo
      const [mappingsData, playersData] = await Promise.all([
        getCategoryMappings(clubId, selectedCategoryId),
        getCategoryPlayers(clubId, selectedCategoryId, {
          includeInactive: false,
          includeLoaned: false
        })
      ]);

      setMappings(mappingsData);
      setPlayers(playersData);

      // Carregar mapeamento principal
      const mainMapping = await getMainCategoryMapping(clubId, selectedCategoryId);
      if (mainMapping) {
        setCurrentMapping(mainMapping.mapping as FieldMapping);
        setSelectedMappingId(mainMapping.id);
      } else {
        setCurrentMapping({});
        setSelectedMappingId(null);
      }
    } catch (error) {
      console.error("Erro ao carregar mapeamentos:", error);
      toast({
        title: "Erro",
        description: "Erro ao carregar mapeamentos da categoria",
        variant: "destructive",
      });
    }
  };

  const handlePositionClick = (positionKey: string) => {
    // Determinar tipo de posição baseado na chave
    let positionType = "all";
    let positionLabel = positionKey.toUpperCase();
    
    if (positionKey.startsWith("gk")) {
      positionType = "goalkeeper";
      positionLabel = "Goleiro";
    } else if (positionKey.startsWith("ld") || positionKey.startsWith("le") || positionKey.startsWith("zag")) {
      positionType = "defender";
      positionLabel = "Defensor";
    } else if (positionKey.startsWith("md") || positionKey.startsWith("me") || positionKey.startsWith("mc")) {
      positionType = "midfielder";
      positionLabel = "Meio-campista";
    } else if (positionKey.startsWith("ad") || positionKey.startsWith("ae") || positionKey.startsWith("ac")) {
      positionType = "attacker";
      positionLabel = "Atacante";
    }

    setSelectedPosition({
      key: positionKey,
      type: positionType,
      label: positionLabel,
    });
    setIsPlayerModalOpen(true);
  };

  const handleSelectPlayer = (player: MappingPosition) => {
    if (!selectedPosition) return;

    setCurrentMapping(prev => ({
      ...prev,
      [selectedPosition.key]: player,
    }));

    setSelectedPosition(null);
  };

  const handleRemovePlayer = (positionKey: string) => {
    setCurrentMapping(prev => {
      const newMapping = { ...prev };
      delete newMapping[positionKey];
      return newMapping;
    });
  };

  const handleSaveMapping = async () => {
    if (!selectedCategoryId) {
      toast({
        title: "Erro",
        description: "Selecione uma categoria primeiro",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSaving(true);
      
      await saveMainCategoryMapping(clubId, selectedCategoryId, currentMapping);
      
      toast({
        title: "Sucesso",
        description: "Mapeamento salvo com sucesso!",
      });
      
      // Recarregar mapeamentos
      await loadCategoryMappings();
    } catch (error) {
      console.error("Erro ao salvar mapeamento:", error);
      toast({
        title: "Erro",
        description: "Erro ao salvar mapeamento",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleClearMapping = () => {
    setCurrentMapping({});
    toast({
      title: "Sucesso",
      description: "Mapeamento limpo com sucesso!",
    });
  };

  const handleGeneratePDF = async () => {
    if (!selectedCategoryId || Object.keys(currentMapping).length === 0) {
      toast({
        title: "Erro",
        description: "Selecione uma categoria e adicione jogadores ao mapeamento primeiro",
        variant: "destructive",
      });
      return;
    }

    try {
      // Buscar informações do clube
      const clubInfo = await getClubInfo(clubId);
      const selectedCategory = categories.find(c => c.id === selectedCategoryId);

      await generateMappingPDF({
        categoryName: selectedCategory?.name || "Categoria",
        clubName: clubInfo?.name || "Seu Clube",
        mapping: currentMapping,
        date: new Date().toLocaleDateString("pt-BR"),
      });

      toast({
        title: "Sucesso",
        description: "PDF gerado com sucesso!",
      });
    } catch (error) {
      console.error("Erro ao gerar PDF:", error);
      toast({
        title: "Erro",
        description: "Erro ao gerar PDF. Verifique se as dependências estão instaladas.",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">
          {isPlayerView ? "Meu Mapeamento" : "Mapeamento de Jogadores"}
        </h1>

        {!isPlayerView && (
          <div className="flex gap-2">
            <Button
              onClick={handleGeneratePDF}
              variant="outline"
              className="flex items-center gap-2"
            >
              <FileDown className="h-4 w-4" />
              Gerar PDF
            </Button>

            <Button
              onClick={handleClearMapping}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Trash2 className="h-4 w-4" />
              Limpar
            </Button>

            <Button
              onClick={handleSaveMapping}
              disabled={isSaving}
              className="flex items-center gap-2"
            >
              {isSaving ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Save className="h-4 w-4" />
              )}
              Salvar
            </Button>
          </div>
        )}
      </div>

      {/* Seleção de categoria */}
      {!isPlayerView && (
        <Card>
          <CardHeader>
            <CardTitle>Selecionar Categoria</CardTitle>
          </CardHeader>
          <CardContent>
            <Select
              value={selectedCategoryId?.toString() || ""}
              onValueChange={(value) => setSelectedCategoryId(parseInt(value))}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Selecione uma categoria" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id.toString()}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </CardContent>
        </Card>
      )}

      {/* Informação da categoria para jogadores */}
      {isPlayerView && selectedCategoryId && (
        <Card>
          <CardHeader>
            <CardTitle>Minha Categoria</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-lg font-medium">
              {categories.find(c => c.id === selectedCategoryId)?.name || "Categoria não encontrada"}
            </p>
            <p className="text-sm text-muted-foreground mt-2">
              Este é o mapeamento da sua categoria. Você pode visualizar as posições, mas não pode fazer alterações.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Campo de futebol */}
      {selectedCategoryId && (
        <Card>
          <CardHeader>
            <CardTitle>Campo de Futebol - Mapeamento de Posições</CardTitle>
          </CardHeader>
          <CardContent>
            <InteractiveFootballField
              mapping={currentMapping}
              onPositionClick={isPlayerView ? () => {} : handlePositionClick}
              onRemovePlayer={isPlayerView ? () => {} : handleRemovePlayer}
            />
          </CardContent>
        </Card>
      )}

      {/* Modal de seleção de jogadores - apenas para não jogadores */}
      {!isPlayerView && (
        <PlayerSelectionModal
          isOpen={isPlayerModalOpen}
          onClose={() => {
            setIsPlayerModalOpen(false);
            setSelectedPosition(null);
          }}
          onSelectPlayer={handleSelectPlayer}
          players={players}
          positionType={selectedPosition?.type || "all"}
          positionLabel={selectedPosition?.label || ""}
          selectedPlayers={currentMapping}
        />
      )}
    </div>
  );
};

export default Mapeamento;
