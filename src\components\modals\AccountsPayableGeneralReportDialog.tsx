import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { format, isBefore, isAfter } from "date-fns";
import { ptBR } from "date-fns/locale";
import { useCurrentClubId } from "@/context/ClubContext";
import { getClubInfo } from "@/api";
import { getAllPendingPayables, ConsolidatedPayable } from "@/api/financialReports";
import jsPDF from "jspdf";
import autoTable from 'jspdf-autotable';
import { CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { Calendar } from "@/components/ui/calendar";
import { Pop<PERSON>, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

// Função para calcular salário proporcional com base na data de entrada
function calcularSalarioProporcional(dataEntrada: string, salarioMensal: number, dataReferencia: Date): number {
  try {
    console.log('--- Início do cálculo de salário proporcional ---');
    console.log(`Data de entrada: ${dataEntrada}`);
    console.log(`Salário mensal: R$ ${salarioMensal.toFixed(2)}`);
    console.log(`Data de referência: ${dataReferencia.toISOString()}`);
    
    // Se não tiver data de entrada, retorna o salário integral
    if (!dataEntrada) {
      console.log('Sem data de entrada, usando valor integral');
      return salarioMensal;
    }
    
    // Garante que a data de entrada está no formato correto
    const entrada = new Date(dataEntrada);
    
    // Verifica se a data de entrada é válida
    if (isNaN(entrada.getTime())) {
      console.log('Data de entrada inválida, usando valor integral');
      return salarioMensal;
    }
    
    // Define o primeiro e último dia do mês de referência
    const primeiroDiaMes = new Date(dataReferencia.getFullYear(), dataReferencia.getMonth(), 1);
    const ultimoDiaMes = new Date(dataReferencia.getFullYear(), dataReferencia.getMonth() + 1, 0);
    
    // Ajusta as horas para evitar problemas de fuso horário
    primeiroDiaMes.setHours(0, 0, 0, 0);
    ultimoDiaMes.setHours(23, 59, 59, 999);
    entrada.setHours(12, 0, 0, 0); // Meio-dia para evitar problemas de horário de verão
    
    console.log(`Data de entrada ajustada: ${entrada.toISOString()}`);
    console.log(`Período de referência: ${primeiroDiaMes.toISOString()} até ${ultimoDiaMes.toISOString()}`);
    
    // Se a entrada for depois do último dia do mês, não tem direito ao salário
    if (entrada > ultimoDiaMes) {
      console.log('Entrada após o mês de referência, valor zerado');
      return 0;
    }
    
    // Se a entrada for antes ou no primeiro dia do mês, recebe o mês inteiro
    if (entrada <= primeiroDiaMes) {
      console.log('Entrada no início do mês ou antes, valor integral');
      return salarioMensal;
    }
    
    // Calcula dias do mês
    const diasNoMes = ultimoDiaMes.getDate();
    
    // Calcula dias trabalhados (incluindo o dia da entrada)
    const diasTrabalhados = ultimoDiaMes.getDate() - entrada.getDate() + 1;
    
    // Verifica se o cálculo dos dias trabalhados faz sentido
    if (diasTrabalhados <= 0 || diasTrabalhados > diasNoMes) {
      console.log(`Cálculo de dias trabalhados inválido: ${diasTrabalhados} dias, usando valor integral`);
      return salarioMensal;
    }
    
    // Calcula o valor proporcional
    const valorDia = salarioMensal / diasNoMes;
    const salarioProporcional = valorDia * diasTrabalhados;
    
    console.log(`Dias no mês: ${diasNoMes}`);
    console.log(`Dias trabalhados: ${diasTrabalhados} (do dia ${entrada.getDate()} ao ${ultimoDiaMes.getDate()})`);
    console.log(`Valor por dia: R$ ${valorDia.toFixed(2)}`);
    console.log(`Salário proporcional: R$ ${salarioProporcional.toFixed(2)}`);
    
    // Arredonda para 2 casas decimais
    const valorFinal = Math.round(salarioProporcional * 100) / 100;
    console.log(`Valor final arredondado: R$ ${valorFinal.toFixed(2)}`);
    console.log('--- Fim do cálculo de salário proporcional ---\n');
    
    return valorFinal;
  } catch (error) {
    console.error('Erro ao calcular salário proporcional:', error);
    return salarioMensal; // Em caso de erro, retorna o valor integral
  }
}

// Mapeamento de nomes de cores para códigos HEX
const COLOR_MAP: Record<string, string> = {
  'red': '#E53E3E',    // Vermelho
  'blue': '#3182CE',   // Azul
  'purple': '#805AD5', // Roxo
  'green': '#38A169',  // Verde
  'yellow': '#D69E2E', // Amarelo
  'pink': '#D53F8C',  // Rosa
  'orange': '#DD6B20', // Laranja
  'teal': '#319795',   // Verde-água
  'gray': '#718096',   // Cinza
  'black': '#1A202C'   // Preto
};

// Função auxiliar para converter cor (HEX ou nome) para RGB
function hexToRgb(color: string | undefined | null) {
  // Se não houver cor definida ou for inválida, retorna azul padrão
  if (!color || typeof color !== 'string') {
    console.warn('Cor inválida, usando azul padrão');
    return { r: 41, g: 128, b: 185 };
  }
  
  try {
    // Verifica se é um nome de cor conhecido
    const hexColor = COLOR_MAP[color.toLowerCase()] || color;
    
    // Remove o # se presente
    const hexClean = hexColor.replace(/^#/, '');
    
    // Garante que o valor tem 6 caracteres
    const hexValid = hexClean.length === 3 
      ? hexClean.split('').map(c => c + c).join('') 
      : hexClean;
    
    // Converte para RGB
    const r = parseInt(hexValid.substring(0, 2), 16) || 0;
    const g = parseInt(hexValid.substring(2, 4), 16) || 0;
    const b = parseInt(hexValid.substring(4, 6), 16) || 0;
    
    // Valida os valores
    if (isNaN(r) || isNaN(g) || isNaN(b)) {
      throw new Error('Valores RGB inválidos');
    }
    
    return { r, g, b };
  } catch (error) {
    console.error('Erro ao converter cor:', color, error);
    return { r: 41, g: 128, b: 185 }; // Retorna azul padrão em caso de erro
  }
}

interface AccountsPayableGeneralReportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

// Interface for jsPDF with autoTable
interface jsPDFWithAutoTable extends Omit<jsPDF, 'internal'> {
  autoTable: typeof autoTable;
  lastAutoTable?: any; // Adicionado para resolver o erro de tipagem
  internal: {
    getNumberOfPages: () => number;
    pageSize: {
      width: number;
      height: number;
      getWidth: () => number;
      getHeight: () => number;
    };
    scaleFactor: number;
    pages: number[];
    events: any;
    getEncryptor: (objectId: number) => (data: string) => string;
  };
}

export function AccountsPayableGeneralReportDialog({
  open,
  onOpenChange
}: AccountsPayableGeneralReportDialogProps) {
  const [department, setDepartment] = useState("todos");
  const [category, setCategory] = useState("todos");
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [loading, setLoading] = useState(false);
  const clubId = useCurrentClubId();

  const handleGenerate = async () => {
    if (!clubId) {
      toast({
        title: "Erro",
        description: "ID do clube não encontrado.",
        variant: "destructive",
      });
      return;
    }

    if (startDate && endDate && startDate > endDate) {
      toast({
        title: "Erro",
        description: "A data de início deve ser anterior à data de fim.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);

    try {
      // Get club info
      const clubInfo = await getClubInfo(clubId);

      // Get all pending payables
      let allPayables = await getAllPendingPayables(clubId);

      // Apply date filter if dates are selected
      if (startDate || endDate) {
        allPayables = allPayables.filter(payable => {
          // Usar a transaction_date (que contém a data de compra) para filtrar
          const payableDate = payable.transaction_date ? new Date(payable.transaction_date) : null;
          if (!payableDate) return false;
          
          // Ajustar as datas para comparar apenas o dia, mês e ano
          const start = startDate ? new Date(startDate.setHours(0, 0, 0, 0)) : null;
          const end = endDate ? new Date(endDate.setHours(23, 59, 59, 999)) : null;
          const dateToCheck = new Date(payableDate.setHours(12, 0, 0, 0));
          
          const isAfterStart = !start || dateToCheck >= start;
          const isBeforeEnd = !end || dateToCheck <= end;
          
          return isAfterStart && isBeforeEnd;
        });
      }

      // Apply other filters
      let filteredPayables = allPayables;

      if (department !== "todos") {
        filteredPayables = filteredPayables.filter(p => p.department === department);
      }

      if (category !== "todos") {
        filteredPayables = filteredPayables.filter(p => p.category === category);
      }

      // Primeiro, normalizar os nomes dos departamentos
      filteredPayables = filteredPayables.map(payable => {
        if (payable.department === 'Comissão Técnica' || payable.department === 'Vales - Comissão Técnica') {
          return { ...payable, department: 'Colaboradores' };
        }
        return payable;
      });

      // Separar contas de fornecedores das demais
      const supplierPayables = filteredPayables.filter(p => p.department === 'Fornecedores');
      const otherPayables = filteredPayables.filter(p => p.department !== 'Fornecedores');

      // Agrupar fornecedores por nome
      const payablesBySupplier: Record<string, ConsolidatedPayable[]> = {};
      supplierPayables.forEach(payable => {
        const supplierName = payable.name || 'Fornecedor não identificado';
        if (!payablesBySupplier[supplierName]) {
          payablesBySupplier[supplierName] = [];
        }
        payablesBySupplier[supplierName].push(payable);
      });

      // Agrupar outras contas por departamento
      const payablesByDepartment: Record<string, ConsolidatedPayable[]> = {};
      otherPayables.forEach(payable => {
        const dept = payable.department || 'Outros';
        if (!payablesByDepartment[dept]) {
          payablesByDepartment[dept] = [];
        }
        payablesByDepartment[dept].push(payable);
      });

      // Create PDF
      const doc = new jsPDF("portrait", "mm", "a4") as jsPDFWithAutoTable;

      // Configurações de layout
      const pageWidth = doc.internal.pageSize.width;
      const margin = 15;
      const logoSize = 20;
      
      // Adiciona o título à esquerda
      doc.setFontSize(18);
      const title = "Relatório Geral de Contas a Pagar";
      doc.text(title, margin, 20);
      
      // Adiciona as informações do clube abaixo do título
      doc.setFontSize(12);
      let currentY = 30; // Posição Y inicial após o título
      
      doc.text(clubInfo.name, margin, currentY);
      currentY += 5;
      
      if (clubInfo.address) {
        doc.setFontSize(10);
        doc.text(clubInfo.address, margin, currentY);
        currentY += 5;
      }
      
      if (clubInfo.phone) {
        doc.text(`Telefone: ${clubInfo.phone}`, margin, currentY);
        currentY += 5;
      }
      
      // Adiciona o logo à direita, alinhado com o título
      if (clubInfo.logo_url) {
        try {
          const logoResponse = await fetch(clubInfo.logo_url);
          const logoBlob = await logoResponse.blob();
          const logoUrl = URL.createObjectURL(logoBlob);
          
          // Posiciona o logo à direita, alinhado com o título
          const logoX = pageWidth - margin - logoSize;
          doc.addImage(logoUrl, 'JPEG', logoX, 10, logoSize, logoSize);
        } catch (error) {
          console.error("Erro ao carregar logo:", error);
          // Se falhar, continua sem o logo
        }
      }

      // Add generation date and period
      doc.setFontSize(10);
      const startY = currentY + 5; // Usa a posição atual + 5mm de margem
      doc.text(`Gerado em: ${format(new Date(), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR })}`, 15, startY);
      
      // Adiciona o período do relatório, se as datas estiverem definidas
      if (startDate || endDate) {
        const periodText = `Período: ${startDate ? format(startDate, "dd/MM/yyyy") : 'Início'} a ${endDate ? format(endDate, "dd/MM/yyyy") : 'Fim'}`;
        doc.text(periodText, 15, startY + 5);
        currentY = startY + 15; // Ajusta o espaçamento
      } else {
        currentY = startY + 10; // Atualiza a posição Y para o conteúdo seguinte
      }

      // Add summary
      const totalAmount = filteredPayables.reduce((sum, p) => sum + p.amount, 0);
      const totalCount = filteredPayables.length;

      doc.setFontSize(12);
      doc.text("Resumo Geral:", 15, currentY);
      currentY += 8;

      doc.setFontSize(10);
      doc.text(`Total de Contas: ${totalCount}`, 15, currentY);
      currentY += 5;
      doc.text(`Valor Total: R$ ${totalAmount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`, 15, currentY);
      currentY += 15;

      // Definir cores do tema uma única vez
      const primaryColor = clubInfo.primary_color ? 
        hexToRgb(clubInfo.primary_color) : {r: 41, g: 128, b: 185};
      const secondaryColor = clubInfo.secondary_color ? 
        hexToRgb(clubInfo.secondary_color) : 
        {r: Math.max(0, primaryColor.r - 30), g: Math.max(0, primaryColor.g - 30), b: Math.max(0, primaryColor.b - 30)};

      // 1. Processar fornecedores (cada um em sua própria tabela)
      const sortedSuppliers = Object.keys(payablesBySupplier).sort();
      if (sortedSuppliers.length > 0) {
        for (const supplierName of sortedSuppliers) {
          const supplierPayablesList = payablesBySupplier[supplierName];
          const supplierTotal = supplierPayablesList.reduce((sum, p) => sum + p.amount, 0);

          // Cabeçalho do fornecedor
          doc.setFontSize(12);
          doc.setFont('helvetica', 'bold');
          doc.setFillColor(primaryColor.r, primaryColor.g, primaryColor.b);
          doc.rect(margin, currentY, pageWidth - (2 * margin), 8, 'F');
          doc.setTextColor(255, 255, 255);
          const supplierHeaderText = `${supplierName} (${supplierPayablesList.length} itens - R$ ${supplierTotal.toLocaleString('pt-BR', { minimumFractionDigits: 2 })})`;
          doc.text(supplierHeaderText, margin + 2, currentY + 5);
          doc.setTextColor(0, 0, 0);
          currentY += 12;

          // Tabela de itens do fornecedor
          const tableData = supplierPayablesList.map(item => {
            // Para fornecedores, usar a data de compra (transaction_date) como data de entrada
            // Para jogadores/colaboradores, priorizar a data de entrada (entry_date)
            // Se não houver, usar a data da transação ou data de vencimento como fallback
            let entryDate = item.entry_date; // Já incluímos entry_date para jogadores/colaboradores
            
            // Se não tiver entry_date ou for um fornecedor, usar transaction_date
            if (!entryDate || item.type === 'account') {
              entryDate = item.transaction_date || item.due_date;
            }
            
            return [
              item.name,
              item.description,
              entryDate ? format(new Date(entryDate), "dd/MM/yyyy", { locale: ptBR }) : 'N/A',
              item.pix_key || 'N/A',
              item.role || '-',
              `R$ ${item.amount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`
            ];
          });

          autoTable(doc, {
            head: [["Nome", "Descrição", "Entrada", "Chave PIX", "Função", "Valor"]],
            body: tableData,
            startY: currentY,
            styles: { fontSize: 8, cellPadding: 2 },
            headStyles: {
              fillColor: [secondaryColor.r, secondaryColor.g, secondaryColor.b],
              textColor: 255,
              fontStyle: 'bold',
              halign: 'center'
            },
            alternateRowStyles: { fillColor: [248, 248, 248] },
            columnStyles: { 5: { halign: 'right' } },
            margin: { left: 15, right: 15 },
            didDrawPage: (data: any) => {
              currentY = data.cursor?.y || currentY + 10;
            }
          });
          
          currentY += 15;

          if (currentY > 250) {
            doc.addPage();
            currentY = 20;
          }
        }
      }

      // 2. Processar os demais departamentos
      const sortedDepartments = Object.keys(payablesByDepartment).sort();
      if (sortedDepartments.length > 0) {
        for (const dept of sortedDepartments) {
          const deptPayables = payablesByDepartment[dept];
          
          // Calcula o total considerando os valores proporcionais
          let deptTotal = 0;
          const processedPayables = deptPayables.map(item => {
            // Para jogadores/colaboradores, priorizar a data de entrada (entry_date)
            const entryDate = item.entry_date || item.transaction_date || item.due_date;
            let valor = item.amount;

            if (item.type === 'salary' && entryDate) {
              const dataReferencia = item.transaction_date 
                ? new Date(item.transaction_date) 
                : new Date();
                
              const dataEntrada = new Date(entryDate);
              dataEntrada.setHours(12, 0, 0, 0);
              
              if (dataEntrada <= dataReferencia) {
                valor = calcularSalarioProporcional(entryDate, item.amount, dataReferencia);
              }
            }
            
            deptTotal += valor;
            return { ...item, valorCalculado: valor };
          });

          // Cabeçalho do departamento
          doc.setFontSize(12);
          doc.setFont('helvetica', 'bold');
          doc.setFillColor(primaryColor.r, primaryColor.g, primaryColor.b);
          doc.rect(margin, currentY, pageWidth - (2 * margin), 8, 'F');
          doc.setTextColor(255, 255, 255);
          const deptHeaderText = `${dept} (${deptPayables.length} itens - R$ ${deptTotal.toLocaleString('pt-BR', { minimumFractionDigits: 2 })})`;
          doc.text(deptHeaderText, margin + 2, currentY + 5);
          doc.setTextColor(0, 0, 0);
          currentY += 12;

          // A função calcularSalarioProporcional foi movida para o início do arquivo

          // Tabela de itens do departamento (usando os valores já calculados)
          const tableData = processedPayables.map(item => {
            // Para jogadores/colaboradores, priorizar a data de entrada (entry_date)
            // Se não houver, usar a data da transação ou data de vencimento como fallback
            const entryDate = item.entry_date || item.transaction_date || item.due_date;

            const transactionDate = item.transaction_date ? 
              format(new Date(item.transaction_date), "dd/MM/yyyy", { locale: ptBR }) : 'N/A';

            // Usa o valor já calculado anteriormente para manter a consistência com o total
            const valor = item.valorCalculado || item.amount;
            
            // Para salários, mostramos o valor integral e o valor proporcional
            if (item.type === 'salary') {
              return [
                item.name,
                item.description,
                entryDate ? format(new Date(entryDate), "dd/MM/yyyy", { locale: ptBR }) : 'N/A',
                transactionDate,
                item.pix_key || 'N/A',
                item.role || '-',
                `R$ ${item.amount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`,
                `R$ ${valor.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`
              ];
            }
            
            // Para outros itens, mostramos apenas o valor normal
            return [
              item.name,
              item.description,
              entryDate ? format(new Date(entryDate), "dd/MM/yyyy", { locale: ptBR }) : 'N/A',
              transactionDate,
              item.pix_key || 'N/A',
              item.role || '-',
              '-',
              `R$ ${valor.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`
            ];
          });

          // Define os cabeçalhos da tabela com a nova coluna
          const headers = [
            "Nome",
            "Descrição",
            "Entrada",
            "Transação",
            "Chave PIX",
            "Função",
            "Salário",
            "Valor"
          ];

          // Configura as larguras das colunas
          const columnStyles = {
            0: { cellWidth: 'auto' },  // Nome
            1: { cellWidth: 'auto' },  // Descrição
            2: { cellWidth: 'auto' },  // Entrada
            3: { cellWidth: 'auto' },  // Transação
            4: { cellWidth: 'auto' },  // Chave PIX
            5: { cellWidth: 'auto' },  // Função
            6: { cellWidth: 'auto' },  // Salário
            7: { cellWidth: 'auto' }   // Valor a Receber
          };

          autoTable(doc, {
            head: [headers],
            body: tableData,
            columnStyles: columnStyles,
            startY: currentY,
            styles: { fontSize: 8, cellPadding: 2 },
            headStyles: {
              fillColor: [secondaryColor.r, secondaryColor.g, secondaryColor.b],
              textColor: 255,
              fontStyle: 'bold',
              halign: 'center'
            },
            alternateRowStyles: { fillColor: [248, 248, 248] },
            columnStyles: { 6: { halign: 'right' } },
            margin: { left: 15, right: 15 },
            didDrawPage: (data: any) => {
              currentY = data.cursor?.y || currentY + 10;
            }
          });

          currentY = (doc as any).lastAutoTable.finalY + 10;

          if (currentY > 250) {
            doc.addPage();
            currentY = 20;
          }
        }
      }

      // Add page numbers
      const pageCount = doc.internal.getNumberOfPages();
      for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i);
        doc.setFontSize(8);
        doc.text(`Página ${i} de ${pageCount}`, pageWidth / 2, doc.internal.pageSize.height - 10, { align: "center" });
      }

      // Save the PDF
      const reportTitle = `Contas_a_Pagar_Geral_${format(new Date(), "dd-MM-yyyy")}.pdf`;
      doc.save(reportTitle);

      toast({
        title: "Relatório gerado",
        description: "O relatório geral de contas a pagar foi gerado com sucesso.",
      });

      onOpenChange(false);
    } catch (error) {
      console.error("Erro ao gerar relatório:", error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao gerar o relatório.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Get unique departments and categories for filters
  const [payables, setPayables] = useState<ConsolidatedPayable[]>([]);

  // Load payables for filter options
  React.useEffect(() => {
    if (open && clubId) {
      getAllPendingPayables(clubId).then(setPayables).catch(console.error);
    }
  }, [open, clubId]);

  const uniqueDepartments = Array.from(new Set(payables.map(p => p.department).filter(Boolean)));
  const uniqueCategories = Array.from(new Set(payables.map(p => p.category).filter(Boolean)));

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Relatório Geral de Contas a Pagar</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="grid grid-cols-1 gap-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>Data de Início</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !startDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {startDate ? format(startDate, "dd/MM/yyyy", { locale: ptBR }) : "Selecione a data"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={startDate || undefined}
                      onSelect={(date) => date && setStartDate(date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div>
                <Label>Data de Fim</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !endDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {endDate ? format(endDate, "dd/MM/yyyy", { locale: ptBR }) : "Selecione a data"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={endDate || undefined}
                      onSelect={(date) => date && setEndDate(date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <div>
              <Label htmlFor="department">Departamento</Label>
              <Select value={department} onValueChange={setDepartment}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione um departamento" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="todos">Todos os Departamentos</SelectItem>
                  {uniqueDepartments.map((dept) => (
                    <SelectItem key={dept} value={dept}>
                      {dept}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="category">Categoria</Label>
              <Select value={category} onValueChange={setCategory}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione uma categoria" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="todos">Todas as Categorias</SelectItem>
                  {uniqueCategories.map((cat) => (
                    <SelectItem key={cat} value={cat}>
                      {cat}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleGenerate} disabled={loading}>
            {loading ? "Gerando..." : "Gerar Relatório"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
