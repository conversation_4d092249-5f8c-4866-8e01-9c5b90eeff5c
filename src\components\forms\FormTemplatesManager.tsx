import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { useFormTemplatesStore } from "@/store/useFormTemplatesStore";
import { ClubFormTemplate } from "@/api/clubFormTemplates";
import {
  Plus,
  Edit,
  Trash2,
  Eye,
  Download,
  Power,
  PowerOff,
  FileText
} from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { FormTemplateDialog } from "./FormTemplateDialogNew";
import { FormTemplatePreviewDialog } from "./FormTemplatePreviewDialogNew";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";

const FORM_TYPE_LABELS = {
  pre_registration: "Pré-cadastro",
  housing: "Moradia",
  liability_waiver_minor: "Termo de Responsabilidade - Menor de 18",
  liability_waiver_adult: "Termo de Responsabilidade - Maior de 18",
  liability_waiver: "Termo de Responsabilidade",
  custom: "Personalizado"
};

export function FormTemplatesManager() {
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const { toast } = useToast();

  const {
    templates,
    loading,
    error,
    fetchTemplates,
    removeTemplate,
    toggleStatus,
    generatePDFHtml
  } = useFormTemplatesStore();

  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showPreviewDialog, setShowPreviewDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [selectedTemplateLocal, setSelectedTemplateLocal] = useState<ClubFormTemplate | null>(null);

  useEffect(() => {
    if (clubId && user?.id) {
      fetchTemplates(clubId, user.id);
    }
  }, [clubId, user?.id, fetchTemplates]);

  const handleEdit = (template: ClubFormTemplate) => {
    setSelectedTemplateLocal(template);
    setShowEditDialog(true);
  };

  const handlePreview = (template: ClubFormTemplate) => {
    setSelectedTemplateLocal(template);
    setShowPreviewDialog(true);
  };

  const handleDelete = (template: ClubFormTemplate) => {
    setSelectedTemplateLocal(template);
    setShowDeleteDialog(true);
  };

  const confirmDelete = async () => {
    if (!selectedTemplateLocal || !user?.id) return;

    try {
      await removeTemplate(clubId, selectedTemplateLocal.id, user.id);
      toast({
        title: "Template excluído",
        description: "O template foi excluído com sucesso.",
      });
      setShowDeleteDialog(false);
      setSelectedTemplateLocal(null);
    } catch (err) {
      toast({
        title: "Erro ao excluir template",
        description: "Ocorreu um erro ao excluir o template.",
        variant: "destructive",
      });
    }
  };

  const handleToggleStatus = async (template: ClubFormTemplate) => {
    if (!user?.id) return;

    try {
      await toggleStatus(clubId, template.id, user.id);
      toast({
        title: "Status alterado",
        description: `Template ${template.is_active ? 'desativado' : 'ativado'} com sucesso.`,
      });
    } catch (err) {
      toast({
        title: "Erro ao alterar status",
        description: "Ocorreu um erro ao alterar o status do template.",
        variant: "destructive",
      });
    }
  };

  const handleDownloadPDF = async (template: ClubFormTemplate) => {
    try {
      const pdfBlob = await generatePDFHtml(template, clubId);
      const url = URL.createObjectURL(pdfBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${template.name.replace(/\s+/g, '_')}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast({
        title: "PDF gerado",
        description: "O PDF foi gerado e baixado com sucesso.",
      });
    } catch (err) {
      toast({
        title: "Erro ao gerar PDF",
        description: "Ocorreu um erro ao gerar o PDF.",
        variant: "destructive",
      });
    }
  };

  const onDialogSuccess = () => {
    setShowCreateDialog(false);
    setShowEditDialog(false);
    setSelectedTemplateLocal(null);
    if (clubId && user?.id) {
      fetchTemplates(clubId, user.id);
    }
  };

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            Erro ao carregar templates: {error}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Gerenciar Fichas</CardTitle>
              <CardDescription>
                Crie e gerencie fichas personalizadas para o pré-cadastro de atletas
              </CardDescription>
            </div>
            <Button onClick={() => setShowCreateDialog(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Nova Ficha
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">Carregando templates...</div>
          ) : templates.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Nenhuma ficha encontrada</p>
              <p className="text-sm">Clique em "Nova Ficha" para criar sua primeira ficha personalizada</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nome</TableHead>
                  <TableHead>Tipo</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Criado em</TableHead>
                  <TableHead>Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {templates.map((template) => (
                  <TableRow key={template.id}>
                    <TableCell className="font-medium">
                      {template.name}
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {FORM_TYPE_LABELS[template.form_type]}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant={template.is_active ? "default" : "secondary"}>
                        {template.is_active ? "Ativo" : "Inativo"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {new Date(template.created_at).toLocaleDateString('pt-BR')}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            Ações
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                          <DropdownMenuItem onClick={() => handlePreview(template)}>
                            <Eye className="h-4 w-4 mr-2" />
                            Visualizar
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleEdit(template)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Editar
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleDownloadPDF(template)}>
                            <Download className="h-4 w-4 mr-2" />
                            Baixar PDF
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleToggleStatus(template)}>
                            {template.is_active ? (
                              <>
                                <PowerOff className="h-4 w-4 mr-2" />
                                Desativar
                              </>
                            ) : (
                              <>
                                <Power className="h-4 w-4 mr-2" />
                                Ativar
                              </>
                            )}
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleDelete(template)}
                            className="text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Excluir
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Dialogs */}
      <FormTemplateDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        onSuccess={onDialogSuccess}
      />

      <FormTemplateDialog
        open={showEditDialog}
        onOpenChange={setShowEditDialog}
        template={selectedTemplateLocal}
        onSuccess={onDialogSuccess}
      />

      <FormTemplatePreviewDialog
        open={showPreviewDialog}
        onOpenChange={setShowPreviewDialog}
        template={selectedTemplateLocal}
      />

      <ConfirmDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        title="Excluir Template"
        description={`Tem certeza que deseja excluir o template "${selectedTemplateLocal?.name}"? Esta ação não pode ser desfeita.`}
        onConfirm={confirmDelete}
      />
    </div>
  );
}
