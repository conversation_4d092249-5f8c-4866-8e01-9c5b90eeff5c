import { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { X } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

// Estilo personalizado para o footer do diálogo
const CustomDialogFooter = ({ children }: { children: React.ReactNode }) => (
  <div className="flex justify-end gap-4 mt-4">{children}</div>
);
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/components/ui/use-toast";
import {
  Plus,
  ChevronLeft,
  Utensils,
  MapPin,
  Clock,
  Users,
  Edit,
  Trash2,
  FileText,
  Calendar
} from "lucide-react";
import { useCurrentClubId } from "@/context/ClubContext";
import { getClubInfo } from "@/api/api";
import { Search } from "lucide-react";
import {
  getMealTypes,
  getMealSessions,
  createMealType,
  createMealSession,
  updateMealType,
  updateMealSession,
  deleteMealType,
  deleteMealSession,
  MealType,
  MealSessionWithDetails
} from "@/api/meals";
import {
  MealLocation,
  getMealLocations,
  createMealLocation,
  updateMealLocation,
  deleteMealLocation
} from "@/api/mealLocations";

export default function Alimentacao() {
  const navigate = useNavigate();
  const clubId = useCurrentClubId();

  // Função para navegar para os detalhes da sessão
  const handleSessionClick = (sessionId: number) => {
    navigate(`/alimentacao-sessao/${sessionId}`);
  };

  // Função para baixar relatório (placeholder)
  const handleDownloadReport = (e: React.MouseEvent, sessionId: number) => {
    e.stopPropagation(); // Evita que o clique na linha seja acionado
    // TODO: Implementar lógica para baixar o relatório
    toast({
      title: "Relatório",
      description: `Gerando relatório para a sessão ${sessionId}...`,
    });
  };

  // Estados principais
  const [mealTypes, setMealTypes] = useState<MealType[]>([]);
  const [mealLocations, setMealLocations] = useState<MealLocation[]>([]);
  const [mealSessions, setMealSessions] = useState<MealSessionWithDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  
  // Filtrar sessões com base no termo de pesquisa
  const filteredSessions = mealSessions.filter(session => {
    const term = searchTerm.toLowerCase();
    const nameMatch = session.name?.toLowerCase().includes(term);
    const notesMatch = session.notes?.toLowerCase().includes(term);
    const typeMatch = session.meal_types?.some(t => t.name.toLowerCase().includes(term));
    return nameMatch || notesMatch || typeMatch;
  });

  // Estados dos diálogos
  const [isMealTypeDialogOpen, setIsMealTypeDialogOpen] = useState(false);
  const [isMealSessionDialogOpen, setIsMealSessionDialogOpen] = useState(false);
  const [isEditMealSessionDialogOpen, setIsEditMealSessionDialogOpen] = useState(false);
  const [isEditMealTypeDialogOpen, setIsEditMealTypeDialogOpen] = useState(false);
  const [isLocationDialogOpen, setIsLocationDialogOpen] = useState(false);

  // Estados dos formulários
  const [mealTypeForm, setMealTypeForm] = useState({
    name: "",
  });
  const [locationForm, setLocationForm] = useState({
    name: "",
    address: "",
    number: "",
  });

  const [mealSessionForm, setMealSessionForm] = useState({
    name: "",
    date: new Date().toISOString().split("T")[0],
    notes: "",
    items: [
      { location_id: "", meal_type_id: "", start_time: "", end_time: "" }
    ] as { location_id: string; meal_type_id: string; start_time: string; end_time: string }[],
  });

  const [editingMealType, setEditingMealType] = useState<MealType | null>(null);
  const [editingLocation, setEditingLocation] = useState<MealLocation | null>(null);
  const [isEditLocationDialogOpen, setIsEditLocationDialogOpen] = useState(false);
  const [editingMealSession, setEditingMealSession] = useState<MealSessionWithDetails | null>(null);

  // Função para carregar dados
  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const [mealTypesData, sessionsData, locationsData] = await Promise.all([
        getMealTypes(clubId),
        getMealSessions(clubId),
        getMealLocations(clubId)
      ]);

      setMealTypes(mealTypesData);
      setMealSessions(sessionsData);
      setMealLocations(locationsData);
      setLoading(false);
    } catch (error) {
      console.error("Erro ao carregar dados:", error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar os dados de alimentação.",
        variant: "destructive",
      });
      setLoading(false);
    }
  }, [clubId]);

  // Carregar dados quando o componente montar
  useEffect(() => {
    if (clubId) {
      fetchData();
    }
  }, [clubId, fetchData]);

  // Função para criar um novo tipo de refeição
  const handleCreateMealType = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      // Verifica se o nome foi preenchido
      if (!mealTypeForm.name.trim()) {
        toast({
          title: "Erro",
          description: "O nome do tipo de refeição é obrigatório.",
          variant: "destructive" as any,
        });
        return;
      }

      const newMealType = await createMealType(
        clubId,
        {
          name: mealTypeForm.name.trim(),
          club_id: clubId,
        } as any
      );
      
      setMealTypes([...mealTypes, newMealType]);
      setMealTypeForm({ name: "" });
      setIsMealTypeDialogOpen(false);
      
      toast({
        title: "Sucesso",
        description: "Tipo de refeição criado com sucesso!",
        variant: "default" as any,
      });
    } catch (error) {
      console.error("Erro ao criar tipo de refeição:", error);
      toast({
        title: "Erro",
        description: "Não foi possível criar o tipo de refeição.",
        variant: "destructive" as any,
      });
    }
  };

  // Função para criar uma nova sessão de refeição
  const handleCreateMealSession = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validação dos campos obrigatórios
    if (!mealSessionForm.name.trim()) {
      toast({
        title: "Erro",
        description: "Informe um nome para a sessão.",
        variant: "destructive" as any,
      });
      return;
    }

    if (mealSessionForm.items.length === 0) {
      toast({
        title: "Erro",
        description: "Adicione pelo menos um local e tipo de refeição.",
        variant: "destructive" as any,
      });
      return;
    }
    for (const item of mealSessionForm.items) {
      if (!item.location_id || !item.meal_type_id || !item.start_time || !item.end_time) {
        toast({
          title: "Erro",
          description: "Preencha todas as informações de local e horário.",
          variant: "destructive" as any,
        });
        return;
      }
    }

    if (!mealSessionForm.date) {
      toast({
        title: "Erro",
        description: "A data é obrigatória.",
        variant: "destructive" as any,
      });
      return;
    }


    try {
      const newMealSession = await createMealSession(
        clubId,
        {
          name: mealSessionForm.name,
          date: mealSessionForm.date,
          notes: mealSessionForm.notes || "",
          locations: mealSessionForm.items.map(it => ({
            location_id: parseInt(it.location_id),
            meal_type_id: parseInt(it.meal_type_id),
            start_time: it.start_time,
            end_time: it.end_time,
          }))
        } as any
      );
      
      setMealSessions([newMealSession, ...mealSessions]);
      setMealSessionForm({
        name: "",
        date: new Date().toISOString().split("T")[0],
        notes: "",
        items: [{ location_id: "", meal_type_id: "", start_time: "", end_time: "" }],
      });
      setIsMealSessionDialogOpen(false);
      
      toast({
        title: "Sucesso",
        description: "Sessão de refeição criada com sucesso!",
        variant: "default" as any,
      });
    } catch (error) {
      console.error("Erro ao criar sessão de refeição:", error);
      toast({
        title: "Erro",
        description: "Não foi possível criar a sessão de refeição.",
        variant: "destructive" as any,
      });
    }
  };

  const handleEditMealType = (mealType: MealType) => {
    setEditingMealType(mealType);
    setMealTypeForm({
      name: mealType.name
    });
    setIsEditMealTypeDialogOpen(true);
  };

  const handleUpdateMealType = async () => {
    if (!editingMealType || !mealTypeForm.name) {
      toast({
        title: "Erro",
        description: "O nome do tipo de refeição é obrigatório.",
        variant: "destructive",
      });
      return;
    }

    try {
      await updateMealType(clubId, editingMealType.id, mealTypeForm);
      toast({
        title: "Sucesso",
        description: "Tipo de refeição atualizado com sucesso.",
      });
      setIsEditMealTypeDialogOpen(false);
      setEditingMealType(null);
      setMealTypeForm({ name: "" });
      fetchData();
    } catch (error: any) {
      toast({
        title: "Erro",
        description: error.message || "Erro ao atualizar tipo de refeição.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteMealType = async (mealType: MealType) => {
    if (window.confirm(`Tem certeza que deseja excluir o tipo de refeição "${mealType.name}"?`)) {
      try {
        await deleteMealType(clubId, mealType.id);
        toast({
          title: "Sucesso",
          description: "Tipo de refeição excluído com sucesso.",
        });
        fetchData();
      } catch (error: any) {
        toast({
          title: "Erro",
          description: error.message || "Erro ao excluir tipo de refeição.",
          variant: "destructive",
        });
      }
    }
  };

  const handleEditLocation = (loc: MealLocation) => {
    setEditingLocation(loc);
    setLocationForm({
      name: loc.name,
      address: loc.address || "",
      number: loc.number || "",
    });
    setIsEditLocationDialogOpen(true);
  };

  const handleUpdateLocation = async () => {
    if (!editingLocation || !locationForm.name.trim()) {
      toast({ title: "Erro", description: "Informe o nome do local.", variant: "destructive" });
      return;
    }

    try {
      await updateMealLocation(clubId, editingLocation.id, {
        name: locationForm.name.trim(),
        address: locationForm.address.trim() || null,
        number: locationForm.number.trim() || null,
      });
      toast({ title: "Sucesso", description: "Local atualizado com sucesso." });
      setIsEditLocationDialogOpen(false);
      setEditingLocation(null);
      setLocationForm({ name: "", address: "", number: "" });
      fetchData();
    } catch (err: any) {
      toast({ title: "Erro", description: err.message || "Erro ao atualizar local.", variant: "destructive" });
    }
  };

  const handleDeleteLocation = async (loc: MealLocation) => {
    if (window.confirm(`Tem certeza que deseja excluir o local "${loc.name}"?`)) {
      try {
        await deleteMealLocation(clubId, loc.id);
        toast({ title: "Sucesso", description: "Local excluído com sucesso." });
        fetchData();
      } catch (err: any) {
        toast({ title: "Erro", description: err.message || "Erro ao excluir local.", variant: "destructive" });
      }
    }
  };

  const handleDeleteMealSession = async (session: MealSessionWithDetails) => {
    if (window.confirm(`Tem certeza que deseja excluir esta sessão de alimentação?`)) {
      try {
        await deleteMealSession(clubId, session.id);
        toast({
          title: "Sucesso",
          description: "Sessão de alimentação excluída com sucesso.",
        });

        // Recarregar sessões
        const sessionsData = await getMealSessions(clubId);
        setMealSessions(sessionsData);
      } catch (error: any) {
        toast({
          title: "Erro",
          description: error.message || "Erro ao excluir sessão de alimentação.",
          variant: "destructive",
        });
      }
    }
  };

  const handleEditMealSession = (session: MealSessionWithDetails) => {
    setEditingMealSession(session);
    setMealSessionForm({
      name: session.name || "",
      date: session.date,
      notes: session.notes || "",
      items: (session.meal_types || []).map(mt => ({
        location_id: mt.location_id?.toString() || "",
        meal_type_id: mt.id.toString(),
        start_time: mt.start_time || "",
        end_time: mt.end_time || "",
      })) as { location_id: string; meal_type_id: string; start_time: string; end_time: string }[],
    });
    setIsEditMealSessionDialogOpen(true);
  };

  const handleUpdateMealSession = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!editingMealSession) return;

    if (!mealSessionForm.name.trim()) {
      toast({
        title: "Erro",
        description: "Informe um nome para a sessão.",
        variant: "destructive" as any,
      });
      return;
    }

    if (mealSessionForm.items.length === 0) {
      toast({
        title: "Erro",
        description: "Adicione pelo menos um local e tipo de refeição.",
        variant: "destructive" as any,
      });
      return;
    }

    for (const item of mealSessionForm.items) {
      if (!item.location_id || !item.meal_type_id || !item.start_time || !item.end_time) {
        toast({
          title: "Erro",
          description: "Preencha todas as informações de local e horário.",
          variant: "destructive" as any,
        });
        return;
      }
    }

    if (!mealSessionForm.date) {
      toast({
        title: "Erro",
        description: "A data é obrigatória.",
        variant: "destructive" as any,
      });
      return;
    }

    try {
      await updateMealSession(
        clubId,
        editingMealSession.id,
        {
          name: mealSessionForm.name,
          date: mealSessionForm.date,
          notes: mealSessionForm.notes || "",
          locations: mealSessionForm.items.map(it => ({
            location_id: parseInt(it.location_id),
            meal_type_id: parseInt(it.meal_type_id),
            start_time: it.start_time,
            end_time: it.end_time,
          }))
        } as any
      );

      toast({
        title: "Sucesso",
        description: "Sessão de refeição atualizada com sucesso!",
      });

      setIsEditMealSessionDialogOpen(false);
      setEditingMealSession(null);
      setMealSessionForm({
        name: "",
        date: new Date().toISOString().split("T")[0],
        notes: "",
        items: [{ location_id: "", meal_type_id: "", start_time: "", end_time: "" }],
      });

      fetchData();
    } catch (error: any) {
      toast({
        title: "Erro",
        description: error.message || "Não foi possível atualizar a sessão de refeição.",
        variant: "destructive" as any,
      });
    }
  };

  const addItem = () => {
    setMealSessionForm(prev => ({
      ...prev,
      items: [...prev.items, { location_id: "", meal_type_id: "", start_time: "", end_time: "" }]
    }));
  };

  const updateItem = (index: number, updates: Partial<{ location_id: string; meal_type_id: string; start_time: string; end_time: string }>) => {
    setMealSessionForm(prev => {
      const items = [...prev.items];
      items[index] = { ...items[index], ...updates };
      return { ...prev, items };
    });
  };

  const removeItem = (index: number) => {
    setMealSessionForm(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index)
    }));
  };

  // Função para formatar data para exibição
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { day: "2-digit", month: "2-digit", year: "numeric" };
    return new Date(`${dateString}T00:00:00`).toLocaleDateString("pt-BR", options)
  };

  // Função para formatar hora para exibição
  const formatTime = (timeString: string) => {
    if (!timeString) return "";
    const [hours, minutes] = timeString.split(":");
    return `${hours}:${minutes}`;
  };

  // Renderização da tabela de sessões
  function renderSessionsTable() {
    return (
      <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Sessões de Refeição</h3>
        <Button onClick={() => setIsMealSessionDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Nova Sessão
        </Button>
      </div>

      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Nome da Sessão</TableHead>
              <TableHead>Data</TableHead>
              <TableHead>Hora</TableHead>
              <TableHead>Observações</TableHead>
              <TableHead className="text-right">Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredSessions.length > 0 ? (
              filteredSessions.map((session) => (
                <TableRow 
                  key={session.id} 
                  className="cursor-pointer hover:bg-gray-50"
                  onClick={() => handleSessionClick(session.id)}
                >
                  <TableCell className="font-medium">{session.name}</TableCell>
                  <TableCell>{formatDate(session.date)}</TableCell>
                  <TableCell>{formatTime(session.time)}</TableCell>
                  <TableCell className="truncate max-w-[200px]">{session.notes || "-"}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex space-x-1 justify-end">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDownloadReport(e, session.id);
                        }}
                        title="Baixar relatório"
                      >
                        <FileText className="h-4 w-4 text-blue-600" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEditMealSession(session);
                        }}
                        title="Editar sessão"
                      >
                        <Edit className="h-4 w-4 text-green-600" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteMealSession(session);
                        }}
                        title="Excluir sessão"
                      >
                        <Trash2 className="h-4 w-4 text-destructive" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={5} className="h-24 text-center">
                  Nenhuma sessão encontrada.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </Card>
    </div>
    );
  }

  const renderMealSessionDialog = () => (
    <Dialog open={isMealSessionDialogOpen} onOpenChange={setIsMealSessionDialogOpen}>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle>Nova Sessão de Refeição</DialogTitle>
          <DialogDescription>
            Preencha os dados da nova sessão de refeição.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleCreateMealSession}>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="name">Nome <span className="text-destructive">*</span></Label>
              <Input
                id="name"
                value={mealSessionForm.name}
                onChange={(e) =>
                  setMealSessionForm({ ...mealSessionForm, name: e.target.value })
                }
                required
              />
            </div>
            <div className="space-y-4 col-span-4">
              {mealSessionForm.items.map((item, index) => (
                <div key={index} className="grid grid-cols-5 gap-2 items-end">
                  <Select
                    value={item.location_id}
                    onValueChange={(val) => updateItem(index, { location_id: val })}
                    className="col-span-5"
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Local" />
                    </SelectTrigger>
                    <SelectContent>
                      {mealLocations.map((loc) => (
                        <SelectItem key={loc.id} value={loc.id.toString()}>{loc.name}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Select
                    value={item.meal_type_id}
                    onValueChange={(val) => updateItem(index, { meal_type_id: val })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Tipo" />
                    </SelectTrigger>
                    <SelectContent>
                      {mealTypes.map((t) => (
                        <SelectItem key={t.id} value={t.id.toString()}>{t.name}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Input
                    className="col-span-1"
                    type="time"
                    value={item.start_time}
                    onChange={(e) => updateItem(index, { start_time: e.target.value })}
                  />
                  <Input
                    className="col-span-1"
                    type="time"
                    value={item.end_time}
                    onChange={(e) => updateItem(index, { end_time: e.target.value })}
                  />
                  <Button type="button" size="icon" variant="ghost" onClick={() => removeItem(index)} className="col-span-1">
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
              <Button type="button" variant="outline" onClick={addItem}>Adicionar Local/Tipo</Button>
            </div>
            <div className="space-y-2">
              <Label htmlFor="date">Data <span className="text-destructive">*</span></Label>
              <Input
                id="date"
                type="date"
                value={mealSessionForm.date}
                onChange={(e) =>
                  setMealSessionForm({ ...mealSessionForm, date: e.target.value })
                }
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="notes">Observações</Label>
              <Textarea
                id="notes"
                value={mealSessionForm.notes}
                onChange={(e) =>
                  setMealSessionForm({ ...mealSessionForm, notes: e.target.value })
                }
                placeholder="Informações adicionais sobre a refeição"
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsMealSessionDialogOpen(false)}
            >
              Cancelar
            </Button>
            <Button type="submit">Salvar</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );

  const renderEditMealSessionDialog = () => (
    <Dialog open={isEditMealSessionDialogOpen} onOpenChange={setIsEditMealSessionDialogOpen}>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle>Editar Sessão de Refeição</DialogTitle>
          <DialogDescription>
            Atualize os dados da sessão de refeição.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleUpdateMealSession}>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-name">Nome <span className="text-destructive">*</span></Label>
              <Input
                id="edit-name"
                value={mealSessionForm.name}
                onChange={(e) =>
                  setMealSessionForm({ ...mealSessionForm, name: e.target.value })
                }
                required
              />
            </div>
            <div className="space-y-4 col-span-4">
              {mealSessionForm.items.map((item, index) => (
                <div key={index} className="grid grid-cols-5 gap-2 items-end">
                  <Select
                    value={item.location_id}
                    onValueChange={(val) => updateItem(index, { location_id: val })}
                    className="col-span-5"
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Local" />
                    </SelectTrigger>
                    <SelectContent>
                      {mealLocations.map((loc) => (
                        <SelectItem key={loc.id} value={loc.id.toString()}>{loc.name}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Select
                    value={item.meal_type_id}
                    onValueChange={(val) => updateItem(index, { meal_type_id: val })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Tipo" />
                    </SelectTrigger>
                    <SelectContent>
                      {mealTypes.map((t) => (
                        <SelectItem key={t.id} value={t.id.toString()}>{t.name}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Input
                    className="col-span-1"
                    type="time"
                    value={item.start_time}
                    onChange={(e) => updateItem(index, { start_time: e.target.value })}
                  />
                  <Input
                    className="col-span-1"
                    type="time"
                    value={item.end_time}
                    onChange={(e) => updateItem(index, { end_time: e.target.value })}
                  />
                  <Button type="button" size="icon" variant="ghost" onClick={() => removeItem(index)} className="col-span-1">
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
              <Button type="button" variant="outline" onClick={addItem}>Adicionar Local/Tipo</Button>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-date">Data <span className="text-destructive">*</span></Label>
              <Input
                id="edit-date"
                type="date"
                value={mealSessionForm.date}
                onChange={(e) =>
                  setMealSessionForm({ ...mealSessionForm, date: e.target.value })
                }
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-notes">Observações</Label>
              <Textarea
                id="edit-notes"
                value={mealSessionForm.notes}
                onChange={(e) =>
                  setMealSessionForm({ ...mealSessionForm, notes: e.target.value })
                }
                placeholder="Informações adicionais sobre a refeição"
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsEditMealSessionDialogOpen(false)}
            >
              Cancelar
            </Button>
            <Button type="submit">Salvar</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );

  const renderMealTypeDialog = () => (
    <Dialog open={isMealTypeDialogOpen} onOpenChange={setIsMealTypeDialogOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">
          <Utensils className="h-4 w-4 mr-1" />
          Novo Tipo de Refeição
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>Novo Tipo de Refeição</DialogTitle>
          <DialogDescription>
            Adicione um novo tipo de refeição para organizar as sessões de alimentação.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleCreateMealType}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Nome <span className="text-destructive">*</span>
              </Label>
              <Input
                id="name"
                name="name"
                value={mealTypeForm.name}
                onChange={(e) => setMealTypeForm({...mealTypeForm, name: e.target.value})}
                className="col-span-3"
                required
              />
            </div>
            <div className="mt-4 space-y-2 col-span-4">
              {mealTypes.map((type) => (
                <div key={type.id} className="flex items-center justify-between border p-2 rounded">
                  <span>{type.name}</span>
                  <div className="space-x-1">
                    <Button
                      type="button"
                      size="icon"
                      variant="ghost"
                      onClick={() => handleEditMealType(type)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      type="button"
                      size="icon"
                      variant="ghost"
                      onClick={() => handleDeleteMealType(type)}
                    >
                      <Trash2 className="h-4 w-4 text-destructive" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setIsMealTypeDialogOpen(false)}>
              Cancelar
            </Button>
            <Button type="submit">Salvar</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );

  const renderEditMealTypeDialog = () => (
    <Dialog open={isEditMealTypeDialogOpen} onOpenChange={setIsEditMealTypeDialogOpen}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Editar Tipo de Refeição</DialogTitle>
        </DialogHeader>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            handleUpdateMealType();
          }}
        >
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-mealtype-name" className="text-right">Nome *</Label>
              <Input
                id="edit-mealtype-name"
                value={mealTypeForm.name}
                onChange={(e) => setMealTypeForm({ ...mealTypeForm, name: e.target.value })}
                className="col-span-3"
                required
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setIsEditMealTypeDialogOpen(false)}>
              Cancelar
            </Button>
            <Button type="submit">Salvar</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );

  const renderLocationDialog = () => (
    <Dialog open={isLocationDialogOpen} onOpenChange={setIsLocationDialogOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">
          <MapPin className="h-4 w-4 mr-1" />
          Novo Local
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Novo Local de Refeição</DialogTitle>
        </DialogHeader>
        <form
          onSubmit={async (e) => {
            e.preventDefault();
            try {
              if (!locationForm.name.trim()) {
                toast({ title: 'Erro', description: 'Informe o nome do local.', variant: 'destructive' });
                return;
              }
              await createMealLocation(clubId, {
                name: locationForm.name.trim(),
                address: locationForm.address.trim() || null,
                number: locationForm.number.trim() || null
              });
              const locs = await getMealLocations(clubId);
              setMealLocations(locs);
              setLocationForm({ name: '', address: '', number: '' });
              setIsLocationDialogOpen(false);
              toast({ title: 'Sucesso', description: 'Local criado com sucesso.' });
            } catch (err) {
              toast({ title: 'Erro', description: 'Não foi possível criar o local.', variant: 'destructive' });
            }
          }}
        >
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="loc-name">Nome *</Label>
              <Input id="loc-name" value={locationForm.name} onChange={(e) => setLocationForm({ ...locationForm, name: e.target.value })} required />
            </div>
            <div className="space-y-2">
              <Label htmlFor="loc-address">Endereço</Label>
              <Textarea id="loc-address" value={locationForm.address} onChange={(e) => setLocationForm({ ...locationForm, address: e.target.value })} />
            </div>
            <div className="space-y-2">
              <Label htmlFor="loc-number">Número</Label>
              <Input id="loc-number" value={locationForm.number} onChange={(e) => setLocationForm({ ...locationForm, number: e.target.value })} />
            </div>
            <div className="mt-4 space-y-2">
              {mealLocations.map(loc => (
                <div key={loc.id} className="flex items-center justify-between border p-2 rounded">
                  <span>{loc.name}</span>
                  <div className="space-x-1">
                    <Button type="button" size="icon" variant="ghost" onClick={() => handleEditLocation(loc)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button type="button" size="icon" variant="ghost" onClick={() => handleDeleteLocation(loc)}>
                      <Trash2 className="h-4 w-4 text-destructive" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setIsLocationDialogOpen(false)}>
              Cancelar
            </Button>
            <Button type="submit">Salvar</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );

  const renderEditLocationDialog = () => (
    <Dialog open={isEditLocationDialogOpen} onOpenChange={setIsEditLocationDialogOpen}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Editar Local</DialogTitle>
        </DialogHeader>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            handleUpdateLocation();
          }}
        >
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-loc-name">Nome *</Label>
              <Input id="edit-loc-name" value={locationForm.name} onChange={(e) => setLocationForm({ ...locationForm, name: e.target.value })} required />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-loc-address">Endereço</Label>
              <Textarea id="edit-loc-address" value={locationForm.address} onChange={(e) => setLocationForm({ ...locationForm, address: e.target.value })} />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-loc-number">Número</Label>
              <Input id="edit-loc-number" value={locationForm.number} onChange={(e) => setLocationForm({ ...locationForm, number: e.target.value })} />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setIsEditLocationDialogOpen(false)}>
              Cancelar
            </Button>
            <Button type="submit">Salvar</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Button variant="ghost" onClick={() => navigate(-1)} className="mr-2">
            <ChevronLeft className="h-4 w-4 mr-1" />
            Voltar
          </Button>
          <h1 className="text-2xl font-bold">Alimentação</h1>
        </div>
        <div className="flex gap-2">
          {renderLocationDialog()}
          {renderEditLocationDialog()}
          {renderMealTypeDialog()}
          {renderEditMealTypeDialog()}
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-start justify-between">
            <div>
              <CardTitle>Sessões de Alimentação</CardTitle>
            </div>
            {renderMealSessionDialog()}
            {renderEditMealSessionDialog()}
          </CardHeader>
          <CardContent>
            {renderSessionsTable()}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}