
import { useState } from "react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Calendar, Clock, MapPin, Users } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { PermissionControl } from "@/components/PermissionControl";
import { AGENDA_PERMISSIONS } from "@/constants/permissions";
import { AgendaEventEditDialog } from "@/components/agenda/AgendaEventEditDialog";
import type { AgendaEvent } from "@/api/api";
import { useAgendaStore } from "@/store/useAgendaStore";
import { useCurrentClubId } from "@/context/ClubContext";

interface AgendaEventListProps {
  events: (Omit<AgendaEvent, "date" | "endTime"> & { date: Date; endTime: Date })[];
}

const getEventTypeStyles = (type: string) => {
  const styles: Record<string, { color: string, bg: string, label: string }> = {
    treino: { color: "text-green-600", bg: "bg-green-50", label: "Treino" },
    jogo: { color: "text-primary", bg: "bg-primary/10", label: "Jogo" },
    reuniao: { color: "text-purple-600", bg: "bg-purple-50", label: "Reunião" },
    medico: { color: "text-red-600", bg: "bg-red-50", label: "Médico" },
    viagem: { color: "text-amber-600", bg: "bg-amber-50", label: "Viagem" },
    outro: { color: "text-gray-600", bg: "bg-gray-50", label: "Outro" },
  };

  return styles[type] || styles.outro;
};

export function AgendaEventList({ events }: AgendaEventListProps) {
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<AgendaEvent | null>(null);
  const clubId = useCurrentClubId();

  if (events.length === 0) {
    return null;
  }

  // Ordenar eventos por horário
  const sortedEvents = [...events].sort((a, b) => a.date.getTime() - b.date.getTime());

  const handleEdit = (event: AgendaEventListProps["events"][number]) => {
    // Converter o evento para o formato AgendaEvent
    const agendaEvent: AgendaEvent = {
      id: event.id,
      club_id: event.club_id,
      title: event.title,
      type: event.type,
      date: event.date.toISOString(),
      time: event.time || "",
      endTime: event.endTime.toISOString(),
      location: event.location,
      participants: event.participants,
      description: event.description,
    };
    setSelectedEvent(agendaEvent);
    setOpenEditDialog(true);
  };

  // Função para deletar um evento (com confirmação no modal)
  const handleDelete = async (eventId: number) => {
    // A confirmação de exclusão agora é feita dentro do AgendaEventEditDialog
    console.log("Evento excluído:", eventId);
  };

  return (
    <div className="space-y-4">
      {sortedEvents.map((event) => {
        const { color, bg, label } = getEventTypeStyles(event.type);

        return (
          <Card key={event.id} className="overflow-hidden">
            <div className={`h-1 w-full ${bg}`} />
            <CardContent className="pt-6">
              <div className="flex items-start justify-between">
                <h3 className="font-medium text-lg">{event.title}</h3>
                <Badge className={`${color} ${bg} border-transparent`}>
                  {label}
                </Badge>
              </div>

              <CardDescription className="mt-2 space-y-2">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span>
                    {(() => {
                      try {
                        const startDate = event.date instanceof Date ? event.date : new Date(event.date);
                        const endDate = event.endTime instanceof Date ? event.endTime : new Date(event.endTime);
                        return `${format(startDate, 'HH:mm', { locale: ptBR })} - ${format(endDate, 'HH:mm', { locale: ptBR })}`;
                      } catch (error) {
                        return 'Horário inválido';
                      }
                    })()}
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <span>{event.location}</span>
                </div>

                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <span>{event.participants.join(", ")}</span>
                </div>

                <div className="mt-2 text-sm text-foreground">{event.description}</div>
              </CardDescription>
            </CardContent>
            <CardFooter className="justify-end space-x-2">
              <PermissionControl permission={AGENDA_PERMISSIONS.EDIT}>
                <Button size="sm" variant="secondary" onClick={() => handleEdit(event)}>
                  Editar
                </Button>
              </PermissionControl>
            </CardFooter>
          </Card>
        );
      })}

      {/* Modal de edição */}
      {selectedEvent && (
        <AgendaEventEditDialog
          open={openEditDialog}
          onOpenChange={setOpenEditDialog}
          clubId={clubId}
          event={selectedEvent}
        />
      )}
    </div>
  );
}
