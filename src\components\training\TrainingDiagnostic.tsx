import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Loader2, AlertTriangle, CheckCircle, Settings, Play } from 'lucide-react';
import { fixTrainingsWithoutPlayers, testTrainingPlayersPermissions, debugPlayerTrainings, fixInvalidTrainingDates } from '@/api/api';
import { useCurrentClubId } from '@/context/ClubContext';
import { useToast } from '@/hooks/use-toast';

interface DiagnosticResult {
  fixed: number;
  errors: string[];
}

interface PermissionTestResult {
  canRead: boolean;
  canWrite: boolean;
  errors: string[];
}

interface PlayerDebugResult {
  playerExists: boolean;
  playerCategories: any[];
  allTrainings: any[];
  playerTrainingAssociations: any[];
  processedTrainings: any[];
  filteredTrainings: any[];
  errors: string[];
}

export function TrainingDiagnostic() {
  const [isRunning, setIsRunning] = useState(false);
  const [isTestingPermissions, setIsTestingPermissions] = useState(false);
  const [isDebuggingPlayer, setIsDebuggingPlayer] = useState(false);
  const [result, setResult] = useState<DiagnosticResult | null>(null);
  const [permissionResult, setPermissionResult] = useState<PermissionTestResult | null>(null);
  const [playerDebugResult, setPlayerDebugResult] = useState<PlayerDebugResult | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [playerIdToDebug, setPlayerIdToDebug] = useState('');
  const clubId = useCurrentClubId();
  const { toast } = useToast();

  const runDiagnostic = async () => {
    if (!clubId) {
      toast({
        title: "Erro",
        description: "ID do clube não encontrado",
        variant: "destructive"
      });
      return;
    }

    setIsRunning(true);
    setResult(null);

    try {
      const diagnosticResult = await fixTrainingsWithoutPlayers(clubId);
      setResult(diagnosticResult);
      
      if (diagnosticResult.fixed > 0) {
        toast({
          title: "Correção concluída!",
          description: `${diagnosticResult.fixed} treinamento(s) corrigido(s) com sucesso.`,
          variant: "default"
        });
      } else {
        toast({
          title: "Nenhum problema encontrado",
          description: "Todos os treinamentos já estão corretamente associados aos jogadores.",
          variant: "default"
        });
      }

      if (diagnosticResult.errors.length > 0) {
        toast({
          title: "Alguns erros ocorreram",
          description: `${diagnosticResult.errors.length} erro(s) durante a correção. Verifique os detalhes.`,
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Erro durante o diagnóstico:', error);
      toast({
        title: "Erro durante o diagnóstico",
        description: error instanceof Error ? error.message : "Erro desconhecido",
        variant: "destructive"
      });
    } finally {
      setIsRunning(false);
    }
  };

  const testPermissions = async () => {
    if (!clubId) {
      toast({
        title: "Erro",
        description: "ID do clube não encontrado",
        variant: "destructive"
      });
      return;
    }

    setIsTestingPermissions(true);
    setPermissionResult(null);

    try {
      const permissionTestResult = await testTrainingPlayersPermissions(clubId);
      setPermissionResult(permissionTestResult);

      if (permissionTestResult.canRead && permissionTestResult.canWrite) {
        toast({
          title: "Permissões OK",
          description: "Todas as permissões estão funcionando corretamente.",
          variant: "default"
        });
      } else {
        toast({
          title: "Problemas de permissão detectados",
          description: "Verifique os detalhes para mais informações.",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Erro durante teste de permissões:', error);
      toast({
        title: "Erro durante teste de permissões",
        description: error instanceof Error ? error.message : "Erro desconhecido",
        variant: "destructive"
      });
    } finally {
      setIsTestingPermissions(false);
    }
  };

  const debugPlayer = async () => {
    if (!clubId) {
      toast({
        title: "Erro",
        description: "ID do clube não encontrado",
        variant: "destructive"
      });
      return;
    }

    if (!playerIdToDebug.trim()) {
      toast({
        title: "Erro",
        description: "Por favor, insira o ID do jogador",
        variant: "destructive"
      });
      return;
    }

    setIsDebuggingPlayer(true);
    setPlayerDebugResult(null);

    try {
      const debugResult = await debugPlayerTrainings(clubId, playerIdToDebug.trim());
      setPlayerDebugResult(debugResult);

      if (debugResult.playerExists) {
        toast({
          title: "Diagnóstico concluído",
          description: `Jogador encontrado. ${debugResult.filteredTrainings.length} treinamento(s) visível(is) no perfil.`,
          variant: "default"
        });
      } else {
        toast({
          title: "Jogador não encontrado",
          description: "Verifique se o ID do jogador está correto.",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Erro durante debug do jogador:', error);
      toast({
        title: "Erro durante diagnóstico",
        description: error instanceof Error ? error.message : "Erro desconhecido",
        variant: "destructive"
      });
    } finally {
      setIsDebuggingPlayer(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          <CardTitle>Diagnóstico de Treinamentos</CardTitle>
        </div>
        <CardDescription>
          Corrige treinamentos que não estão aparecendo no perfil dos jogadores
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Esta ferramenta identifica e corrige treinamentos que não têm jogadores associados.
            Isso pode acontecer com treinamentos criados antes de melhorias no sistema.
          </AlertDescription>
        </Alert>

        <div className="space-y-3 p-4 border rounded-lg bg-muted/50">
          <Label htmlFor="player-id">Diagnóstico Específico do Jogador</Label>
          <div className="flex gap-2">
            <Input
              id="player-id"
              placeholder="ID do jogador (ex: 123e4567-e89b-12d3-a456-426614174000)"
              value={playerIdToDebug}
              onChange={(e) => setPlayerIdToDebug(e.target.value)}
              disabled={isDebuggingPlayer}
            />
            <Button
              onClick={debugPlayer}
              disabled={isRunning || isTestingPermissions || isDebuggingPlayer || !playerIdToDebug.trim()}
              variant="outline"
              className="flex items-center gap-2 whitespace-nowrap"
            >
              {isDebuggingPlayer ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Settings className="h-4 w-4" />
              )}
              {isDebuggingPlayer ? 'Analisando...' : 'Analisar Jogador'}
            </Button>
          </div>
          <p className="text-sm text-muted-foreground">
            Use esta opção para analisar por que um jogador específico não vê seus treinamentos.
          </p>
        </div>

        <div className="flex gap-2 flex-wrap">
          <Button
            onClick={runDiagnostic}
            disabled={isRunning || isTestingPermissions || isDebuggingPlayer}
            className="flex items-center gap-2"
          >
            {isRunning ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Play className="h-4 w-4" />
            )}
            {isRunning ? 'Executando...' : 'Executar Diagnóstico'}
          </Button>

          <Button
            onClick={testPermissions}
            disabled={isRunning || isTestingPermissions || isDebuggingPlayer}
            variant="outline"
            className="flex items-center gap-2"
          >
            {isTestingPermissions ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Settings className="h-4 w-4" />
            )}
            {isTestingPermissions ? 'Testando...' : 'Testar Permissões'}
          </Button>

          {(result || permissionResult || playerDebugResult) && (
            <Button
              variant="outline"
              onClick={() => setShowDetails(!showDetails)}
            >
              {showDetails ? 'Ocultar' : 'Ver'} Detalhes
            </Button>
          )}
        </div>

        {(result || permissionResult) && (
          <div className="space-y-3">
            <div className="flex gap-2 flex-wrap">
              {result && (
                <>
                  <Badge variant={result.fixed > 0 ? "default" : "secondary"} className="flex items-center gap-1">
                    <CheckCircle className="h-3 w-3" />
                    {result.fixed} Corrigidos
                  </Badge>

                  {result.errors.length > 0 && (
                    <Badge variant="destructive" className="flex items-center gap-1">
                      <AlertTriangle className="h-3 w-3" />
                      {result.errors.length} Erros
                    </Badge>
                  )}
                </>
              )}

              {permissionResult && (
                <>
                  <Badge variant={permissionResult.canRead ? "default" : "destructive"} className="flex items-center gap-1">
                    {permissionResult.canRead ? <CheckCircle className="h-3 w-3" /> : <AlertTriangle className="h-3 w-3" />}
                    Leitura {permissionResult.canRead ? 'OK' : 'Erro'}
                  </Badge>

                  <Badge variant={permissionResult.canWrite ? "default" : "destructive"} className="flex items-center gap-1">
                    {permissionResult.canWrite ? <CheckCircle className="h-3 w-3" /> : <AlertTriangle className="h-3 w-3" />}
                    Escrita {permissionResult.canWrite ? 'OK' : 'Erro'}
                  </Badge>
                </>
              )}
            </div>

            {showDetails && (
              <div className="space-y-2">
                {result && result.fixed > 0 && (
                  <Alert>
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Treinamentos corrigidos:</strong> {result.fixed}
                      <br />
                      Os jogadores das categorias correspondentes foram automaticamente associados aos treinamentos.
                    </AlertDescription>
                  </Alert>
                )}

                {result && result.errors.length > 0 && (
                  <Alert variant="destructive">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Erros de correção encontrados:</strong>
                      <ul className="mt-2 space-y-1">
                        {result.errors.slice(0, 5).map((error, index) => (
                          <li key={index} className="text-sm">• {error}</li>
                        ))}
                        {result.errors.length > 5 && (
                          <li className="text-sm text-muted-foreground">
                            ... e mais {result.errors.length - 5} erro(s)
                          </li>
                        )}
                      </ul>
                    </AlertDescription>
                  </Alert>
                )}

                {permissionResult && permissionResult.errors.length > 0 && (
                  <Alert variant="destructive">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Erros de permissão encontrados:</strong>
                      <ul className="mt-2 space-y-1">
                        {permissionResult.errors.map((error, index) => (
                          <li key={index} className="text-sm">• {error}</li>
                        ))}
                      </ul>
                      <div className="mt-2 text-sm">
                        <strong>Possíveis causas:</strong>
                        <ul className="mt-1 space-y-1 list-disc list-inside">
                          <li>Políticas RLS (Row Level Security) restritivas</li>
                          <li>Usuário sem permissões adequadas no banco</li>
                          <li>Problemas de configuração do clube</li>
                        </ul>
                      </div>
                    </AlertDescription>
                  </Alert>
                )}

                {permissionResult && permissionResult.canRead && permissionResult.canWrite && permissionResult.errors.length === 0 && (
                  <Alert>
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Permissões OK:</strong> Todas as permissões estão funcionando corretamente.
                      Se ainda há problemas, pode ser um erro específico durante a criação dos treinamentos.
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            )}
          </div>
        )}

        <div className="text-sm text-muted-foreground">
          <p><strong>O que esta ferramenta faz:</strong></p>
          <ul className="mt-1 space-y-1 list-disc list-inside">
            <li><strong>Diagnóstico:</strong> Identifica e corrige treinamentos sem jogadores associados</li>
            <li><strong>Teste de Permissões:</strong> Verifica se há problemas de acesso ao banco de dados</li>
            <li>Associa automaticamente jogadores da categoria do treinamento</li>
            <li>Ignora treinamentos já concluídos</li>
            <li>Não afeta treinamentos que já têm jogadores associados</li>
          </ul>

          <p className="mt-3"><strong>Quando usar o teste de permissões:</strong></p>
          <ul className="mt-1 space-y-1 list-disc list-inside">
            <li>Se colaboradores relatam que criaram treinos mas não aparecem para jogadores</li>
            <li>Se há erros durante a criação de treinamentos</li>
            <li>Para verificar se as políticas de segurança estão funcionando</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
