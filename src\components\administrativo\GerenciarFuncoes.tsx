import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "@/components/ui/use-toast";
import { Loader2, Plus, Pencil, Trash2 } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";

interface Role {
  id: number;
  name: string;
  role_type: 'technical' | 'assistant_technical' | 'administrative' | 'other';
  is_active: boolean;
}

const ROLE_TYPES = [
  { value: 'technical', label: 'Técnico' },
  { value: 'assistant_technical', label: 'Auxiliar Técnico' },
  { value: 'administrative', label: 'Administrativo' },
  { value: 'other', label: 'Outro' },
];

interface GerenciarFuncoesProps {
  clubId: number;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onRoleSelect?: (role: string) => void;
}

export function GerenciarFuncoes({ clubId, open, onOpenChange, onRoleSelect }: GerenciarFuncoesProps) {
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [newRole, setNewRole] = useState('');
  const [newRoleType, setNewRoleType] = useState('other');
  const [editingRole, setEditingRole] = useState<Role | null>(null);

  // Carrega as funções do banco de dados
  const loadRoles = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('collaborator_roles')
        .select('*')
        .eq('club_id', clubId)
        .order('name', { ascending: true });

      if (error) throw error;
      
      setRoles(data || []);
    } catch (error) {
      console.error('Erro ao carregar funções:', error);
      toast({
        title: 'Erro',
        description: 'Não foi possível carregar as funções',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Adiciona uma nova função
  const handleAddRole = async () => {
    if (!newRole.trim()) {
      toast({
        title: 'Atenção',
        description: 'Por favor, informe o nome da função',
        variant: 'destructive',
      });
      return;
    }

    try {
      setSaving(true);
      const { data, error } = await supabase
        .from('collaborator_roles')
        .insert([
          {
            club_id: clubId,
            name: newRole.trim(),
            role_type: newRoleType,
            created_by: (await supabase.auth.getUser()).data.user?.id,
          },
        ])
        .select();

      if (error) throw error;

      toast({
        title: 'Sucesso',
        description: 'Função adicionada com sucesso',
      });

      setNewRole('');
      setNewRoleType('other');
      loadRoles();
      
      // Se houver um callback para selecionar a função, chama-o
      if (onRoleSelect && data?.[0]) {
        onRoleSelect(data[0].name);
      }
    } catch (error: any) {
      console.error('Erro ao adicionar função:', error);
      toast({
        title: 'Erro',
        description: error.message || 'Não foi possível adicionar a função',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  // Atualiza uma função existente
  const handleUpdateRole = async () => {
    if (!editingRole || !newRole.trim()) return;

    try {
      setSaving(true);
      const { error } = await supabase
        .from('collaborator_roles')
        .update({
          name: newRole.trim(),
          role_type: newRoleType,
          updated_at: new Date().toISOString(),
        })
        .eq('id', editingRole.id);

      if (error) throw error;

      toast({
        title: 'Sucesso',
        description: 'Função atualizada com sucesso',
      });

      setEditingRole(null);
      setNewRole('');
      setNewRoleType('other');
      loadRoles();
      
      // Se houver um callback para selecionar a função, chama-o
      if (onRoleSelect) {
        onRoleSelect(newRole.trim());
      }
    } catch (error: any) {
      console.error('Erro ao atualizar função:', error);
      toast({
        title: 'Erro',
        description: error.message || 'Não foi possível atualizar a função',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  // Remove uma função
  const handleDeleteRole = async (roleId: number) => {
    if (!confirm('Tem certeza que deseja remover esta função?')) return;

    try {
      setSaving(true);
      const { error } = await supabase
        .from('collaborator_roles')
        .delete()
        .eq('id', roleId);

      if (error) throw error;

      toast({
        title: 'Sucesso',
        description: 'Função removida com sucesso',
      });

      loadRoles();
    } catch (error: any) {
      console.error('Erro ao remover função:', error);
      toast({
        title: 'Erro',
        description: error.message || 'Não foi possível remover a função',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  // Inicia a edição de uma função
  const startEditing = (role: Role) => {
    setEditingRole(role);
    setNewRole(role.name);
    setNewRoleType(role.role_type);
  };

  // Cancela a edição
  const cancelEditing = () => {
    setEditingRole(null);
    setNewRole('');
    setNewRoleType('other');
  };

  // Carrega as funções quando o modal é aberto
  useEffect(() => {
    if (open) {
      loadRoles();
    } else {
      // Reseta os estados quando o modal é fechado
      setEditingRole(null);
      setNewRole('');
      setNewRoleType('other');
    }
  }, [open]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Gerenciar Funções</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="newRole">
              {editingRole ? 'Editar Função' : 'Nova Função'}
            </Label>
            <div className="flex gap-2">
              <Input
                id="newRole"
                value={newRole}
                onChange={(e) => setNewRole(e.target.value)}
                placeholder="Nome da função"
                className="flex-1"
              />
              <Select
                value={newRoleType}
                onValueChange={setNewRoleType}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Tipo" />
                </SelectTrigger>
                <SelectContent>
                  {ROLE_TYPES.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button
                onClick={editingRole ? handleUpdateRole : handleAddRole}
                disabled={saving || !newRole.trim()}
              >
                {saving ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : editingRole ? (
                  'Atualizar'
                ) : (
                  <Plus className="h-4 w-4" />
                )}
              </Button>
              {editingRole && (
                <Button variant="outline" onClick={cancelEditing}>
                  Cancelar
                </Button>
              )}
            </div>
          </div>

          <div className="border rounded-md">
            <div className="p-4 border-b">
              <h3 className="font-medium">Funções Existentes</h3>
            </div>
            {loading ? (
              <div className="p-4 text-center">
                <Loader2 className="h-6 w-6 animate-spin mx-auto" />
              </div>
            ) : roles.length === 0 ? (
              <div className="p-4 text-center text-gray-500">
                Nenhuma função cadastrada
              </div>
            ) : (
              <div className="divide-y">
                {roles.map((role) => (
                  <div key={role.id} className="p-4 flex justify-between items-center">
                    <div>
                      <div className="font-medium">{role.name}</div>
                      <div className="text-sm text-gray-500">
                        {ROLE_TYPES.find(t => t.value === role.role_type)?.label || 'Outro'}
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => startEditing(role)}
                        disabled={saving}
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDeleteRole(role.id)}
                        disabled={saving}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button onClick={() => onOpenChange(false)}>Fechar</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
