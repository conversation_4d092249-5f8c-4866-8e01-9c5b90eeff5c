import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { RichTextEditor } from "@/components/ui/rich-text-editor";
import { useToast } from "@/hooks/use-toast";
import { useUser } from "@/context/UserContext";
import { usePermission } from "@/hooks/usePermission";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  getPlayerEvaluation,
  savePlayerEvaluation,
  lockPlayerEvaluation,
  updatePlayerEvaluationStatus,
  deletePlayerEvaluation,
  PlayerEvaluation as PlayerEvaluationType
} from "@/api/api";
import { History, FileText, XCircle } from "lucide-react";
// AssinaturaDigitalEvaluationDialog removido - assinatura é automática
import { formatDate } from "@/lib/utils";
import { AlertCircle, CheckCircle, Info } from "lucide-react";
import { PlayerEvaluationsList } from "@/components/evaluation/PlayerEvaluationsList";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface PlayerEvaluationProps {
  playerId: string;
  clubId: number;
  isOwnProfile?: boolean;
}

export function PlayerEvaluation({ playerId, clubId, isOwnProfile = false }: PlayerEvaluationProps) {
  const [evaluation, setEvaluation] = useState<PlayerEvaluationType | null>(null);
  const [content, setContent] = useState("");
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  // Assinatura digital é automática - não precisa de estado para modal
  const [activeTab, setActiveTab] = useState("current");
  const { toast } = useToast();
  const { user } = useUser();
  const { can, role } = usePermission();

  const isAdmin = role === "admin" || role === "president";
  const canEdit = can("players.edit") && !isOwnProfile;
  const canDelete = isAdmin && !isOwnProfile;

  // Fetch evaluation data
  useEffect(() => {
    const fetchEvaluation = async () => {
      if (!user?.id) return;

      try {
        setLoading(true);
        const data = await getPlayerEvaluation(clubId, playerId, user.id);
        setEvaluation(data);
        if (data) {
          setContent(data.content);
        }
      } catch (error) {
        console.error("Error fetching player evaluation:", error);
        toast({
          title: "Erro",
          description: "Não foi possível carregar o pré cadastro do atleta",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchEvaluation();
  }, [clubId, playerId, user?.id, toast]);

  // Save evaluation
  const handleSave = async () => {
    if (!user?.id) return;

    try {
      setSaving(true);
      // Always add digital signature (true is now the default)
      const data = await savePlayerEvaluation(clubId, playerId, content, user.id);
      setEvaluation(data);
      setIsEditing(false);
      toast({
        title: "Sucesso",
        description: "Avaliação salva com sucesso! Assinatura digital adicionada automaticamente.",
      });
    } catch (error) {
      console.error("Error saving player evaluation:", error);
      toast({
        title: "Erro",
        description: "Não foi possível salvar o pré cadastro do atleta",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  // Assinatura digital é automática - função removida

  // Lock evaluation
  const handleLock = async () => {
    if (!user?.id) return;

    try {
      setSaving(true);
      const data = await lockPlayerEvaluation(clubId, playerId, user.id);
      setEvaluation(data);
      toast({
        title: "Sucesso",
        description: "Avaliação bloqueada com sucesso",
      });
    } catch (error) {
      console.error("Error locking player evaluation:", error);
      toast({
        title: "Erro",
        description: "Não foi possível bloquear a avaliação do atleta",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  // Update evaluation status
  const handleStatusChange = async (status: "approved" | "released" | "monitored") => {
    if (!user?.id) return;

    try {
      setSaving(true);
      const data = await updatePlayerEvaluationStatus(clubId, playerId, status, user.id);
      setEvaluation(data);
      toast({
        title: "Sucesso",
        description: "Status da avaliação atualizado com sucesso",
      });
    } catch (error) {
      console.error("Error updating player evaluation status:", error);
      toast({
        title: "Erro",
        description: "Não foi possível atualizar o status da avaliação",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  // Delete evaluation
  const handleDelete = async () => {
    if (!user?.id) return;

    try {
      setSaving(true);
      await deletePlayerEvaluation(clubId, playerId, user.id);
      setEvaluation(null);
      setContent("");
      setShowDeleteDialog(false);
      toast({
        title: "Sucesso",
        description: "Avaliação excluída com sucesso",
      });
    } catch (error) {
      console.error("Error deleting player evaluation:", error);
      toast({
        title: "Erro",
        description: "Não foi possível excluir a avaliação do atleta",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  // Get status badge
  const getStatusBadge = () => {
    if (!evaluation) return null;

    switch (evaluation.status) {
      case "pending":
        const managerApproved = !!evaluation.approved_by_manager;
        const presidentApproved = !!evaluation.approved_by_president;
        const requiresManager = evaluation.requires_manager_approval;
        const requiresPresident = evaluation.requires_president_approval;

        if (requiresManager && requiresPresident) {
          if (managerApproved && presidentApproved) {
            return <Badge className="bg-green-500 flex items-center gap-1">
              <CheckCircle className="h-3 w-3" />
              Aprovado - Aguardando Liberação
            </Badge>;
          } else if (managerApproved) {
            return <Badge className="bg-yellow-500 flex items-center gap-1">
              <AlertCircle className="h-3 w-3" />
              Aprovado pelo Gerente
            </Badge>;
          } else if (presidentApproved) {
            return <Badge className="bg-yellow-500 flex items-center gap-1">
              <AlertCircle className="h-3 w-3" />
              Aprovado pelo Presidente
            </Badge>;
          }
        }

        return <Badge className="bg-gray-500 flex items-center gap-1">
          <AlertCircle className="h-3 w-3" />
          Aguardando Aprovação
        </Badge>;
      case "approved":
        return <Badge className="bg-green-500">Aprovado</Badge>;
      case "released":
        return <Badge className="bg-red-500">Liberado</Badge>;
      case "monitored":
        return <Badge className="bg-yellow-500">Monitorado</Badge>;
      case "rejected":
        return <Badge className="bg-red-600 flex items-center gap-1">
          <XCircle className="h-3 w-3" />
          Rejeitado
        </Badge>;
      default:
        return <Badge className="bg-gray-500">Pendente</Badge>;
    }
  };

  // Render loading state
  if (loading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center h-40">
            <p className="text-muted-foreground">Carregando pré cadastro...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Render player view (read-only)
  if (isOwnProfile) {
    return (
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Avaliação do Atleta</CardTitle>
          {getStatusBadge()}
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="current" className="flex items-center gap-1">
                <FileText className="h-4 w-4" />
                <span>Avaliação Atual</span>
              </TabsTrigger>
              <TabsTrigger value="history" className="flex items-center gap-1">
                <History className="h-4 w-4" />
                <span>Histórico de Avaliações</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="current">
          {evaluation ? (
            <>
              <div className="prose max-w-none" dangerouslySetInnerHTML={{ __html: evaluation.content }} />

              {evaluation.status === "released" && (
                <Alert className="mt-4 bg-red-50 border-red-200">
                  <AlertCircle className="h-4 w-4 text-red-500" />
                  <AlertDescription className="text-red-500">
                    Você foi liberado. Favor regularizar sua saída do alojamento, devolver os materiais recebidos e recolher os documentos deixados no clube.
                  </AlertDescription>
                </Alert>
              )}

              <div className="mt-4 text-sm text-muted-foreground space-y-2">
                <div>
                  Criado por: {evaluation.created_by_name || "Usuário"}
                  {evaluation.created_by_role && <span> ({evaluation.created_by_role})</span>}
                </div>
                <div>
                  Última atualização: {formatDate(evaluation.updated_at)}
                </div>

                {/* Approval Information */}
                {evaluation.status === "pending" && (evaluation.requires_manager_approval || evaluation.requires_president_approval) && (
                  <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                    <h4 className="text-sm font-medium text-yellow-800 mb-2">Status de Aprovação</h4>
                    <div className="space-y-1 text-xs">
                      {evaluation.requires_manager_approval && (
                        <div className="flex items-center gap-2">
                          {evaluation.approved_by_manager ? (
                            <CheckCircle className="h-3 w-3 text-green-600" />
                          ) : (
                            <AlertCircle className="h-3 w-3 text-yellow-600" />
                          )}
                          <span>
                            Gerente: {evaluation.approved_by_manager ?
                              `Aprovado em ${formatDate(evaluation.manager_approved_at)}` :
                              "Aguardando aprovação"
                            }
                          </span>
                        </div>
                      )}
                      {evaluation.requires_president_approval && (
                        <div className="flex items-center gap-2">
                          {evaluation.approved_by_president ? (
                            <CheckCircle className="h-3 w-3 text-green-600" />
                          ) : (
                            <AlertCircle className="h-3 w-3 text-yellow-600" />
                          )}
                          <span>
                            Presidente: {evaluation.approved_by_president ?
                              `Aprovado em ${formatDate(evaluation.president_approved_at)}` :
                              "Aguardando aprovação"
                            }
                          </span>
                        </div>
                      )}
                    </div>
                    {(evaluation.manager_notes || evaluation.president_notes) && (
                      <div className="mt-2">
                        <h5 className="text-xs font-medium text-yellow-800">Observações:</h5>
                        {evaluation.manager_notes && (
                          <p className="text-xs text-yellow-700">Gerente: {evaluation.manager_notes}</p>
                        )}
                        {evaluation.president_notes && (
                          <p className="text-xs text-yellow-700">Presidente: {evaluation.president_notes}</p>
                        )}
                      </div>
                    )}
                  </div>
                )}

                {evaluation.status === "rejected" && (evaluation.manager_notes || evaluation.president_notes) && (
                  <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
                    <h4 className="text-sm font-medium text-red-800 mb-2">Motivo da Rejeição</h4>
                    <div className="space-y-1 text-xs">
                      {evaluation.manager_notes && (
                        <p className="text-red-700">Gerente: {evaluation.manager_notes}</p>
                      )}
                      {evaluation.president_notes && (
                        <p className="text-red-700">Presidente: {evaluation.president_notes}</p>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </>
          ) : (
            <div className="flex flex-col items-center justify-center h-40">
              <Info className="h-8 w-8 text-muted-foreground mb-2" />
              <p className="text-muted-foreground">Nenhuma avaliação disponível</p>
            </div>
          )}
            </TabsContent>

            <TabsContent value="history">
              <PlayerEvaluationsList playerId={parseInt(playerId)} showHeader={false} />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    );
  }

  // Render staff view (editable)
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Avaliação do Atleta</CardTitle>
        {evaluation && getStatusBadge()}
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="current" className="flex items-center gap-1">
              <FileText className="h-4 w-4" />
              <span>Avaliação Atual</span>
            </TabsTrigger>
            <TabsTrigger value="history" className="flex items-center gap-1">
              <History className="h-4 w-4" />
              <span>Histórico de Avaliações</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="current">
        {isEditing ? (
          <>
            <RichTextEditor content={content} onChange={setContent} />
          </>
        ) : (
          <>
            {evaluation ? (
              <>
                <div className="prose max-w-none" dangerouslySetInnerHTML={{ __html: evaluation.content }} />

                {/* Assinatura digital já é incluída automaticamente no conteúdo */}
              </>
            ) : (
              <div className="flex flex-col items-center justify-center h-40">
                <Info className="h-8 w-8 text-muted-foreground mb-2" />
                <p className="text-muted-foreground">Nenhuma avaliação disponível</p>
                {canEdit && (
                  <Button
                    className="mt-4"
                    style={{
                      backgroundColor: 'var(--color-primary)',
                      color: 'white'
                    }}
                    onClick={() => {
                      setContent("");
                      setIsEditing(true);
                    }}
                  >
                    Criar Avaliação
                  </Button>
                )}
              </div>
            )}
          </>
        )}

        {/* Assinatura digital é automática - não precisa de modal */}

        {evaluation && evaluation.status === "released" && (
          <Alert className="mt-4 bg-red-50 border-red-200">
            <AlertCircle className="h-4 w-4 text-red-500" />
            <AlertDescription className="text-red-500">
              O atleta foi liberado. Uma mensagem foi exibida para que regularize sua saída do alojamento, devolva os materiais recebidos e recolha os documentos deixados no clube.
            </AlertDescription>
          </Alert>
        )}

        {evaluation && (
          <div className="mt-4 text-sm text-muted-foreground">
            Criado por: {evaluation.created_by_name || "Usuário"}
            {evaluation.created_by_role && <span> ({evaluation.created_by_role})</span>} em {formatDate(evaluation.created_at)}
            <br />
            Última atualização: {formatDate(evaluation.updated_at)}
            {evaluation.updated_by && evaluation.updated_by !== evaluation.created_by && (
              <> por {evaluation.updated_by_name || "Usuário"}
                {evaluation.updated_by_role && <span> ({evaluation.updated_by_role})</span>}
              </>
            )}
            {evaluation.last_viewed_at && (
              <>
                <br />
                <span className="text-amber-600">
                  Visualizado pelo atleta em: {formatDate(evaluation.last_viewed_at, {
                    day: '2-digit',
                    month: '2-digit',
                    year: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </span>
              </>
            )}
          </div>
        )}
          </TabsContent>

          <TabsContent value="history">
            <PlayerEvaluationsList playerId={parseInt(playerId)} showHeader={false} />
          </TabsContent>
        </Tabs>
      </CardContent>

      {canEdit && (
        <CardFooter className="flex justify-between">
          <div>
            {evaluation && !isEditing && (
              <Button
                variant="destructive"
                onClick={() => setShowDeleteDialog(true)}
                disabled={saving || !canDelete}
              >
                Excluir
              </Button>
            )}
          </div>

          <div className="flex gap-2">
            {isEditing ? (
              <>
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsEditing(false);
                    setContent(evaluation?.content || "");
                  }}
                  disabled={saving}
                >
                  Cancelar
                </Button>
                <Button
                  onClick={handleSave}
                  disabled={saving}
                  style={{
                    backgroundColor: 'var(--color-primary)',
                    color: 'white'
                  }}
                >
                  {saving ? "Salvando..." : "Salvar"}
                </Button>
              </>
            ) : (
              <>
                {evaluation && (
                  <>
                    {!evaluation.is_locked && (
                      <Button
                        variant="outline"
                        onClick={() => setIsEditing(true)}
                        disabled={saving}
                        style={{
                          borderColor: 'var(--color-primary)',
                          color: 'var(--color-primary)'
                        }}
                      >
                        Editar
                      </Button>
                    )}

                    {!evaluation.is_locked && (
                      <Button
                        variant="outline"
                        onClick={handleLock}
                        disabled={saving}
                        style={{
                          borderColor: 'var(--color-primary)',
                          color: 'var(--color-primary)'
                        }}
                      >
                        Finalizar
                      </Button>
                    )}

                    {isAdmin && (
                      <Select
                        onValueChange={(value) => handleStatusChange(value as any)}
                        defaultValue={evaluation.status}
                        disabled={saving}
                      >
                        <SelectTrigger className="w-[180px]">
                          <SelectValue placeholder="Status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="approved">Aprovado</SelectItem>
                          <SelectItem value="released">Liberado</SelectItem>
                          <SelectItem value="monitored">Monitorado</SelectItem>
                        </SelectContent>
                      </Select>
                    )}
                  </>
                )}
              </>
            )}
          </div>
        </CardFooter>
      )}

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Excluir avaliação</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir esta avaliação? Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>Excluir</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  );
}
