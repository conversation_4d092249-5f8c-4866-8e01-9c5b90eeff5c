-- SQL script to create inventory request tables
CREATE TABLE IF NOT EXISTS inventory_requests (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id) NOT NULL,
  department_id INTEGER REFERENCES departments(id),
  requested_by UUID REFERENCES auth.users(id) NOT NULL,
  requester_type TEXT NOT NULL,
  requester_id TEXT,
  category TEXT NOT NULL,
  withdrawal_date DATE NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending',
  requester_notes TEXT,
  delivery_notes TEXT,
  delivery_method TEXT NOT NULL DEFAULT 'pickup',
  delivery_location TEXT,
  requester_signature_url TEXT,
  delivery_signature_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS inventory_request_items (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id) NOT NULL,
  request_id INTEGER REFERENCES inventory_requests(id) ON DELETE CASCADE,
  product_id INTEGER REFERENCES inventory_products(id) NOT NULL,
  quantity INTEGER NOT NULL,
  returned_quantity INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

ALTER TABLE inventory_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory_request_items ENABLE ROW LEVEL SECURITY;

CREATE POLICY inventory_requests_isolation_policy ON inventory_requests
  FOR ALL USING (club_id IN (SELECT club_id FROM club_members WHERE user_id = auth.uid()));

CREATE POLICY inventory_request_items_isolation_policy ON inventory_request_items
  FOR ALL USING (club_id IN (SELECT club_id FROM club_members WHERE user_id = auth.uid()));
