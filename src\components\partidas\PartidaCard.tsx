import { Button } from "@/components/ui/button";
import { Pencil, Trash2, Calendar, Clock, MapPin } from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";

interface PartidaCardProps {
  partida: {
    id: string;
    opponent: string;
    date: string;
    time?: string;
    location: string;
    type: "casa" | "fora";
    competition?: string;
  };
  nomeTime: string;
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
  isSelected: boolean;
  onSelect: (id: string) => void;
}

export function PartidaCard({
  partida,
  nomeTime,
  onEdit,
  onDelete,
  isSelected,
  onSelect
}: PartidaCardProps) {
  // Criar data no fuso horário local para evitar problemas de timezone
  const [ano, mes, dia] = partida.date.split('-').map(Number);
  const data = new Date(ano, mes - 1, dia);
  const dataFormatada = format(data, "dd/MM/yyyy", { locale: ptBR });
  const diaSemana = format(data, "EEEE", { locale: ptBR });
  
  // Extrair hora e minuto do campo time, se existir
  let horaFormatada = "";
  if (partida.time) {
    const [hora, minuto] = partida.time.split(':');
    horaFormatada = `${hora}:${minuto}`;
  } else if (partida.date) {
    // Se não tiver time, tenta extrair da data
    horaFormatada = format(data, "HH:mm");
  }

  return (
    <div 
      className={`border rounded-lg p-4 mb-3 cursor-pointer transition-colors ${isSelected ? 'bg-primary/10 border-primary' : 'hover:bg-gray-50'}`}
      onClick={() => onSelect(partida.id)}
    >
      <div className="flex justify-between items-start">
        <div>
          <div className="flex items-center gap-2 text-sm text-muted-foreground mb-1">
            <Calendar className="h-4 w-4" />
            <span>{diaSemana.charAt(0).toUpperCase() + diaSemana.slice(1)}, {dataFormatada}</span>
            {horaFormatada && (
              <span className="flex items-center gap-1 ml-2">
                <Clock className="h-4 w-4" />
                {horaFormatada}
              </span>
            )}
          </div>
          
          <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
            <MapPin className="h-4 w-4" />
            <span>{partida.location}</span>
          </div>
          
          <div className="flex items-center gap-2 mt-1">
            <span className="font-medium">
              {partida.type === "casa" ? nomeTime : partida.opponent}
            </span>
            <span>vs</span>
            <span className="font-medium">
              {partida.type === "casa" ? partida.opponent : nomeTime}
            </span>
          </div>
          
          {partida.competition && (
            <div className="mt-2">
              <span className="inline-block bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded">
                {partida.competition}
              </span>
            </div>
          )}
        </div>
        
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8"
            onClick={(e) => {
              e.stopPropagation();
              onEdit(partida.id);
            }}
          >
            <Pencil className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8 text-destructive hover:bg-destructive/10 hover:text-destructive"
            onClick={(e) => {
              e.stopPropagation();
              onDelete(partida.id);
            }}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
