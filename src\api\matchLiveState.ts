import { supabase } from "@/integrations/supabase/client";

// Tipos para o estado da partida
export type MatchLiveState = {
  id: number;
  club_id: number;
  match_id: string;
  is_live: boolean;
  is_paused: boolean;
  is_finished: boolean;
  match_start_time: string | null;
  total_elapsed_seconds: number;
  pause_start_time: string | null;
  score_home: number;
  score_away: number;
  created_at: string;
  updated_at: string;
  // Campos calculados
  exists?: boolean;
  current_time_seconds?: number;
};

export type MatchLiveStateResponse = {
  exists: boolean;
  is_live: boolean;
  is_paused: boolean;
  is_finished: boolean;
  current_time_seconds: number;
  score_home: number;
  score_away: number;
  match_start_time?: string | null;
  total_elapsed_seconds?: number;
  pause_start_time?: string | null;
  created_at?: string;
  updated_at?: string;
};

// Função utilitária para formatar tempo em MM:SS
export function formatMatchTime(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
}

// Obter estado atual da partida
export async function getMatchLiveState(
  clubId: number,
  matchId: string
): Promise<MatchLiveStateResponse> {
  const { data, error } = await supabase.rpc('get_match_live_state', {
    p_club_id: clubId,
    p_match_id: matchId
  });

  if (error) {
    console.error("Erro ao obter estado da partida:", error);
    throw new Error(`Erro ao obter estado da partida: ${error.message}`);
  }

  return data as MatchLiveStateResponse;
}

// Iniciar partida
export async function startMatchLive(
  clubId: number,
  matchId: string
): Promise<MatchLiveState> {
  const { data, error } = await supabase.rpc('start_match_live', {
    p_club_id: clubId,
    p_match_id: matchId
  });

  if (error) {
    console.error("Erro ao iniciar partida:", error);
    throw new Error(`Erro ao iniciar partida: ${error.message}`);
  }

  return data as MatchLiveState;
}

// Pausar partida
export async function pauseMatchLive(
  clubId: number,
  matchId: string
): Promise<MatchLiveState> {
  const { data, error } = await supabase.rpc('pause_match_live', {
    p_club_id: clubId,
    p_match_id: matchId
  });

  if (error) {
    console.error("Erro ao pausar partida:", error);
    throw new Error(`Erro ao pausar partida: ${error.message}`);
  }

  return data as MatchLiveState;
}

// Retomar partida
export async function resumeMatchLive(
  clubId: number,
  matchId: string
): Promise<MatchLiveState> {
  const { data, error } = await supabase.rpc('resume_match_live', {
    p_club_id: clubId,
    p_match_id: matchId
  });

  if (error) {
    console.error("Erro ao retomar partida:", error);
    throw new Error(`Erro ao retomar partida: ${error.message}`);
  }

  return data as MatchLiveState;
}

// Finalizar partida
export async function finishMatchLive(
  clubId: number,
  matchId: string
): Promise<MatchLiveState> {
  const { data, error } = await supabase.rpc('finish_match_live', {
    p_club_id: clubId,
    p_match_id: matchId
  });

  if (error) {
    console.error("Erro ao finalizar partida:", error);
    throw new Error(`Erro ao finalizar partida: ${error.message}`);
  }

  return data as MatchLiveState;
}

// Atualizar placar
export async function updateMatchScore(
  clubId: number,
  matchId: string,
  scoreHome: number,
  scoreAway: number
): Promise<MatchLiveState> {
  const { data, error } = await supabase.rpc('update_match_score', {
    p_club_id: clubId,
    p_match_id: matchId,
    p_score_home: scoreHome,
    p_score_away: scoreAway
  });

  if (error) {
    console.error("Erro ao atualizar placar:", error);
    throw new Error(`Erro ao atualizar placar: ${error.message}`);
  }

  return data as MatchLiveState;
}

// Hook personalizado para gerenciar estado da partida
export class MatchLiveStateManager {
  private clubId: number;
  private matchId: string;
  private syncInterval: NodeJS.Timeout | null = null;
  private onStateChange?: (state: MatchLiveStateResponse) => void;
  private lastSyncTime: number = 0;
  private syncFailureCount: number = 0;
  private maxSyncFailures: number = 3;
  private isOnline: boolean = navigator.onLine;

  constructor(
    clubId: number,
    matchId: string,
    onStateChange?: (state: MatchLiveStateResponse) => void
  ) {
    this.clubId = clubId;
    this.matchId = matchId;
    this.onStateChange = onStateChange;

    // Monitorar status de conexão
    this.setupConnectionMonitoring();
  }

  // Configurar monitoramento de conexão
  private setupConnectionMonitoring() {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.syncFailureCount = 0;
      console.log('Conexão restaurada - reiniciando sincronização');
      this.syncNow(); // Sincronizar imediatamente quando voltar online
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
      console.log('Conexão perdida - pausando sincronização');
    });
  }

  // Sincronização inteligente com retry e backoff
  private async performSync(): Promise<void> {
    if (!this.isOnline) {
      console.log('Offline - pulando sincronização');
      return;
    }

    try {
      const state = await getMatchLiveState(this.clubId, this.matchId);
      this.lastSyncTime = Date.now();
      this.syncFailureCount = 0;

      if (this.onStateChange) {
        this.onStateChange(state);
      }
    } catch (error) {
      this.syncFailureCount++;
      console.error(`Erro na sincronização (tentativa ${this.syncFailureCount}):`, error);

      // Se muitas falhas, aumentar intervalo ou parar temporariamente
      if (this.syncFailureCount >= this.maxSyncFailures) {
        console.warn('Muitas falhas de sincronização - pausando temporariamente');
        this.stopSync();

        // Tentar reconectar após 30 segundos
        setTimeout(() => {
          if (this.isOnline) {
            console.log('Tentando reestabelecer sincronização');
            this.startSync();
          }
        }, 30000);
      }
    }
  }

  // Iniciar sincronização automática
  startSync(intervalMs: number = 3000) {
    this.stopSync(); // Parar qualquer sincronização existente

    this.syncInterval = setInterval(() => {
      this.performSync();
    }, intervalMs);

    // Sincronizar imediatamente
    this.performSync();
  }

  // Sincronização manual/imediata
  async syncNow(): Promise<void> {
    await this.performSync();
  }

  // Parar sincronização
  stopSync() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
  }

  // Obter estado atual
  async getCurrentState(): Promise<MatchLiveStateResponse> {
    return await getMatchLiveState(this.clubId, this.matchId);
  }

  // Iniciar partida
  async start(): Promise<MatchLiveState> {
    const result = await startMatchLive(this.clubId, this.matchId);
    // Sincronizar estado após ação
    if (this.onStateChange) {
      const state = await this.getCurrentState();
      this.onStateChange(state);
    }
    return result;
  }

  // Pausar partida
  async pause(): Promise<MatchLiveState> {
    const result = await pauseMatchLive(this.clubId, this.matchId);
    // Sincronizar estado após ação
    if (this.onStateChange) {
      const state = await this.getCurrentState();
      this.onStateChange(state);
    }
    return result;
  }

  // Retomar partida
  async resume(): Promise<MatchLiveState> {
    const result = await resumeMatchLive(this.clubId, this.matchId);
    // Sincronizar estado após ação
    if (this.onStateChange) {
      const state = await this.getCurrentState();
      this.onStateChange(state);
    }
    return result;
  }

  // Finalizar partida
  async finish(): Promise<MatchLiveState> {
    const result = await finishMatchLive(this.clubId, this.matchId);
    this.stopSync(); // Parar sincronização quando finalizar
    // Sincronizar estado após ação
    if (this.onStateChange) {
      const state = await this.getCurrentState();
      this.onStateChange(state);
    }
    return result;
  }

  // Atualizar placar
  async updateScore(scoreHome: number, scoreAway: number): Promise<MatchLiveState> {
    const result = await updateMatchScore(this.clubId, this.matchId, scoreHome, scoreAway);
    // Sincronizar estado após ação
    if (this.onStateChange) {
      const state = await this.getCurrentState();
      this.onStateChange(state);
    }
    return result;
  }

  // Obter informações de sincronização
  getSyncInfo() {
    return {
      lastSyncTime: this.lastSyncTime,
      syncFailureCount: this.syncFailureCount,
      isOnline: this.isOnline,
      isActive: this.syncInterval !== null
    };
  }

  // Cleanup
  destroy() {
    this.stopSync();
    // Remover listeners de conexão
    window.removeEventListener('online', this.setupConnectionMonitoring);
    window.removeEventListener('offline', this.setupConnectionMonitoring);
  }
}

// Função utilitária para criar um manager
export function createMatchLiveStateManager(
  clubId: number,
  matchId: string,
  onStateChange?: (state: MatchLiveStateResponse) => void
): MatchLiveStateManager {
  return new MatchLiveStateManager(clubId, matchId, onStateChange);
}
