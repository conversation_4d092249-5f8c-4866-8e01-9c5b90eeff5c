import { create } from "zustand";
import {
  getTrainingLocations,
  createTrainingLocation,
  updateTrainingLocation,
  deleteTrainingLocation,
  saveLocationHours,
  getLocationHours,
  type TrainingLocation,
  type TrainingLocationHour
} from "@/api/trainingLocations";

interface TrainingLocationsState {
  locations: TrainingLocation[];
  loading: boolean;
  error: string | null;
  fetchLocations: (clubId: number) => Promise<void>;
  addLocation: (
    clubId: number,
    location: Omit<TrainingLocation, "id" | "club_id" | "created_at" | "updated_at">,
    hours: Omit<TrainingLocationHour, "id" | "location_id" | "created_at">[]
  ) => Promise<void>;
  updateLocation: (
    clubId: number,
    id: number,
    location: Partial<TrainingLocation>,
    hours: Omit<TrainingLocationHour, "id" | "location_id" | "created_at">[]
  ) => Promise<void>;
  removeLocation: (clubId: number, id: number) => Promise<void>;
  getHours: (locationId: number) => Promise<TrainingLocationHour[]>;
}

export const useTrainingLocationsStore = create<TrainingLocationsState>((set) => ({
  locations: [],
  loading: false,
  error: null,

  fetchLocations: async (clubId: number) => {
    set({ loading: true, error: null });
    try {
      const locations = await getTrainingLocations(clubId);
      set({ locations, loading: false });
    } catch (err: any) {
      set({ error: err.message || "Erro ao buscar locais", loading: false });
    }
  },

  addLocation: async (clubId, location, hours) => {
    set({ loading: true, error: null });
    try {
      const newLoc = await createTrainingLocation(clubId, location);
      await saveLocationHours(newLoc.id, hours);
      set((state) => ({ locations: [...state.locations, newLoc], loading: false }));
    } catch (err: any) {
      set({ error: err.message || "Erro ao criar local", loading: false });
    }
  },

  updateLocation: async (clubId, id, location, hours) => {
    set({ loading: true, error: null });
    try {
      const updated = await updateTrainingLocation(clubId, id, location);
      await saveLocationHours(updated.id, hours);
      set((state) => ({
        locations: state.locations.map((l) => (l.id === id ? updated : l)),
        loading: false
      }));
    } catch (err: any) {
      set({ error: err.message || "Erro ao atualizar local", loading: false });
    }
  },

  removeLocation: async (clubId, id) => {
    set({ loading: true, error: null });
    try {
      await deleteTrainingLocation(clubId, id);
      set((state) => ({ locations: state.locations.filter((l) => l.id !== id), loading: false }));
    } catch (err: any) {
      set({ error: err.message || "Erro ao excluir local", loading: false });
    }
  },

  getHours: async (locationId: number) => {
    return getLocationHours(locationId);
  }
}));