import { supabase } from "@/integrations/supabase/client";
import type { Database } from "@/integrations/supabase/types";

// Tipos
export type CategoryMapping = Database["public"]["Tables"]["category_mappings"]["Row"];

// Tipo para posição no mapeamento
export interface MappingPosition {
  player_id: string;
  player_name: string;
  player_nickname?: string;
  player_number: number;
  player_birthdate?: string;
  player_image?: string;
}

// Tipo para o mapeamento completo
export interface FieldMapping {
  [positionKey: string]: MappingPosition | null;
}

/**
 * Busca todos os mapeamentos de uma categoria
 * @param clubId ID do clube
 * @param categoryId ID da categoria
 * @returns Lista de mapeamentos da categoria
 */
export async function getCategoryMappings(clubId: number, categoryId: number): Promise<CategoryMapping[]> {
  const { data, error } = await supabase
    .from("category_mappings")
    .select("*")
    .eq("club_id", clubId)
    .eq("category_id", categoryId)
    .order("created_at", { ascending: false });

  if (error) {
    console.error(`Erro ao buscar mapeamentos da categoria ${categoryId}:`, error);
    throw new Error(`Erro ao buscar mapeamentos da categoria: ${error.message}`);
  }

  return data || [];
}

/**
 * Busca um mapeamento específico por ID
 * @param clubId ID do clube
 * @param mappingId ID do mapeamento
 * @returns Mapeamento encontrado
 */
export async function getCategoryMappingById(clubId: number, mappingId: number): Promise<CategoryMapping> {
  const { data, error } = await supabase
    .from("category_mappings")
    .select("*")
    .eq("club_id", clubId)
    .eq("id", mappingId)
    .single();

  if (error) {
    console.error(`Erro ao buscar mapeamento ${mappingId}:`, error);
    throw new Error(`Erro ao buscar mapeamento: ${error.message}`);
  }

  return data;
}

/**
 * Busca o mapeamento principal de uma categoria
 * @param clubId ID do clube
 * @param categoryId ID da categoria
 * @returns Mapeamento principal ou null se não existir
 */
export async function getMainCategoryMapping(clubId: number, categoryId: number): Promise<CategoryMapping | null> {
  const { data, error } = await supabase
    .from("category_mappings")
    .select("*")
    .eq("club_id", clubId)
    .eq("category_id", categoryId)
    .eq("name", "Mapeamento Principal")
    .single();

  if (error) {
    if (error.code === "PGRST116") {
      // Não encontrou - retorna null
      return null;
    }
    console.error(`Erro ao buscar mapeamento principal da categoria ${categoryId}:`, error);
    throw new Error(`Erro ao buscar mapeamento principal: ${error.message}`);
  }

  return data;
}

/**
 * Cria um novo mapeamento
 * @param clubId ID do clube
 * @param categoryId ID da categoria
 * @param name Nome do mapeamento
 * @param mapping Dados do mapeamento
 * @returns Mapeamento criado
 */
export async function createCategoryMapping(
  clubId: number,
  categoryId: number,
  name: string,
  mapping: FieldMapping
): Promise<CategoryMapping> {
  const { data, error } = await supabase
    .from("category_mappings")
    .insert({
      club_id: clubId,
      category_id: categoryId,
      name,
      mapping: mapping as any
    })
    .select()
    .single();

  if (error) {
    console.error("Erro ao criar mapeamento:", error);
    throw new Error(`Erro ao criar mapeamento: ${error.message}`);
  }

  return data;
}

/**
 * Atualiza um mapeamento existente
 * @param clubId ID do clube
 * @param mappingId ID do mapeamento
 * @param updates Dados para atualizar
 * @returns Mapeamento atualizado
 */
export async function updateCategoryMapping(
  clubId: number,
  mappingId: number,
  updates: Partial<Pick<CategoryMapping, "name" | "mapping">>
): Promise<CategoryMapping> {
  const { data, error } = await supabase
    .from("category_mappings")
    .update({
      ...updates,
      updated_at: new Date().toISOString()
    } as any)
    .eq("club_id", clubId)
    .eq("id", mappingId)
    .select()
    .single();

  if (error) {
    console.error(`Erro ao atualizar mapeamento ${mappingId}:`, error);
    throw new Error(`Erro ao atualizar mapeamento: ${error.message}`);
  }

  return data;
}

/**
 * Salva ou atualiza o mapeamento principal de uma categoria
 * @param clubId ID do clube
 * @param categoryId ID da categoria
 * @param mapping Dados do mapeamento
 * @returns Mapeamento salvo
 */
export async function saveMainCategoryMapping(
  clubId: number,
  categoryId: number,
  mapping: FieldMapping
): Promise<CategoryMapping> {
  // Verificar se já existe um mapeamento principal
  const existing = await getMainCategoryMapping(clubId, categoryId);

  if (existing) {
    // Atualizar mapeamento existente
    return updateCategoryMapping(clubId, existing.id, { mapping });
  } else {
    // Criar novo mapeamento principal
    return createCategoryMapping(clubId, categoryId, "Mapeamento Principal", mapping);
  }
}

/**
 * Deleta um mapeamento
 * @param clubId ID do clube
 * @param mappingId ID do mapeamento
 * @returns True se deletado com sucesso
 */
export async function deleteCategoryMapping(clubId: number, mappingId: number): Promise<boolean> {
  const { error } = await supabase
    .from("category_mappings")
    .delete()
    .eq("club_id", clubId)
    .eq("id", mappingId);

  if (error) {
    console.error(`Erro ao deletar mapeamento ${mappingId}:`, error);
    throw new Error(`Erro ao deletar mapeamento: ${error.message}`);
  }

  return true;
}

/**
 * Duplica um mapeamento existente
 * @param clubId ID do clube
 * @param mappingId ID do mapeamento a ser duplicado
 * @param newName Nome para o novo mapeamento
 * @returns Novo mapeamento criado
 */
export async function duplicateCategoryMapping(
  clubId: number,
  mappingId: number,
  newName: string
): Promise<CategoryMapping> {
  // Buscar o mapeamento original
  const original = await getCategoryMappingById(clubId, mappingId);

  // Criar novo mapeamento com os mesmos dados
  return createCategoryMapping(
    clubId,
    original.category_id,
    newName,
    original.mapping as FieldMapping
  );
}
