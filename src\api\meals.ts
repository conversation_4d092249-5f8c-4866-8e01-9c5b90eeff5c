import { supabase } from "@/integrations/supabase/client";
import type { Database } from "@/integrations/supabase/types";

// Tipos
export type MealType = Database["public"]["Tables"]["meal_types"]["Row"];
export type MealSession = Database["public"]["Tables"]["meal_sessions"]["Row"];
export type MealParticipant = Database["public"]["Tables"]["meal_participants"]["Row"];

// Tipos estendidos para incluir dados relacionados
export type MealSessionWithDetails = MealSession & {
  meal_type_name?: string;
  participant_count?: number;
  meal_types?: {
    id: number;
    location_id: number;
    name?: string;
    location?: string;
    address?: string;
    start_time?: string | null;
    end_time?: string | null;
  }[];
};

export type MealParticipantWithDetails = MealParticipant & {
  participant_name?: string;
  participant_nickname?: string;
  participant_role?: string;
  meal_type_id?: number;
  players?: {
    name: string;
    nickname?: string;
  };
  collaborators?: {
    full_name: string;
    role?: string;
  };
};

export interface MealSessionLocationInput {
  location_id: number;
  meal_type_id: number;
  start_time: string;
  end_time: string;
}

// ===== MEAL TYPES =====

export async function getMealTypes(clubId: number): Promise<MealType[]> {
  const { data, error } = await supabase
    .from("meal_types")
    .select("*")
    .eq("club_id", clubId as any)
    .order("name");

  if (error) {
    console.error("Erro ao buscar tipos de refeição:", error);
    throw new Error(`Erro ao buscar tipos de refeição: ${error.message}`);
  }

  return (data || []) as unknown as MealType[];
}

export async function createMealType(
  clubId: number,
  mealType: Omit<MealType, "id" | "club_id" | "created_at">
): Promise<MealType> {
  const { data, error } = await supabase
    .from("meal_types")
    .insert({
      ...mealType,
      club_id: clubId
    } as any)
    .select()
    .single();

  if (error) {
    console.error("Erro ao criar tipo de refeição:", error);
    throw new Error(`Erro ao criar tipo de refeição: ${error.message}`);
  }

  return data as unknown as MealType;
}

export async function updateMealType(
  clubId: number,
  id: number,
  mealType: Partial<MealType>
): Promise<MealType> {
  const { data, error } = await supabase
    .from("meal_types")
    .update(mealType as any)
    .eq("club_id", clubId as any)
    .eq("id", id as any)
    .select()
    .single();

  if (error) {
    console.error(`Erro ao atualizar tipo de refeição ${id}:`, error);
    throw new Error(`Erro ao atualizar tipo de refeição: ${error.message}`);
  }

  return data as unknown as MealType;
}

export async function deleteMealType(clubId: number, id: number): Promise<boolean> {
  // Verificar se há sessões de refeição usando este tipo
  const { data: sessions, error: sessionsError } = await supabase
    .from('meal_session_locations')
    .select('meal_session_id')
    .eq('meal_type_id', id as any)
    .limit(1);

  if (sessionsError) {
    console.error("Erro ao verificar sessões de refeição:", sessionsError);
    throw new Error(`Erro ao verificar sessões de refeição: ${sessionsError.message}`);
  }

  if (sessions && sessions.length > 0) {
    throw new Error("Não é possível excluir este tipo de refeição pois há sessões associadas a ele.");
  }

  const { error } = await supabase
    .from("meal_types")
    .delete()
    .eq("club_id", clubId as any)
    .eq("id", id as any);

  if (error) {
    console.error(`Erro ao excluir tipo de refeição ${id}:`, error);
    throw new Error(`Erro ao excluir tipo de refeição: ${error.message}`);
  }

  return true;
}

// ===== MEAL SESSIONS =====

export async function getMealSessions(clubId: number): Promise<MealSessionWithDetails[]> {
  const { data, error } = await supabase
    .from("meal_sessions")
    .select("*")
    .eq("club_id", clubId as any)
    .order("date", { ascending: false })
    .order("time", { ascending: false });

  if (error) {
    console.error("Erro ao buscar sessões de refeição:", error);
    throw new Error(`Erro ao buscar sessões de refeição: ${error.message}`);
  }

  // Buscar contagem de participantes para cada sessão
  const sessionIds = (data || []).map((s: any) => s.id);

  const { data: sessionTypes, error: typesError } = await supabase
    .from('meal_session_locations')
    .select('meal_session_id, meal_type_id, location_id, start_time, end_time, meal_types(name), meal_locations(name, address)')
    .in('meal_session_id', sessionIds as any);

  if (typesError) {
    console.error('Erro ao buscar tipos das sessões:', typesError);
  }

  const sessionsWithCount = await Promise.all(
    (data || []).map(async (session: any) => {
      const { data: participants, error: participantsError } = await supabase
        .from("meal_participants")
        .select("id")
        .eq("meal_session_id", session.id as any);

      if (participantsError) {
        console.error("Erro ao contar participantes:", participantsError);
      }

      const typesForSession = (sessionTypes || [])
        .filter(st => st.meal_session_id === session.id)
        .map(st => ({
          id: st.meal_type_id,
          location_id: st.location_id,
          start_time: st.start_time,
          end_time: st.end_time,
          name: st.meal_types?.name,
          location: st.meal_locations?.name,
          address: st.meal_locations?.address,
        }));

      return {
        ...session,
        meal_types: typesForSession,
        participant_count: participants?.length || 0
      };
    })
  );

  return sessionsWithCount as MealSessionWithDetails[];
}

export async function createMealSession(
  clubId: number,
  mealSession: Omit<MealSession, "id" | "club_id" | "created_at"> & { locations: MealSessionLocationInput[] }
): Promise<MealSession> {
  const { data, error } = await supabase
    .from("meal_sessions")
    .insert({
      ...mealSession,
      locations: undefined,
      club_id: clubId
    } as any)
    .select()
    .single();

  if (error) {
    console.error("Erro ao criar sessão de refeição:", error);
    throw new Error(`Erro ao criar sessão de refeição: ${error.message}`);
  }

  if (!data) {
    throw new Error('Erro desconhecido ao criar sessão');
  }

  if (mealSession.locations && mealSession.locations.length > 0) {
    const inserts = mealSession.locations.map(loc => ({
      meal_session_id: data.id,
      location_id: loc.location_id,
      meal_type_id: loc.meal_type_id,
      start_time: loc.start_time,
      end_time: loc.end_time,
      club_id: clubId,
    }));

    const { error: mtError } = await supabase
      .from('meal_session_locations')
      .insert(inserts as any);

    if (mtError) {
      console.error('Erro ao vincular locais à sessão:', mtError);
    }
  }

  return data as unknown as MealSession;
}

export async function updateMealSession(
  clubId: number,
  id: number,
  mealSession: Partial<MealSession> & { locations?: MealSessionLocationInput[] }
): Promise<MealSession> {
  const { data, error } = await supabase
    .from("meal_sessions")
    .update({ ...mealSession, locations: undefined } as any)
    .eq("club_id", clubId as any)
    .eq("id", id as any)
    .select()
    .single();

  if (error) {
    console.error(`Erro ao atualizar sessão de refeição ${id}:`, error);
    throw new Error(`Erro ao atualizar sessão de refeição: ${error.message}`);
  }

  if (!data) {
    throw new Error('Erro desconhecido ao atualizar sessão');
  }

  if (mealSession.locations) {
    await supabase
      .from('meal_session_locations')
      .delete()
      .eq('meal_session_id', data.id as any);

    if (mealSession.locations.length > 0) {
      const inserts = mealSession.locations.map(loc => ({
        meal_session_id: data.id,
        location_id: loc.location_id,
        meal_type_id: loc.meal_type_id,
        start_time: loc.start_time,
        end_time: loc.end_time,
        club_id: clubId,
      }));
      const { error: mtError } = await supabase
        .from('meal_session_locations')
        .insert(inserts as any);
      if (mtError) {
        console.error('Erro ao atualizar locais da sessão:', mtError);
      }
    }
  }

  return data as unknown as MealSession;
}

export async function deleteMealSession(clubId: number, id: number): Promise<boolean> {
  // Primeiro, excluir todos os participantes da sessão
  const { error: participantsError } = await supabase
    .from("meal_participants")
    .delete()
    .eq("club_id", clubId as any)
    .eq("meal_session_id", id as any);

  if (participantsError) {
    console.error("Erro ao excluir participantes da sessão:", participantsError);
    throw new Error(`Erro ao excluir participantes da sessão: ${participantsError.message}`);
  }

  await supabase
    .from('meal_session_locations')
    .delete()
    .eq('meal_session_id', id as any);

  // Depois, excluir a sessão
  const { error } = await supabase
    .from("meal_sessions")
    .delete()
    .eq("club_id", clubId as any)
    .eq("id", id as any);

  if (error) {
    console.error(`Erro ao excluir sessão de refeição ${id}:`, error);
    throw new Error(`Erro ao excluir sessão de refeição: ${error.message}`);
  }

  return true;
}

// ===== MEAL PARTICIPANTS =====

export async function getMealParticipants(clubId: number, mealSessionId: number): Promise<MealParticipantWithDetails[]> {
  // Buscar participantes sem joins primeiro
  const { data: participants, error } = await supabase
    .from("meal_participants")
    .select("*")
    .eq("club_id", clubId as any)
    .eq("meal_session_id", mealSessionId as any)
    .order("participant_type")
    .order("created_at");

  if (error) {
    console.error("Erro ao buscar participantes da refeição:", error);
    throw new Error(`Erro ao buscar participantes da refeição: ${error.message}`);
  }

  if (!participants || participants.length === 0) {
    return [];
  }

  // Buscar dados dos jogadores e colaboradores separadamente
  const participantsData = participants as any[];
  const playerIds = participantsData
    .filter(p => p.participant_type === 'player')
    .map(p => p.player_uuid);

  const collaboratorIds = participantsData
    .filter(p => p.participant_type === 'collaborator')
    .map(p => p.participant_id);

  // Buscar dados dos jogadores
  let playersData: any[] = [];
  if (playerIds.length > 0) {
    const { data: players, error: playersError } = await supabase
      .from("players")
      .select("id, name, nickname")
      .eq("club_id", clubId as any)
      .in("id", playerIds as any);

    if (playersError) {
      console.error("Erro ao buscar dados dos jogadores:", playersError);
    } else {
      playersData = players || [];
    }
  }

  // Buscar dados dos colaboradores
  let collaboratorsData: any[] = [];
  if (collaboratorIds.length > 0) {
    const { data: collaborators, error: collaboratorsError } = await supabase
      .from("collaborators")
      .select("id, full_name, role")
      .eq("club_id", clubId as any)
      .in("id", collaboratorIds as any);

    if (collaboratorsError) {
      console.error("Erro ao buscar dados dos colaboradores:", collaboratorsError);
    } else {
      collaboratorsData = collaborators || [];
    }
  }

  // Combinar os dados
  return participantsData.map((participant: any) => {
    let participantData: any = {};

    if (participant.participant_type === 'player') {
      const player = playersData.find(p => p.id === participant.player_uuid);
      participantData = {
        participant_name: player?.name || 'Jogador não encontrado',
        participant_nickname: player?.nickname,
        participant_role: undefined
      };
    } else if (participant.participant_type === 'collaborator') {
      const collaborator = collaboratorsData.find(c => c.id.toString() === participant.participant_id);
      participantData = {
        participant_name: collaborator?.full_name || 'Colaborador não encontrado',
        participant_nickname: undefined,
        participant_role: collaborator?.role
      };
    }

    return {
      ...participant,
      ...participantData
    };
  }) as MealParticipantWithDetails[];
}

export async function addMealParticipant(
  clubId: number,
  mealSessionId: number,
  participantId: string,
  participantType: 'player' | 'collaborator',
  mealTypeId: number
): Promise<MealParticipant> {
  // Verificar se já existe um registro idêntico
  const checkQuery = participantType === 'player'
    ? `
        SELECT * FROM meal_participants 
        WHERE club_id = ${clubId}
          AND meal_session_id = ${mealSessionId}
          AND player_uuid = '${participantId}'
          AND participant_type = '${participantType}'
          AND meal_type_id = ${mealTypeId}
        LIMIT 1;
      `
    : `
        SELECT * FROM meal_participants 
        WHERE club_id = ${clubId}
          AND meal_session_id = ${mealSessionId}
          AND participant_id = '${participantId}'
          AND participant_type = '${participantType}'
          AND meal_type_id = ${mealTypeId}
        LIMIT 1;
      `;

  // Verificar se o participante já existe
  console.log('Verificando participante existente com query:', checkQuery);
  
  // Usar uma abordagem diferente para verificar se o participante existe
  let query = supabase
    .from('meal_participants')
    .select('*', { count: 'exact', head: true });

  // Adicionar condições com tipos explícitos
  query = query
    .eq('club_id', clubId as any)
    .eq('meal_session_id', mealSessionId as any)
    .eq('meal_type_id', mealTypeId as any)
    .eq('participant_type', participantType as any);

  // Adicionar condição baseada no tipo de participante
  if (participantType === 'player') {
    query = query.eq('player_uuid', participantId as any);
  } else {
    query = query.eq('participant_id', participantId as any);
  }

  const { count, error: countError } = await query;

  console.log('Resultado da contagem de participantes existentes:', { count, countError });

  if (countError) {
    console.error('Erro ao verificar participante existente:', countError);
    throw new Error('Erro ao verificar participante existente');
  }

  if (count && count > 0) {
    console.log('Participante já existe na refeição');
    throw new Error("Este participante já está adicionado a este tipo de refeição.");
  }

  const insertData = {
    club_id: clubId,
    meal_session_id: mealSessionId,
    participant_type: participantType,
    meal_type_id: mealTypeId,
    signed: false,
    participant_id: participantType === 'collaborator' ? participantId : null,
    player_uuid: participantType === 'player' ? participantId : null,
  };

  const { data, error: insertError } = await supabase
    .from('meal_participants')
    .insert(insertData as any)
    .select()
    .single();

  if (insertError) {
    console.error('Erro ao adicionar participante:', insertError);
    throw new Error(`Erro ao adicionar participante: ${insertError.message}`);
  }

  return data as unknown as MealParticipant;
}

export async function removeMealParticipant(clubId: number, participantId: number): Promise<boolean> {
  const { error } = await supabase
    .from("meal_participants")
    .delete()
    .eq("club_id", clubId as any)
    .eq("id", participantId as any);

  if (error) {
    console.error(`Erro ao remover participante ${participantId}:`, error);
    throw new Error(`Erro ao remover participante: ${error.message}`);
  }

  return true;
}

export async function updateMealParticipantSignature(
  clubId: number,
  participantId: number,
  signed: boolean
): Promise<MealParticipant> {
  const { data, error } = await supabase
    .from("meal_participants")
    .update({ signed } as any)
    .eq("club_id", clubId as any)
    .eq("id", participantId as any)
    .select()
    .single();

  if (error) {
    console.error(`Erro ao atualizar assinatura do participante ${participantId}:`, error);
    throw new Error(`Erro ao atualizar assinatura do participante: ${error.message}`);
  }

  return data as unknown as MealParticipant;
}

// Nova função para buscar participantes por tipo de refeição
export async function getMealParticipantsByType(
  clubId: number,
  mealSessionId: number,
  mealTypeId?: number
): Promise<MealParticipantWithDetails[]> {
  let query = supabase
    .from("meal_participants")
    .select("*")
    .eq("club_id", clubId as any)
    .eq("meal_session_id", mealSessionId as any)
    .order("participant_type")
    .order("created_at");

  if (mealTypeId) {
    query = query.eq("meal_type_id", mealTypeId as any);
  }

  const { data: participants, error } = await query;

  if (error) {
    console.error("Erro ao buscar participantes da refeição por tipo:", error);
    throw new Error(`Erro ao buscar participantes da refeição: ${error.message}`);
  }

  if (!participants || participants.length === 0) {
    return [];
  }

  // Buscar dados dos jogadores e colaboradores separadamente
  const participantsData = participants as any[];
  const playerIds = participantsData
    .filter(p => p.participant_type === 'player')
    .map(p => p.player_uuid);

  const collaboratorIds = participantsData
    .filter(p => p.participant_type === 'collaborator')
    .map(p => p.participant_id);

  // Buscar dados dos jogadores
  let playersData: any[] = [];
  if (playerIds.length > 0) {
    const { data: players, error: playersError } = await supabase
      .from("players")
      .select("id, name, nickname")
      .eq("club_id", clubId as any)
      .in("id", playerIds as any);

    if (playersError) {
      console.error("Erro ao buscar dados dos jogadores:", playersError);
    } else {
      playersData = players || [];
    }
  }

  // Buscar dados dos colaboradores
  let collaboratorsData: any[] = [];
  if (collaboratorIds.length > 0) {
    const { data: collaborators, error: collaboratorsError } = await supabase
      .from("collaborators")
      .select("id, full_name, role")
      .eq("club_id", clubId as any)
      .in("id", collaboratorIds as any);

    if (collaboratorsError) {
      console.error("Erro ao buscar dados dos colaboradores:", collaboratorsError);
    } else {
      collaboratorsData = collaborators || [];
    }
  }

  // Combinar os dados
  return participantsData.map((participant: any) => {
    let participantData: any = {};

    if (participant.participant_type === 'player') {
      const player = playersData.find(p => p.id === participant.player_uuid);
      participantData = {
        participant_name: player?.name || 'Jogador não encontrado',
        participant_nickname: player?.nickname,
        participant_role: undefined
      };
    } else if (participant.participant_type === 'collaborator') {
      const collaborator = collaboratorsData.find(c => c.id.toString() === participant.participant_id);
      participantData = {
        participant_name: collaborator?.full_name || 'Colaborador não encontrado',
        participant_nickname: undefined,
        participant_role: collaborator?.role
      };
    }

    return {
      ...participant,
      ...participantData
    };
  }) as MealParticipantWithDetails[];
}

// Nova função para buscar todos os usuários ativos do clube (jogadores e colaboradores)
export async function getClubActiveUsers(clubId: number): Promise<AccommodationUser[]> {
  try {
    // Buscar jogadores ativos
    const { data: players, error: playersError } = await supabase
      .from("players")
      .select("id, name, nickname, status")
      .eq("club_id", clubId as any)
      .not("status", "eq", "inativo");

    if (playersError) {
      console.error("Erro ao buscar jogadores:", playersError);
      throw new Error(`Erro ao buscar jogadores: ${playersError.message}`);
    }

    // Buscar colaboradores ativos
    const { data: collaborators, error: collaboratorsError } = await supabase
      .from("collaborators")
      .select("id, full_name, role, status")
      .eq("club_id", clubId as any)
      .or("status.is.null,status.neq.inativo");

    if (collaboratorsError) {
      console.error("Erro ao buscar colaboradores:", collaboratorsError);
      throw new Error(`Erro ao buscar colaboradores: ${collaboratorsError.message}`);
    }

    // Converter para formato unificado
    const users: AccommodationUser[] = [
      ...(players || []).map((player: any) => ({
        id: player.id,
        name: player.name,
        nickname: player.nickname,
        type: 'player' as const,
        image: undefined
      })),
      ...(collaborators || []).map((collaborator: any) => ({
        id: collaborator.id.toString(),
        name: collaborator.full_name,
        role: collaborator.role,
        type: 'collaborator' as const,
        image: undefined
      }))
    ];

    return users;
  } catch (error) {
    console.error("Erro ao buscar usuários ativos do clube:", error);
    throw error;
  }
}

// Interface para usuários do clube (movida para o topo do arquivo)
export interface AccommodationUser {
  id: string;
  name: string;
  nickname?: string;
  role?: string;
  type: 'player' | 'collaborator';
  image?: string;
}

// Interface simplificada para refeições atribuídas a jogadores
export interface PlayerMeal {
  id: number;
  meal_session_id: number | null;
  meal_type_name: string | null;
  meal_type_location: string | null;
  meal_type_address: string | null;
  date: string;
  meal_time: string;
}

// Busca refeições futuras de um jogador
export async function getPlayerUpcomingMeals(
  clubId: number,
  playerId: string
): Promise<PlayerMeal[]> {
  try {
    console.log('Buscando refeições para:', { clubId, playerId });
    // A data de "hoje" precisa considerar o fuso UTC-3 (horário de Brasília)
    const today = new Date()
      .toLocaleDateString('en-CA', { timeZone: 'America/Sao_Paulo' });

    // Usar consulta SQL direta para evitar problemas de tipagem
    const { data, error } = await supabase.rpc('get_player_upcoming_meals', {
      p_club_id: clubId,
      p_player_id: playerId,
      p_today: today
    });

    if (error) {
      console.error('Erro ao buscar refeições do jogador:', error);
      // Se der erro, retornar array vazio para não quebrar a UI
      return [];
    }

    console.log('Refeições encontradas:', data);
    
    // Mapear os dados para o tipo PlayerMeal
    const meals: PlayerMeal[] = (Array.isArray(data) ? data : []).map((item: any) => ({
      id: item.id || 0,
      meal_session_id: item.meal_session_id || null,
      meal_type_name: item.meal_type_name || null,
      meal_type_location: item.meal_type_location || null,
      meal_type_address: item.meal_type_address || null,
      date: item.date || '',
      meal_time: item.meal_time || ''
    }));

    return meals;
  } catch (error) {
    console.error('Erro inesperado ao buscar refeições do jogador:', error);
    return [];
  }
}