import React, { forwardRef, useCallback, useRef, useState, useEffect } from 'react';
import { useDrop } from 'react-dnd';
import { TrainingElement } from './InteractiveTrainingBuilder';
import { DraggableElement } from './DraggableElement';

interface TrainingFieldProps {
  elements: TrainingElement[];
  selectedElements: string[];
  zoom: number;
  showGrid: boolean;
  showPlayerNames: boolean;
  onElementUpdate: (id: string, updates: Partial<TrainingElement>) => void;
  onElementSelect: (id: string, multiSelect?: boolean) => void;
  onElementDelete: (id: string) => void;
  drawingMode: 'select' | 'draw' | 'erase';
}

export const TrainingField = forwardRef<HTMLDivElement, TrainingFieldProps>(
  ({
    elements,
    selectedElements,
    zoom,
    showGrid,
    showPlayerNames,
    onElementUpdate,
    onElementSelect,
    onElementDelete,
    drawingMode
  }, ref) => {
    const fieldRef = useRef<SVGSVGElement>(null);
    const [fieldDimensions, setFieldDimensions] = useState({ width: 800, height: 600 });
    const [isDrawing, setIsDrawing] = useState(false);
    const [drawingPath, setDrawingPath] = useState<string>('');

    // Dimensões reais de um campo de futebol (em metros)
    const FIELD_LENGTH = 105; // metros
    const FIELD_WIDTH = 68; // metros
    const GOAL_WIDTH = 7.32; // metros
    const GOAL_DEPTH = 2.44; // metros
    const PENALTY_AREA_LENGTH = 16.5; // metros
    const PENALTY_AREA_WIDTH = 40.32; // metros
    const GOAL_AREA_LENGTH = 5.5; // metros
    const GOAL_AREA_WIDTH = 18.32; // metros
    const CENTER_CIRCLE_RADIUS = 9.15; // metros
    const PENALTY_SPOT_DISTANCE = 11; // metros

    // Conversão de metros para pixels
    const metersToPixels = useCallback((meters: number, isWidth = false) => {
      const baseSize = isWidth ? fieldDimensions.width : fieldDimensions.height;
      const fieldSize = isWidth ? FIELD_WIDTH : FIELD_LENGTH;
      return (meters / fieldSize) * baseSize;
    }, [fieldDimensions]);

    // Drop zone para elementos arrastados da toolbar
    const [{ isOver }, drop] = useDrop({
      accept: ['cone', 'ball', 'goal', 'player', 'marker'],
      drop: (item: { type: string; properties?: any }, monitor) => {
        const offset = monitor.getClientOffset();
        const fieldRect = fieldRef.current?.getBoundingClientRect();
        
        if (offset && fieldRect) {
          const x = ((offset.x - fieldRect.left) / fieldRect.width) * 100;
          const y = ((offset.y - fieldRect.top) / fieldRect.height) * 100;
          
          // Criar novo elemento na posição do drop
          const newElement: Omit<TrainingElement, 'id'> = {
            type: item.type as any,
            position: { x: Math.max(0, Math.min(100, x)), y: Math.max(0, Math.min(100, y)) },
            properties: item.properties || {}
          };
          
          // Aqui você chamaria onAddElement se tivesse acesso
          console.log('Novo elemento:', newElement);
        }
      },
      collect: (monitor) => ({
        isOver: monitor.isOver(),
      }),
    });

    // Handlers para desenho livre
    const handleMouseDown = useCallback((e: React.MouseEvent<SVGSVGElement>) => {
      if (drawingMode !== 'draw') return;
      
      const rect = fieldRef.current?.getBoundingClientRect();
      if (!rect) return;
      
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      
      setIsDrawing(true);
      setDrawingPath(`M ${x} ${y}`);
    }, [drawingMode]);

    const handleMouseMove = useCallback((e: React.MouseEvent<SVGSVGElement>) => {
      if (!isDrawing || drawingMode !== 'draw') return;
      
      const rect = fieldRef.current?.getBoundingClientRect();
      if (!rect) return;
      
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      
      setDrawingPath(prev => `${prev} L ${x} ${y}`);
    }, [isDrawing, drawingMode]);

    const handleMouseUp = useCallback(() => {
      if (isDrawing) {
        setIsDrawing(false);
        // Aqui você salvaria o desenho
        console.log('Desenho finalizado:', drawingPath);
        setDrawingPath('');
      }
    }, [isDrawing, drawingPath]);

    // Atualizar dimensões do campo baseado no container
    useEffect(() => {
      const updateDimensions = () => {
        if (ref && 'current' in ref && ref.current) {
          const rect = ref.current.getBoundingClientRect();
          const aspectRatio = FIELD_LENGTH / FIELD_WIDTH;
          let width = rect.width - 32; // padding
          let height = width / aspectRatio;
          
          if (height > rect.height - 32) {
            height = rect.height - 32;
            width = height * aspectRatio;
          }
          
          setFieldDimensions({ width, height });
        }
      };

      updateDimensions();
      window.addEventListener('resize', updateDimensions);
      return () => window.removeEventListener('resize', updateDimensions);
    }, [ref]);

    return (
      <div 
        ref={ref}
        className={`w-full h-full flex items-center justify-center p-4 ${
          isOver ? 'bg-primary/5' : 'bg-muted/20'
        }`}
        style={{ transform: `scale(${zoom})`, transformOrigin: 'center' }}
      >
        <svg
          ref={(node) => {
            fieldRef.current = node;
            drop(node);
          }}
          width={fieldDimensions.width}
          height={fieldDimensions.height}
          viewBox={`0 0 ${fieldDimensions.width} ${fieldDimensions.height}`}
          className="border border-green-600 bg-green-100 cursor-crosshair"
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
        >
          {/* Definições para padrões */}
          <defs>
            {showGrid && (
              <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#10b981" strokeWidth="0.5" opacity="0.3"/>
              </pattern>
            )}
          </defs>

          {/* Grade de fundo */}
          {showGrid && (
            <rect width="100%" height="100%" fill="url(#grid)" />
          )}

          {/* Campo de futebol */}
          <g stroke="#10b981" strokeWidth="2" fill="none">
            {/* Perímetro do campo */}
            <rect 
              x="0" 
              y="0" 
              width={fieldDimensions.width} 
              height={fieldDimensions.height}
              className="stroke-green-700"
            />
            
            {/* Linha central */}
            <line 
              x1={fieldDimensions.width / 2} 
              y1="0" 
              x2={fieldDimensions.width / 2} 
              y2={fieldDimensions.height}
            />
            
            {/* Círculo central */}
            <circle 
              cx={fieldDimensions.width / 2} 
              cy={fieldDimensions.height / 2} 
              r={metersToPixels(CENTER_CIRCLE_RADIUS)}
            />
            
            {/* Ponto central */}
            <circle 
              cx={fieldDimensions.width / 2} 
              cy={fieldDimensions.height / 2} 
              r="2" 
              fill="#10b981"
            />

            {/* Área penal esquerda */}
            <rect 
              x="0" 
              y={(fieldDimensions.height - metersToPixels(PENALTY_AREA_WIDTH, true)) / 2}
              width={metersToPixels(PENALTY_AREA_LENGTH)} 
              height={metersToPixels(PENALTY_AREA_WIDTH, true)}
            />
            
            {/* Área penal direita */}
            <rect 
              x={fieldDimensions.width - metersToPixels(PENALTY_AREA_LENGTH)} 
              y={(fieldDimensions.height - metersToPixels(PENALTY_AREA_WIDTH, true)) / 2}
              width={metersToPixels(PENALTY_AREA_LENGTH)} 
              height={metersToPixels(PENALTY_AREA_WIDTH, true)}
            />

            {/* Área do goleiro esquerda */}
            <rect 
              x="0" 
              y={(fieldDimensions.height - metersToPixels(GOAL_AREA_WIDTH, true)) / 2}
              width={metersToPixels(GOAL_AREA_LENGTH)} 
              height={metersToPixels(GOAL_AREA_WIDTH, true)}
            />
            
            {/* Área do goleiro direita */}
            <rect 
              x={fieldDimensions.width - metersToPixels(GOAL_AREA_LENGTH)} 
              y={(fieldDimensions.height - metersToPixels(GOAL_AREA_WIDTH, true)) / 2}
              width={metersToPixels(GOAL_AREA_LENGTH)} 
              height={metersToPixels(GOAL_AREA_WIDTH, true)}
            />

            {/* Pontos de pênalti */}
            <circle 
              cx={metersToPixels(PENALTY_SPOT_DISTANCE)} 
              cy={fieldDimensions.height / 2} 
              r="2" 
              fill="#10b981"
            />
            <circle 
              cx={fieldDimensions.width - metersToPixels(PENALTY_SPOT_DISTANCE)} 
              cy={fieldDimensions.height / 2} 
              r="2" 
              fill="#10b981"
            />

            {/* Arcos da área penal */}
            <path 
              d={`M ${metersToPixels(PENALTY_AREA_LENGTH)} ${fieldDimensions.height / 2 - metersToPixels(CENTER_CIRCLE_RADIUS)} 
                  A ${metersToPixels(CENTER_CIRCLE_RADIUS)} ${metersToPixels(CENTER_CIRCLE_RADIUS)} 0 0 1 
                  ${metersToPixels(PENALTY_AREA_LENGTH)} ${fieldDimensions.height / 2 + metersToPixels(CENTER_CIRCLE_RADIUS)}`}
            />
            <path 
              d={`M ${fieldDimensions.width - metersToPixels(PENALTY_AREA_LENGTH)} ${fieldDimensions.height / 2 - metersToPixels(CENTER_CIRCLE_RADIUS)} 
                  A ${metersToPixels(CENTER_CIRCLE_RADIUS)} ${metersToPixels(CENTER_CIRCLE_RADIUS)} 0 0 0 
                  ${fieldDimensions.width - metersToPixels(PENALTY_AREA_LENGTH)} ${fieldDimensions.height / 2 + metersToPixels(CENTER_CIRCLE_RADIUS)}`}
            />
          </g>

          {/* Gols */}
          <g stroke="#8b5cf6" strokeWidth="3" fill="none">
            {/* Gol esquerdo */}
            <rect 
              x={-metersToPixels(GOAL_DEPTH)} 
              y={(fieldDimensions.height - metersToPixels(GOAL_WIDTH, true)) / 2}
              width={metersToPixels(GOAL_DEPTH)} 
              height={metersToPixels(GOAL_WIDTH, true)}
            />
            
            {/* Gol direito */}
            <rect 
              x={fieldDimensions.width} 
              y={(fieldDimensions.height - metersToPixels(GOAL_WIDTH, true)) / 2}
              width={metersToPixels(GOAL_DEPTH)} 
              height={metersToPixels(GOAL_WIDTH, true)}
            />
          </g>

          {/* Desenho livre em progresso */}
          {isDrawing && drawingPath && (
            <path 
              d={drawingPath} 
              stroke="#ef4444" 
              strokeWidth="2" 
              fill="none"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          )}

          {/* Elementos do treino */}
          {elements.map((element) => (
            <DraggableElement
              key={element.id}
              element={element}
              isSelected={selectedElements.includes(element.id)}
              fieldDimensions={fieldDimensions}
              showPlayerNames={showPlayerNames}
              onUpdate={(updates) => onElementUpdate(element.id, updates)}
              onSelect={(multiSelect) => onElementSelect(element.id, multiSelect)}
              onDelete={() => onElementDelete(element.id)}
            />
          ))}
        </svg>
      </div>
    );
  }
);

TrainingField.displayName = 'TrainingField';
