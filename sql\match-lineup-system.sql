-- Sistema de Escalação por Partida
-- Este arquivo deve ser executado manualmente no Supabase

-- 1. <PERSON><PERSON><PERSON> tabela para escalações específicas por partida
CREATE TABLE IF NOT EXISTS match_lineups (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id) NOT NULL,
  match_id UUID REFERENCES matches(id) NOT NULL,
  formation TEXT NOT NULL DEFAULT '4-4-2',
  lineup JSONB NOT NULL DEFAULT '{}', -- Posições -> IDs dos jogadores titulares
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(club_id, match_id)
);

-- 2. <PERSON>riar tabela para squad completo da partida (titulares + reservas + staff)
CREATE TABLE IF NOT EXISTS match_squad (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id) NOT NULL,
  match_id UUID REFERENCES matches(id) NOT NULL,
  player_id UUID REFERENCES players(id),
  user_id UUID REFERENCES auth.users(id),
  role TEXT NOT NULL, -- 'starter', 'substitute', 'technical_staff', 'staff', 'executive'
  position TEXT, -- Para jogadores: posição específica na escalação
  jersey_number INTEGER, -- Número da camisa para a partida
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Garantir que cada pessoa só pode ter um papel por partida
  UNIQUE(club_id, match_id, player_id),
  UNIQUE(club_id, match_id, user_id),
  
  -- Garantir que pelo menos um dos IDs seja fornecido
  CONSTRAINT check_player_or_user CHECK (
    (player_id IS NOT NULL AND user_id IS NULL) OR 
    (player_id IS NULL AND user_id IS NOT NULL)
  )
);

-- 3. Criar tabela para substituições durante a partida
CREATE TABLE IF NOT EXISTS match_substitutions (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id) NOT NULL,
  match_id UUID REFERENCES matches(id) NOT NULL,
  player_out_id UUID REFERENCES players(id) NOT NULL,
  player_in_id UUID REFERENCES players(id) NOT NULL,
  minute INTEGER NOT NULL,
  reason TEXT, -- 'tactical', 'injury', 'disciplinary', etc.
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Criar tabela para minutos jogados (calculado automaticamente)
CREATE TABLE IF NOT EXISTS match_player_minutes (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id) NOT NULL,
  match_id UUID REFERENCES matches(id) NOT NULL,
  player_id UUID REFERENCES players(id) NOT NULL,
  minutes_played INTEGER NOT NULL DEFAULT 0,
  started BOOLEAN NOT NULL DEFAULT FALSE,
  substituted_in_minute INTEGER, -- Minuto que entrou (se foi substituto)
  substituted_out_minute INTEGER, -- Minuto que saiu (se foi substituído)
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(club_id, match_id, player_id)
);

-- 5. Criar índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_match_lineups_club_match ON match_lineups(club_id, match_id);
CREATE INDEX IF NOT EXISTS idx_match_squad_club_match ON match_squad(club_id, match_id);
CREATE INDEX IF NOT EXISTS idx_match_squad_role ON match_squad(club_id, match_id, role);
CREATE INDEX IF NOT EXISTS idx_match_substitutions_match ON match_substitutions(club_id, match_id);
CREATE INDEX IF NOT EXISTS idx_match_player_minutes_match ON match_player_minutes(club_id, match_id);

-- 6. Criar função para atualizar timestamp
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = NOW();
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 7. Criar triggers para atualizar timestamps
CREATE TRIGGER update_match_lineups_timestamp
BEFORE UPDATE ON match_lineups
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_match_player_minutes_timestamp
BEFORE UPDATE ON match_player_minutes
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

-- 8. Criar função para calcular minutos jogados automaticamente
CREATE OR REPLACE FUNCTION calculate_player_minutes(
  p_club_id INTEGER,
  p_match_id UUID,
  p_player_id UUID,
  p_match_duration INTEGER DEFAULT 90
) RETURNS INTEGER AS $$
DECLARE
  v_started BOOLEAN;
  v_sub_in_minute INTEGER;
  v_sub_out_minute INTEGER;
  v_minutes INTEGER := 0;
BEGIN
  -- Verificar se o jogador começou como titular
  SELECT EXISTS(
    SELECT 1 FROM match_squad 
    WHERE club_id = p_club_id 
    AND match_id = p_match_id 
    AND player_id = p_player_id 
    AND role = 'starter'
  ) INTO v_started;
  
  -- Buscar minuto de entrada (se foi substituto)
  SELECT minute INTO v_sub_in_minute
  FROM match_substitutions
  WHERE club_id = p_club_id 
  AND match_id = p_match_id 
  AND player_in_id = p_player_id;
  
  -- Buscar minuto de saída (se foi substituído)
  SELECT minute INTO v_sub_out_minute
  FROM match_substitutions
  WHERE club_id = p_club_id 
  AND match_id = p_match_id 
  AND player_out_id = p_player_id;
  
  -- Calcular minutos
  IF v_started THEN
    -- Começou jogando
    IF v_sub_out_minute IS NOT NULL THEN
      -- Foi substituído
      v_minutes := v_sub_out_minute;
    ELSE
      -- Jogou o jogo todo
      v_minutes := p_match_duration;
    END IF;
  ELSIF v_sub_in_minute IS NOT NULL THEN
    -- Entrou como substituto
    IF v_sub_out_minute IS NOT NULL THEN
      -- Entrou e depois saiu
      v_minutes := v_sub_out_minute - v_sub_in_minute;
    ELSE
      -- Entrou e jogou até o final
      v_minutes := p_match_duration - v_sub_in_minute;
    END IF;
  END IF;
  
  RETURN v_minutes;
END;
$$ LANGUAGE plpgsql;

-- 9. Criar função para atualizar minutos automaticamente após substituições
CREATE OR REPLACE FUNCTION update_player_minutes_after_substitution()
RETURNS TRIGGER AS $$
BEGIN
  -- Atualizar minutos do jogador que saiu
  INSERT INTO match_player_minutes (club_id, match_id, player_id, minutes_played, started, substituted_out_minute)
  VALUES (
    NEW.club_id, 
    NEW.match_id, 
    NEW.player_out_id,
    calculate_player_minutes(NEW.club_id, NEW.match_id, NEW.player_out_id),
    EXISTS(SELECT 1 FROM match_squad WHERE club_id = NEW.club_id AND match_id = NEW.match_id AND player_id = NEW.player_out_id AND role = 'starter'),
    NEW.minute
  )
  ON CONFLICT (club_id, match_id, player_id) 
  DO UPDATE SET 
    minutes_played = calculate_player_minutes(NEW.club_id, NEW.match_id, NEW.player_out_id),
    substituted_out_minute = NEW.minute,
    updated_at = NOW();
  
  -- Atualizar minutos do jogador que entrou
  INSERT INTO match_player_minutes (club_id, match_id, player_id, minutes_played, started, substituted_in_minute)
  VALUES (
    NEW.club_id, 
    NEW.match_id, 
    NEW.player_in_id,
    0, -- Será calculado quando o jogo terminar ou ele for substituído
    FALSE,
    NEW.minute
  )
  ON CONFLICT (club_id, match_id, player_id) 
  DO UPDATE SET 
    substituted_in_minute = NEW.minute,
    updated_at = NOW();
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 10. Criar trigger para atualizar minutos após substituições
CREATE TRIGGER trigger_update_player_minutes_after_substitution
AFTER INSERT ON match_substitutions
FOR EACH ROW
EXECUTE FUNCTION update_player_minutes_after_substitution();

-- 11. Habilitar RLS (Row Level Security) nas novas tabelas
ALTER TABLE match_lineups ENABLE ROW LEVEL SECURITY;
ALTER TABLE match_squad ENABLE ROW LEVEL SECURITY;
ALTER TABLE match_substitutions ENABLE ROW LEVEL SECURITY;
ALTER TABLE match_player_minutes ENABLE ROW LEVEL SECURITY;

-- 12. Criar políticas RLS
CREATE POLICY "Club members can view their own match lineups"
  ON match_lineups
  FOR SELECT
  USING (club_id = (auth.jwt() ->> 'club_id')::INTEGER);

CREATE POLICY "Club members can manage their own match lineups"
  ON match_lineups
  FOR ALL
  USING (club_id = (auth.jwt() ->> 'club_id')::INTEGER);

CREATE POLICY "Club members can view their own match squad"
  ON match_squad
  FOR SELECT
  USING (club_id = (auth.jwt() ->> 'club_id')::INTEGER);

CREATE POLICY "Club members can manage their own match squad"
  ON match_squad
  FOR ALL
  USING (club_id = (auth.jwt() ->> 'club_id')::INTEGER);

CREATE POLICY "Club members can view their own match substitutions"
  ON match_substitutions
  FOR SELECT
  USING (club_id = (auth.jwt() ->> 'club_id')::INTEGER);

CREATE POLICY "Club members can manage their own match substitutions"
  ON match_substitutions
  FOR ALL
  USING (club_id = (auth.jwt() ->> 'club_id')::INTEGER);

CREATE POLICY "Club members can view their own match player minutes"
  ON match_player_minutes
  FOR SELECT
  USING (club_id = (auth.jwt() ->> 'club_id')::INTEGER);

CREATE POLICY "Club members can manage their own match player minutes"
  ON match_player_minutes
  FOR ALL
  USING (club_id = (auth.jwt() ->> 'club_id')::INTEGER);
