import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "@/components/ui/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { getUserPermissions, updateUserPermissions } from "@/api/api";
import { Save } from "lucide-react";
import { PermissionControl } from "@/components/PermissionControl";
import { USER_PERMISSIONS } from "@/constants/permissions";
import {
  PERMISSION_GROUPS,
  ROLES,
  ROLE_PERMISSIONS
} from "@/constants/permissions";

interface UserPermissionsProps {
  userId: string;
  userName?: string;
}

export function UserPermissions({ userId, userName }: UserPermissionsProps) {
  const clubId = useCurrentClubId();
  const [role, setRole] = useState<string>("staff");
  const [permissions, setPermissions] = useState<Record<string, boolean>>({});
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // Carregar permissões do usuário
  useEffect(() => {
    const fetchPermissions = async () => {
      try {
        setLoading(true);
        const data = await getUserPermissions(clubId, userId);

        if (data) {
          setRole(data.role || "staff");
          setPermissions(data.permissions || {});
        } else {
          // Usuário sem permissões definidas, usar padrão
          setRole("staff");
          setPermissions(ROLE_PERMISSIONS.staff);
        }
      } catch (err: any) {
        console.error("Erro ao carregar permissões:", err);
        toast({
          title: "Erro",
          description: "Não foi possível carregar as permissões do usuário",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
        setHasChanges(false);
      }
    };

    if (userId) {
      fetchPermissions();
    }
  }, [clubId, userId]);

  // Função para aplicar um papel predefinido
  const applyRole = (newRole: string) => {
    setRole(newRole);
    setPermissions(ROLE_PERMISSIONS[newRole as keyof typeof ROLE_PERMISSIONS] || {});
    setHasChanges(true);
  };

  // Função para alternar uma permissão
  const togglePermission = (permission: string) => {
    setPermissions((prev) => ({
      ...prev,
      [permission]: !prev[permission],
    }));
    setHasChanges(true);
  };

  // Função para salvar as permissões
  const savePermissions = async () => {
    try {
      setSaving(true);
      await updateUserPermissions(clubId, userId, permissions);

      toast({
        title: "Sucesso",
        description: "Permissões atualizadas com sucesso",
      });

      setHasChanges(false);
    } catch (err: any) {
      console.error("Erro ao salvar permissões:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao salvar permissões",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>
          {userName ? `Permissões de ${userName}` : "Permissões de Usuário"}
        </CardTitle>
        <div className="flex items-center gap-2">
          <Select value={role} onValueChange={applyRole}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Selecione um papel" />
            </SelectTrigger>
            <SelectContent>
              {Object.entries(ROLES).map(([key, { label, description }]) => (
                <SelectItem key={key} value={key}>
                  <div>
                    <div>{label}</div>
                    <div className="text-xs text-muted-foreground">{description}</div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <PermissionControl permission={USER_PERMISSIONS.PERMISSIONS}>
            <Button
              onClick={savePermissions}
              disabled={!hasChanges || saving}
              className="ml-2"
            >
              <Save className="h-4 w-4 mr-1" />
              Salvar
            </Button>
          </PermissionControl>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        ) : (
          <div className="space-y-6">
            {Object.entries(PERMISSION_GROUPS).map(([groupKey, { label, permissions: groupPermissions }]) => (
              <div key={groupKey} className="border rounded-md p-4">
                <h3 className="text-lg font-medium mb-4">{label}</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {Object.entries(groupPermissions).map(([permKey, permLabel]) => (
                    <div key={permKey} className="flex items-center justify-between">
                      <Label htmlFor={permKey} className="cursor-pointer">
                        {permLabel}
                      </Label>
                      <Switch
                        id={permKey}
                        checked={!!permissions[permKey]}
                        onCheckedChange={() => togglePermission(permKey)}
                      />
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
