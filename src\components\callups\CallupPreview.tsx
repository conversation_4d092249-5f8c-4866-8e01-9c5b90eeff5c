import { useRef } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Callup, CallupPlayer, generateCallupPDF } from "@/api/callups";
import { ClubInfo } from "@/api/api";
import { filterPlayersByRoleCategory } from "@/utils/callupRoles";
import { formatCPF } from "@/utils/formatters";

interface CallupPlayerExtended extends Omit<CallupPlayer, 'player_name' | 'user_name'> {
  player_name?: string;
  user_name?: string;
  name?: string;
  player_number?: number;
}
import { Download, Printer } from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { toast } from "@/components/ui/use-toast";

interface CallupPreviewProps {
  callup: Partial<Callup>;
  players: CallupPlayerExtended[];
  clubInfo: ClubInfo | null;
  selectedFields: {
    players: boolean;
    schedule: boolean;
    hotel: boolean;
    notices: boolean;
    staff: boolean;
    technical: boolean;
    executive: boolean;
  };
}

export function CallupPreview({
  callup,
  players,
  clubInfo,
  selectedFields
}: CallupPreviewProps) {
  const previewRef = useRef<HTMLDivElement>(null);

  const handleGeneratePDF = async () => {
    if (!callup.id) {
      toast({
        title: "Erro",
        description: "Salve a convocação antes de gerar o PDF",
        variant: "destructive",
      });
      return;
    }

    try {
      await generateCallupPDF(callup.club_id || 0, callup.id);
      toast({
        title: "Sucesso",
        description: "PDF gerado com sucesso",
      });
    } catch (error) {
      console.error("Erro ao gerar PDF:", error);
      toast({
        title: "Erro",
        description: "Não foi possível gerar o PDF",
        variant: "destructive",
      });
    }
  };

  const handlePrint = async () => {
    if (!callup.id) {
      toast({
        title: "Erro",
        description: "Salve a convocação antes de imprimir",
        variant: "destructive",
      });
      return;
    }

    try {
      // Gerar o PDF e abrir em uma nova janela para impressão
      await generateCallupPDF(callup.club_id || 0, callup.id, true);
      toast({
        title: "Sucesso",
        description: "PDF aberto para impressão",
      });
    } catch (error) {
      console.error("Erro ao preparar impressão:", error);
      toast({
        title: "Erro",
        description: "Não foi possível preparar a impressão",
        variant: "destructive",
      });
    }
  };

  // Filtrar jogadores por papel
  const athletes = players.filter(p => ["Atleta", "Titular", "Reserva"].includes(p.role)) as CallupPlayerExtended[];
  const technicalStaff = players.filter(p =>
    ["Técnico", "Auxiliar Técnico", "Preparador Físico", "Fisioterapeuta", "Médico", "Preparador de goleiro"].includes(p.role)
  ) as CallupPlayerExtended[];
  const supportStaff = players.filter(p =>
    [
      "Massagista",
      "Roupeiro",
      "Comunicação",
      "Segurança",
      "Supervisor",
      "Nutricionista",
      "Fotógrafo",
      "Motorista",
      "Analista de Desempenho",
      "Assessor de Imprensa"
    ].includes(p.role)
  ) as CallupPlayerExtended[];
  const executives = players.filter(p =>
    ["Diretor", "Presidente"].includes(p.role)
  ) as CallupPlayerExtended[];

  return (
    <div className="space-y-4">
      <div className="flex justify-end space-x-2">
        <Button variant="outline" onClick={handlePrint}>
          <Printer className="h-4 w-4 mr-2" />
          Imprimir
        </Button>
        <Button onClick={handleGeneratePDF}>
          <Download className="h-4 w-4 mr-2" />
          Gerar PDF
        </Button>
      </div>

      <div ref={previewRef} className="bg-white p-6 rounded-lg shadow print:shadow-none">
        {/* Cabeçalho */}
        <div className="flex justify-between items-center mb-6 border-b pb-4">
          <div className="flex items-center">
            {callup.home_club_logo ? (
              <img
                src={callup.home_club_logo}
                alt="Clube Mandante"
                className="h-16 w-16 object-contain mr-4"
              />
            ) : clubInfo?.logo ? (
              <img
                src={clubInfo.logo}
                alt={clubInfo.name}
                className="h-16 w-16 object-contain mr-4"
              />
            ) : (
              <div className="h-16 w-16 bg-gray-200 flex items-center justify-center rounded-full mr-4">
                <span className="text-gray-500">Logo</span>
              </div>
            )}
            <div>
              <h1 className="text-2xl font-bold">{clubInfo?.name || "Clube"}</h1>
              <h2 className="text-xl">Convocação</h2>
            </div>
          </div>
          {callup.away_club_logo && (
            <img
              src={callup.away_club_logo}
              alt="Clube Visitante"
              className="h-16 w-16 object-contain"
            />
          )}
        </div>

        {/* Informações do Jogo */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-2">Informações do Jogo</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="border rounded-md p-3">
              <p className="font-medium">{callup.tournament_type || "Competição"}</p>
              <p>{callup.match_date ? format(new Date(callup.match_date), "dd 'de' MMMM 'de' yyyy", { locale: ptBR }) : "Data do jogo"}</p>
            </div>
            <div className="border rounded-md p-3">
              <p className="font-medium">Local</p>
              <p>{callup.match_location || "Local do jogo"}</p>
            </div>
            {callup.competition_image && (
              <div className="border rounded-md p-3 flex justify-center items-center">
                <img
                  src={callup.competition_image}
                  alt="Competição"
                  className="h-12 object-contain"
                />
              </div>
            )}
          </div>
        </div>

        {/* Imagens */}
        {(callup.uniform_image || callup.hotel_image || callup.bus_image) && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {callup.uniform_image && (
              <div className="border rounded-md p-3">
                <p className="font-medium text-center mb-2">Uniforme</p>
                <div className="flex justify-center">
                  <img
                    src={callup.uniform_image}
                    alt="Uniforme"
                    className="h-24 object-contain"
                  />
                </div>
              </div>
            )}
            {callup.hotel_image && (
              <div className="border rounded-md p-3">
                <p className="font-medium text-center mb-2">Hospedagem</p>
                <div className="flex justify-center">
                  <img
                    src={callup.hotel_image}
                    alt="Hotel"
                    className="h-24 object-contain"
                  />
                </div>
              </div>
            )}
            {callup.bus_image && (
              <div className="border rounded-md p-3">
                <p className="font-medium text-center mb-2">Transporte</p>
                <div className="flex justify-center">
                  <img
                    src={callup.bus_image}
                    alt="Ônibus"
                    className="h-24 object-contain"
                  />
                </div>
              </div>
            )}
          </div>
        )}

        {/* Conteúdo Principal */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          {/* Jogadores Convocados */}
          {selectedFields.players && athletes.length > 0 && (
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Jogadores Convocados</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-1">
                  {athletes.map((player, index) => (
                    <li
                    key={index}
                    className="border-b pb-1 flex justify-between"
                  >
                    <span>
                      {player.player_number ? `${player.player_number} - ` : ""}
                      {player.player_name || player.user_name || player.name}
                    </span>
                    {player.player_cpf && (
                      <span>{formatCPF(player.player_cpf)}</span>
                    )}
                  </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}

          {/* Programação do Jogo */}
          {selectedFields.schedule && callup.match_schedule && (
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Programação do Jogo</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="whitespace-pre-line">
                  {callup.match_schedule}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Controle de Hospedagem */}
          {selectedFields.hotel && callup.hotel_control && (
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Controle de Hospedagem</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="whitespace-pre-line">
                  {callup.hotel_control}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Comissão Técnica */}
          {selectedFields.technical && technicalStaff.length > 0 && (
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Comissão Técnica</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-1">
                  {technicalStaff.map((staff, index) => (
                    <li key={index} className="border-b pb-1">
                      <span className="font-medium">{staff.role}:</span> {staff.player_name || staff.user_name || staff.name}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}

          {/* Staff */}
          {selectedFields.staff && supportStaff.length > 0 && (
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Staff</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-1">
                  {supportStaff.map((staff, index) => (
                    <li key={index} className="border-b pb-1">
                      <span className="font-medium">{staff.role}:</span> {staff.player_name || staff.user_name || staff.name}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}

          {/* Diretoria Executiva */}
          {selectedFields.executive && executives.length > 0 && (
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Diretoria Executiva</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-1">
                  {executives.map((exec, index) => (
                    <li key={index} className="border-b pb-1">
                      <span className="font-medium">{exec.role}:</span> {exec.player_name || exec.user_name || exec.name}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Patrocinadores */}
        {(callup.sponsor_image1 || callup.sponsor_image2) && (
          <div className="flex justify-center items-center space-x-6 mt-8 border-t pt-4">
            {callup.sponsor_image1 && (
              <img
                src={callup.sponsor_image1}
                alt="Patrocinador 1"
                className="h-12 object-contain"
              />
            )}
            {callup.sponsor_image2 && (
              <img
                src={callup.sponsor_image2}
                alt="Patrocinador 2"
                className="h-12 object-contain"
              />
            )}
          </div>
        )}
      </div>
    </div>
  );
}
