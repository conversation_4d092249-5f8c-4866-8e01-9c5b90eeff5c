// vite.config.ts
import { defineConfig } from "file:///C:/Users/<USER>/Desktop/Projetos/game-day-nexus-platform-915e9c55/node_modules/vite/dist/node/index.js";
import react from "file:///C:/Users/<USER>/Desktop/Projetos/game-day-nexus-platform-915e9c55/node_modules/@vitejs/plugin-react-swc/index.mjs";
import path from "path";
import { componentTagger } from "file:///C:/Users/<USER>/Desktop/Projetos/game-day-nexus-platform-915e9c55/node_modules/lovable-tagger/dist/index.js";
var __vite_injected_original_dirname = "C:\\Users\\<USER>\\Desktop\\Projetos\\game-day-nexus-platform-915e9c55";
var vite_config_default = defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080
  },
  plugins: [
    react(),
    mode === "development" && componentTagger()
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__vite_injected_original_dirname, "./src")
    }
  },
  // Ensure environment variables are properly loaded
  define: {
    "import.meta.env.VITE_BREVO_API_KEY": JSON.stringify(process.env.VITE_BREVO_API_KEY || ""),
    "import.meta.env.VITE_BREVO_SENDER_NAME": JSON.stringify(process.env.VITE_BREVO_SENDER_NAME || "clubeFut"),
    "import.meta.env.VITE_BREVO_SENDER_EMAIL": JSON.stringify(process.env.VITE_BREVO_SENDER_EMAIL || "<EMAIL>")
  }
}));
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
