-- Correção das políticas RLS para category_mappings e adição de permissões específicas
-- Execute este arquivo no Supabase para corrigir os problemas

-- 1. Remover políticas antigas que estão causando problemas
DROP POLICY IF EXISTS "Club members can view their own category mappings" ON category_mappings;
DROP POLICY IF EXISTS "Club members can insert their own category mappings" ON category_mappings;
DROP POLICY IF EXISTS "Club members can update their own category mappings" ON category_mappings;
DROP POLICY IF EXISTS "Club members can delete their own category mappings" ON category_mappings;

-- 2. Criar função para obter club_id do usuário atual (se não existir)
CREATE OR REPLACE FUNCTION get_current_club_id()
RETURNS INTEGER AS $$
BEGIN
  -- Tentar obter club_id do JWT primeiro
  IF auth.jwt() ->> 'club_id' IS NOT NULL THEN
    RETURN (auth.jwt() ->> 'club_id')::INTEGER;
  END IF;
  
  -- Tentar obter do header customizado (para chamadas API)
  IF current_setting('request.headers', true)::json ->> 'x-club-id' IS NOT NULL THEN
    RETURN (current_setting('request.headers', true)::json ->> 'x-club-id')::INTEGER;
  END IF;
  
  -- Fallback: buscar na tabela club_members
  RETURN (
    SELECT club_id 
    FROM club_members 
    WHERE user_id = auth.uid() 
    AND status = 'ativo'
    LIMIT 1
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Criar novas políticas RLS usando a função get_current_club_id()
CREATE POLICY "Users can view category mappings from their club"
  ON category_mappings
  FOR SELECT
  USING (
    club_id = get_current_club_id()
    AND EXISTS (
      SELECT 1 FROM club_members 
      WHERE user_id = auth.uid() 
      AND club_id = category_mappings.club_id 
      AND status = 'ativo'
    )
  );

CREATE POLICY "Users can insert category mappings for their club"
  ON category_mappings
  FOR INSERT
  WITH CHECK (
    club_id = get_current_club_id()
    AND EXISTS (
      SELECT 1 FROM club_members 
      WHERE user_id = auth.uid() 
      AND club_id = category_mappings.club_id 
      AND status = 'ativo'
      AND role IN ('president', 'admin', 'collaborator')
    )
  );

CREATE POLICY "Users can update category mappings from their club"
  ON category_mappings
  FOR UPDATE
  USING (
    club_id = get_current_club_id()
    AND EXISTS (
      SELECT 1 FROM club_members 
      WHERE user_id = auth.uid() 
      AND club_id = category_mappings.club_id 
      AND status = 'ativo'
      AND role IN ('president', 'admin', 'collaborator')
    )
  )
  WITH CHECK (
    club_id = get_current_club_id()
    AND EXISTS (
      SELECT 1 FROM club_members 
      WHERE user_id = auth.uid() 
      AND club_id = category_mappings.club_id 
      AND status = 'ativo'
      AND role IN ('president', 'admin', 'collaborator')
    )
  );

CREATE POLICY "Users can delete category mappings from their club"
  ON category_mappings
  FOR DELETE
  USING (
    club_id = get_current_club_id()
    AND EXISTS (
      SELECT 1 FROM club_members 
      WHERE user_id = auth.uid() 
      AND club_id = category_mappings.club_id 
      AND status = 'ativo'
      AND role IN ('president', 'admin', 'collaborator')
    )
  );

-- 4. Adicionar nova permissão específica para mapeamento
-- Verificar se a permissão já existe antes de inserir
DO $$
BEGIN
  -- Adicionar permissão de mapeamento para presidentes
  IF NOT EXISTS (
    SELECT 1 FROM club_members 
    WHERE role = 'president' 
    AND permissions ? 'mapping.view'
  ) THEN
    UPDATE club_members 
    SET permissions = COALESCE(permissions, '{}'::jsonb) || '{"mapping.view": true, "mapping.create": true, "mapping.edit": true, "mapping.delete": true}'::jsonb
    WHERE role = 'president';
  END IF;

  -- Adicionar permissão de mapeamento para administradores
  IF NOT EXISTS (
    SELECT 1 FROM club_members 
    WHERE role = 'admin' 
    AND permissions ? 'mapping.view'
  ) THEN
    UPDATE club_members 
    SET permissions = COALESCE(permissions, '{}'::jsonb) || '{"mapping.view": true, "mapping.create": true, "mapping.edit": true, "mapping.delete": true}'::jsonb
    WHERE role = 'admin';
  END IF;
END $$;

-- 5. Criar índices para melhor performance das consultas RLS
CREATE INDEX IF NOT EXISTS idx_category_mappings_club_user_lookup 
ON category_mappings(club_id);

CREATE INDEX IF NOT EXISTS idx_club_members_user_club_status 
ON club_members(user_id, club_id, status) 
WHERE status = 'ativo';

-- 6. Comentários para documentação
COMMENT ON POLICY "Users can view category mappings from their club" ON category_mappings 
IS 'Permite que usuários vejam mapeamentos do seu clube';

COMMENT ON POLICY "Users can insert category mappings for their club" ON category_mappings 
IS 'Permite que presidentes, admins e colaboradores criem mapeamentos';

COMMENT ON POLICY "Users can update category mappings from their club" ON category_mappings 
IS 'Permite que presidentes, admins e colaboradores editem mapeamentos';

COMMENT ON POLICY "Users can delete category mappings from their club" ON category_mappings 
IS 'Permite que presidentes, admins e colaboradores deletem mapeamentos';

-- 7. Verificar se as políticas foram criadas corretamente
-- Esta query pode ser executada para verificar:
/*
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM pg_policies 
WHERE tablename = 'category_mappings'
ORDER BY policyname;
*/

-- 8. Teste de conectividade (opcional)
-- Esta query pode ser usada para testar se um usuário específico pode acessar:
/*
SELECT 
  cm.*,
  cmem.role,
  cmem.status
FROM category_mappings cm
JOIN club_members cmem ON cmem.club_id = cm.club_id
WHERE cmem.user_id = auth.uid()
AND cmem.status = 'ativo'
LIMIT 1;
*/
