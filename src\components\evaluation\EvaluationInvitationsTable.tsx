import { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";
import {
  Copy,
  RefreshCw,
  Mail,
  CheckCircle,
  XCircle,
  Clock,
  Trash2
} from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { usePermission } from "@/hooks/usePermission";
import { EVALUATION_PERMISSIONS } from "@/constants/permissions";
import {
  getPlayerEvaluationInvitations,
  sendEvaluationInvitationEmail,
  deletePlayerEvaluationInvitation,
  type PlayerEvaluationInvitation
} from "@/api";
import { supabase } from "@/integrations/supabase/client";

export function EvaluationInvitationsTable() {
  const clubId = useCurrentClubId();
  const { toast } = useToast();
  const { user } = useUser();
  const { can } = usePermission();

  const [invitations, setInvitations] = useState<PlayerEvaluationInvitation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [resendingId, setResendingId] = useState<number | null>(null);
  const [deletingId, setDeletingId] = useState<number | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedInvitation, setSelectedInvitation] = useState<PlayerEvaluationInvitation | null>(null);

  // Fetch invitations
  const fetchInvitations = async () => {
    try {
      setLoading(true);
      setError(null);

      const data = await getPlayerEvaluationInvitations(clubId);
      setInvitations(data);
    } catch (err: any) {
      console.error("Erro ao buscar convites:", err);
      setError(err.message || "Erro ao buscar convites");
      toast({
        title: "Erro",
        description: err.message || "Erro ao buscar convites",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Load invitations on mount
  useEffect(() => {
    fetchInvitations();
  }, [clubId]);

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Copy invitation link to clipboard
  const copyInvitationLink = (token: string) => {
    const SITE_URL = import.meta.env.VITE_SITE_URL || "http://localhost:3000";
    const registrationUrl = `${SITE_URL}/evaluation-registration?token=${token}`;

    navigator.clipboard.writeText(registrationUrl);

    toast({
      title: "Link copiado",
      description: "Link de convite copiado para a área de transferência",
    });
  };

  // Resend invitation email
  const resendInvitation = async (invitation: PlayerEvaluationInvitation) => {
    try {
      setResendingId(invitation.id);

      // Get club name
      const { data: clubData } = await supabase
        .from("club_info")
        .select("name")
        .eq("id", clubId)
        .single();

      const clubName = clubData?.name || "Game Day Nexus";

      // Send invitation email
      await sendEvaluationInvitationEmail(invitation.email, invitation.token, clubName);

      toast({
        title: "Sucesso",
        description: "Convite reenviado com sucesso",
      });
    } catch (err: any) {
      console.error("Erro ao reenviar convite:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao reenviar convite",
        variant: "destructive",
      });
    } finally {
      setResendingId(null);
    }
  };

  // Handle delete invitation
  const handleDeleteInvitation = async () => {
    if (!selectedInvitation || !user?.id) return;

    try {
      setDeletingId(selectedInvitation.id);

      // Delete invitation
      await deletePlayerEvaluationInvitation(clubId, selectedInvitation.id, user.id);

      toast({
        title: "Sucesso",
        description: "Convite excluído com sucesso",
      });

      // Refresh invitations
      fetchInvitations();
    } catch (err: any) {
      console.error("Erro ao excluir convite:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao excluir convite",
        variant: "destructive",
      });
    } finally {
      setDeletingId(null);
      setDeleteDialogOpen(false);
      setSelectedInvitation(null);
    }
  };

  // Open delete confirmation dialog
  const confirmDelete = (invitation: PlayerEvaluationInvitation) => {
    setSelectedInvitation(invitation);
    setDeleteDialogOpen(true);
  };

  // Get status badge
  const getStatusBadge = (status: string, expiresAt: string) => {
    const now = new Date();
    const expiration = new Date(expiresAt);

    if (status === "used") {
      return <Badge className="bg-green-500">Utilizado</Badge>;
    }

    if (status === "expired" || expiration < now) {
      return <Badge className="bg-red-500">Expirado</Badge>;
    }

    return <Badge className="bg-primary">Pendente</Badge>;
  };


  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Convites para pré cadastro</h2>
        <Button
          variant="outline"
          size="sm"
          onClick={fetchInvitations}
          disabled={loading}
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Atualizar
        </Button>
      </div>

      {error && (
        <div className="p-4 bg-red-50 text-red-800 rounded-md">
          {error}
        </div>
      )}

      {loading ? (
        <div className="flex justify-center p-8">
          <RefreshCw className="h-6 w-6 animate-spin" />
        </div>
      ) : invitations.length === 0 ? (
        <div className="text-center p-8 text-muted-foreground">
          Nenhum convite encontrado.
        </div>
      ) : (
        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Email</TableHead>
                <TableHead>CPF</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Criado em</TableHead>
                <TableHead>Expira em</TableHead>
                <TableHead className="text-right">Ações</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {invitations.map((invitation) => (
                <TableRow key={invitation.id}>
                  <TableCell>{invitation.email}</TableCell>
                  <TableCell>{invitation.cpf || "-"}</TableCell>
                  <TableCell>
                    {getStatusBadge(invitation.status, invitation.expires_at)}
                  </TableCell>
                  <TableCell>{formatDate(invitation.created_at)}</TableCell>
                  <TableCell>{formatDate(invitation.expires_at)}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      {can(EVALUATION_PERMISSIONS.INVITATIONS.COPY_LINK) && (
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => copyInvitationLink(invitation.token)}
                          title="Copiar link"
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      )}

                      {invitation.status === "pending" && can(EVALUATION_PERMISSIONS.INVITATIONS.RESEND) && (
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => resendInvitation(invitation)}
                          disabled={resendingId === invitation.id}
                          title="Reenviar email"
                        >
                          {resendingId === invitation.id ? (
                            <RefreshCw className="h-4 w-4 animate-spin" />
                          ) : (
                            <Mail className="h-4 w-4" />
                          )}
                        </Button>
                      )}

                      {invitation.status === "used" && invitation.player_id && (
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => window.location.href = `/jogador/${invitation.player_id}`}
                          title="Ver jogador"
                        >
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        </Button>
                      )}

                      {/* Only show delete button for pending or expired invitations */}
                      {(invitation.status === "pending" || invitation.status === "expired") && can(EVALUATION_PERMISSIONS.INVITATIONS.DELETE) && (
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => confirmDelete(invitation)}
                          disabled={deletingId === invitation.id}
                          title="Excluir convite"
                        >
                          {deletingId === invitation.id ? (
                            <RefreshCw className="h-4 w-4 animate-spin text-red-500" />
                          ) : (
                            <Trash2 className="h-4 w-4 text-red-500" />
                          )}
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Confirmation dialog for deleting invitations */}
      <ConfirmDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title="Excluir Convite"
        description={
          selectedInvitation
            ? `Tem certeza que deseja excluir o convite para ${selectedInvitation.email}? Esta ação não pode ser desfeita.`
            : "Tem certeza que deseja excluir este convite? Esta ação não pode ser desfeita."
        }
        onConfirm={handleDeleteInvitation}
      />
    </div>
  );
}
