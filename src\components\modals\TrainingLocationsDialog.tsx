import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Pencil, Trash2, Plus } from 'lucide-react';
import { useTrainingLocationsStore } from '@/store/useTrainingLocationsStore';
import type { TrainingLocation } from '@/api/trainingLocations';

interface Props {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: number;
}

export function TrainingLocationsDialog({ open, onOpenChange, clubId }: Props) {
  const {
    locations,
    fetchLocations,
    addLocation,
    updateLocation,
    removeLocation,
    getHours,
    loading
  } = useTrainingLocationsStore();

  const [editing, setEditing] = useState<TrainingLocation | null>(null);
  const [name, setName] = useState('');
  const [address, setAddress] = useState('');
  const [hours, setHours] = useState<{ day_of_week: number; start_time: string; end_time: string }[]>([]);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (open) {
      fetchLocations(clubId);
    }
  }, [open, clubId, fetchLocations]);

  const reset = () => {
    setEditing(null);
    setName('');
    setAddress('');
    setHours([]);
    setError(null);
  };

  const startEdit = async (loc: TrainingLocation) => {
    setEditing(loc);
    setName(loc.name);
    setAddress(loc.address || '');
    const h = await getHours(loc.id);
    setHours(h.map(({ day_of_week, start_time, end_time }) => ({ day_of_week, start_time, end_time })));
  };

  const addHour = () => {
    setHours([...hours, { day_of_week: 1, start_time: '', end_time: '' }]);
  };

  const updateHour = (idx: number, field: 'day_of_week' | 'start_time' | 'end_time', value: any) => {
    const list = [...hours];
    // @ts-ignore
    list[idx][field] = value;
    setHours(list);
  };

  const removeHourRow = (idx: number) => {
    const list = [...hours];
    list.splice(idx, 1);
    setHours(list);
  };

  const handleSubmit = async () => {
    if (!name) {
      setError('Nome é obrigatório');
      return;
    }
    try {
      if (editing) {
        await updateLocation(clubId, editing.id, { name, address }, hours);
      } else {
        await addLocation(clubId, { name, address }, hours);
      }
      reset();
    } catch (e: any) {
      setError(e.message || 'Erro ao salvar');
    }
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(o) => {
        onOpenChange(o);
        if (!o) reset();
      }}
    >
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle>Locais de Treino</DialogTitle>
        </DialogHeader>
        <div className="flex gap-6">
          <div className="w-1/2 space-y-2">
            <h3 className="font-medium text-sm">{editing ? 'Editar Local' : 'Novo Local'}</h3>
            <Input placeholder="Nome" value={name} onChange={(e) => setName(e.target.value)} />
            <Input placeholder="Endereço" value={address} onChange={(e) => setAddress(e.target.value)} />
            <div>
              <div className="font-medium text-sm mb-1">Horários</div>
              {hours.map((h, idx) => (
                <div key={idx} className="flex items-center gap-2 mb-1">
                  <select
                    className="border rounded px-1 text-sm"
                    value={h.day_of_week}
                    onChange={(e) => updateHour(idx, 'day_of_week', Number(e.target.value))}
                  >
                    <option value={0}>Domingo</option>
                    <option value={1}>Segunda</option>
                    <option value={2}>Terça</option>
                    <option value={3}>Quarta</option>
                    <option value={4}>Quinta</option>
                    <option value={5}>Sexta</option>
                    <option value={6}>Sábado</option>
                  </select>
                  <Input
                    type="time"
                    className="h-8 w-24"
                    value={h.start_time}
                    onChange={(e) => updateHour(idx, 'start_time', e.target.value)}
                  />
                  <Input
                    type="time"
                    className="h-8 w-24"
                    value={h.end_time}
                    onChange={(e) => updateHour(idx, 'end_time', e.target.value)}
                  />
                  <Button variant="ghost" size="icon" onClick={() => removeHourRow(idx)}>
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              ))}
              <Button variant="outline" size="sm" onClick={addHour} className="mt-1">
                <Plus className="w-4 h-4 mr-1" /> Adicionar Horário
              </Button>
            </div>
            {error && <div className="text-red-500 text-sm">{error}</div>}
            <DialogFooter className="mt-2">
              {editing && (
                <Button variant="outline" onClick={reset} disabled={loading}>
                  Cancelar
                </Button>
              )}
              <Button onClick={handleSubmit} disabled={loading}>
                {editing ? 'Salvar' : 'Criar'}
              </Button>
            </DialogFooter>
          </div>
          <div className="flex-1 overflow-y-auto max-h-[60vh]">
            {locations.length === 0 ? (
              <div className="text-sm text-muted-foreground">Nenhum local cadastrado.</div>
            ) : (
              <table className="w-full text-sm border-collapse">
                <thead>
                  <tr>
                    <th className="text-left">Nome</th>
                    <th className="text-left">Endereço</th>
                    <th></th>
                  </tr>
                </thead>
                <tbody>
                  {locations.map((loc) => (
                    <tr key={loc.id} className="border-t">
                      <td className="py-1">{loc.name}</td>
                      <td className="py-1">{loc.address}</td>
                      <td className="py-1 text-right flex gap-1 justify-end">
                        <Button size="icon" variant="outline" onClick={() => startEdit(loc)}>
                          <Pencil className="w-4 h-4" />
                        </Button>
                        <Button size="icon" variant="destructive" onClick={() => removeLocation(clubId, loc.id)}>
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}