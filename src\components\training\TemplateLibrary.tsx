import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Search,
  Filter,
  Download,
  Upload,
  Star,
  Clock,
  Users,
  Target,
  Play,
  Trash2,
  Edit,
  Copy,
  Share2,
  BookOpen,
  Zap,
  Shield,
  TrendingUp
} from 'lucide-react';
import { TrainingDrill } from './InteractiveTrainingBuilder';
import { useToast } from '@/components/ui/use-toast';

interface TemplateLibraryProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSelectTemplate: (template: TrainingDrill) => void;
  clubId: number;
}

// Templates predefinidos para demonstração
const predefinedTemplates: TrainingDrill[] = [
  {
    id: 'template_1',
    name: 'Posse de Bola 4v2',
    description: 'Exercício de manutenção de posse com superioridade numérica',
    category: 'technical',
    difficulty: 'intermediate',
    steps: [
      {
        id: 'step_1',
        name: 'Configuração',
        description: 'Posicionar jogadores e equipamentos',
        duration: 60,
        elements: [],
        annotations: [],
        drawings: []
      },
      {
        id: 'step_2',
        name: 'Execução',
        description: '4 jogadores mantêm posse contra 2 defensores',
        duration: 480,
        elements: [],
        annotations: [],
        drawings: []
      }
    ],
    totalDuration: 540,
    playersRequired: 6,
    equipmentNeeded: ['cones', 'bolas'],
    objectives: ['Melhoria do passe', 'Tomada de decisão', 'Pressão defensiva'],
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'template_2',
    name: 'Finalização em Velocidade',
    description: 'Treino de finalização com corrida em velocidade',
    category: 'finishing',
    difficulty: 'advanced',
    steps: [
      {
        id: 'step_1',
        name: 'Aquecimento',
        description: 'Corrida leve e alongamento',
        duration: 300,
        elements: [],
        annotations: [],
        drawings: []
      },
      {
        id: 'step_2',
        name: 'Finalização',
        description: 'Sprint e finalização no gol',
        duration: 600,
        elements: [],
        annotations: [],
        drawings: []
      }
    ],
    totalDuration: 900,
    playersRequired: 8,
    equipmentNeeded: ['cones', 'bolas', 'gols'],
    objectives: ['Velocidade', 'Precisão na finalização', 'Condicionamento'],
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'template_3',
    name: 'Pressing Alto',
    description: 'Exercício tático de pressão alta coordenada',
    category: 'tactical',
    difficulty: 'advanced',
    steps: [
      {
        id: 'step_1',
        name: 'Posicionamento',
        description: 'Organização da linha de pressão',
        duration: 180,
        elements: [],
        annotations: [],
        drawings: []
      },
      {
        id: 'step_2',
        name: 'Execução',
        description: 'Pressão coordenada em diferentes zonas',
        duration: 720,
        elements: [],
        annotations: [],
        drawings: []
      }
    ],
    totalDuration: 900,
    playersRequired: 11,
    equipmentNeeded: ['cones', 'coletes'],
    objectives: ['Coordenação defensiva', 'Timing de pressão', 'Compactação'],
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

export function TemplateLibrary({ 
  open, 
  onOpenChange, 
  onSelectTemplate, 
  clubId 
}: TemplateLibraryProps) {
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>('all');
  const [templates, setTemplates] = useState<TrainingDrill[]>(predefinedTemplates);
  const [favoriteTemplates, setFavoriteTemplates] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState('all');

  // Filtrar templates
  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    const matchesDifficulty = selectedDifficulty === 'all' || template.difficulty === selectedDifficulty;
    
    const matchesTab = activeTab === 'all' || 
                      (activeTab === 'favorites' && favoriteTemplates.includes(template.id));
    
    return matchesSearch && matchesCategory && matchesDifficulty && matchesTab;
  });

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'tactical':
        return <Target className="h-4 w-4" />;
      case 'technical':
        return <Zap className="h-4 w-4" />;
      case 'physical':
        return <TrendingUp className="h-4 w-4" />;
      case 'finishing':
        return <Target className="h-4 w-4" />;
      case 'transition':
        return <Shield className="h-4 w-4" />;
      default:
        return <BookOpen className="h-4 w-4" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'tactical':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'technical':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'physical':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'finishing':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'transition':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner':
        return 'bg-green-100 text-green-800';
      case 'intermediate':
        return 'bg-yellow-100 text-yellow-800';
      case 'advanced':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    return `${mins}min`;
  };

  const handleSelectTemplate = (template: TrainingDrill) => {
    onSelectTemplate(template);
    onOpenChange(false);
    toast({
      title: "Template carregado",
      description: `O template "${template.name}" foi carregado com sucesso.`,
    });
  };

  const handleToggleFavorite = (templateId: string) => {
    setFavoriteTemplates(prev => 
      prev.includes(templateId) 
        ? prev.filter(id => id !== templateId)
        : [...prev, templateId]
    );
  };

  const handleDuplicateTemplate = (template: TrainingDrill) => {
    const duplicated = {
      ...template,
      id: `template_${Date.now()}`,
      name: `${template.name} (Cópia)`,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    setTemplates(prev => [...prev, duplicated]);
    toast({
      title: "Template duplicado",
      description: "Uma cópia do template foi criada.",
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Biblioteca de Templates
          </DialogTitle>
          <DialogDescription>
            Escolha um template predefinido ou carregue seus próprios drills salvos
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 flex flex-col space-y-4">
          {/* Filtros */}
          <div className="flex items-center gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Buscar templates..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Categoria" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todas</SelectItem>
                <SelectItem value="tactical">Tático</SelectItem>
                <SelectItem value="technical">Técnico</SelectItem>
                <SelectItem value="physical">Físico</SelectItem>
                <SelectItem value="finishing">Finalização</SelectItem>
                <SelectItem value="transition">Transição</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={selectedDifficulty} onValueChange={setSelectedDifficulty}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Dificuldade" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todas</SelectItem>
                <SelectItem value="beginner">Iniciante</SelectItem>
                <SelectItem value="intermediate">Intermediário</SelectItem>
                <SelectItem value="advanced">Avançado</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList>
              <TabsTrigger value="all">Todos ({templates.length})</TabsTrigger>
              <TabsTrigger value="favorites">Favoritos ({favoriteTemplates.length})</TabsTrigger>
            </TabsList>

            <TabsContent value="all" className="flex-1">
              <ScrollArea className="h-96">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {filteredTemplates.map(template => (
                    <Card key={template.id} className="cursor-pointer hover:shadow-md transition-shadow">
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <CardTitle className="text-sm flex items-center gap-2">
                              {getCategoryIcon(template.category)}
                              {template.name}
                            </CardTitle>
                            <CardDescription className="text-xs mt-1">
                              {template.description}
                            </CardDescription>
                          </div>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleToggleFavorite(template.id);
                            }}
                          >
                            <Star 
                              className={`h-4 w-4 ${
                                favoriteTemplates.includes(template.id)
                                  ? 'fill-yellow-400 text-yellow-400'
                                  : 'text-muted-foreground'
                              }`}
                            />
                          </Button>
                        </div>
                        
                        <div className="flex items-center gap-2 mt-2">
                          <Badge 
                            variant="outline" 
                            className={`text-xs ${getCategoryColor(template.category)}`}
                          >
                            {getCategoryIcon(template.category)}
                            <span className="ml-1 capitalize">{template.category}</span>
                          </Badge>
                          
                          <Badge 
                            variant="outline" 
                            className={`text-xs ${getDifficultyColor(template.difficulty)}`}
                          >
                            {template.difficulty}
                          </Badge>
                        </div>
                      </CardHeader>
                      
                      <CardContent className="pt-0">
                        <div className="grid grid-cols-3 gap-2 text-xs text-muted-foreground mb-3">
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            <span>{formatTime(template.totalDuration)}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Users className="h-3 w-3" />
                            <span>{template.playersRequired}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Target className="h-3 w-3" />
                            <span>{template.steps.length} passos</span>
                          </div>
                        </div>
                        
                        {template.objectives.length > 0 && (
                          <div className="mb-3">
                            <p className="text-xs font-medium mb-1">Objetivos:</p>
                            <div className="flex flex-wrap gap-1">
                              {template.objectives.slice(0, 2).map((objective, index) => (
                                <Badge key={index} variant="secondary" className="text-xs">
                                  {objective}
                                </Badge>
                              ))}
                              {template.objectives.length > 2 && (
                                <Badge variant="secondary" className="text-xs">
                                  +{template.objectives.length - 2}
                                </Badge>
                              )}
                            </div>
                          </div>
                        )}
                        
                        <div className="flex gap-2">
                          <Button 
                            size="sm" 
                            className="flex-1"
                            onClick={() => handleSelectTemplate(template)}
                          >
                            <Play className="h-3 w-3 mr-1" />
                            Usar Template
                          </Button>
                          
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-8 w-8"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDuplicateTemplate(template);
                            }}
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="favorites" className="flex-1">
              <ScrollArea className="h-96">
                {favoriteTemplates.length === 0 ? (
                  <div className="text-center py-8">
                    <Star className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">
                      Nenhum template favoritado ainda
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Clique na estrela para adicionar templates aos favoritos
                    </p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {filteredTemplates.filter(t => favoriteTemplates.includes(t.id)).map(template => (
                      <Card key={template.id} className="cursor-pointer hover:shadow-md transition-shadow">
                        {/* Mesmo conteúdo do card acima */}
                      </Card>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </TabsContent>
          </Tabs>
        </div>

        <div className="flex justify-between items-center pt-4 border-t">
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <Upload className="h-4 w-4 mr-2" />
              Importar Template
            </Button>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Exportar Selecionados
            </Button>
          </div>
          
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Fechar
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
