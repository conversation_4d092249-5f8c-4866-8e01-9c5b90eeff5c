import { jsPDF } from "jspdf";
import 'jspdf-autotable';
import { ClubInfo } from "@/api/api";
import autoTable from "jspdf-autotable";
import { getLighterClubColor } from '@/utils/themeUtils';

// Tipo para jsPDF com autoTable
type jsPDFWithAutoTable = jsPDF & {
  autoTable: typeof autoTable;
  lastAutoTable: {
    finalY: number;
  };
};

/**
 * Gera um relatório de controle de rouparia por categoria em PDF
 * @param players Lista de jogadores da categoria
 * @param categoryName Nome da categoria
 * @param clubInfo Informações do clube
 * @param filename Nome do arquivo PDF
 */
export async function generateClothingControlReport(
  players: any[],
  categoryName: string,
  clubInfo: ClubInfo,
  filename: string = 'controle-rouparia.pdf'
): Promise<Blob> {
  // Criar um novo documento PDF
  const doc = new jsPDF() as unknown as jsPDFWithAutoTable;

  // Adicionar título
  doc.setFontSize(18);
  doc.text(title, 14, 22);

  // Adicionar informações do clube
  doc.setFontSize(12);
  doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, 14, 30);

  if (clubInfo.address) {
    doc.text(`Endereço: ${clubInfo.address}`, 14, 36);
  }

  if (clubInfo.phone) {
    doc.text(`Telefone: ${clubInfo.phone}`, 14, 42);
  }

  // Tentar adicionar o logo do clube, se disponível
  if (clubInfo.logo_url) {
    try {
      const img = new Image();

      // Criar uma promessa para lidar com o carregamento da imagem
      const loadImage = new Promise<void>((resolve, reject) => {
        img.onload = () => {
          try {
            // Calcular proporção para manter as proporções
            const imgWidth = 30;
            const imgHeight = (img.height * imgWidth) / img.width;

            // Adicionar a imagem ao PDF (alinhada à direita)
            doc.addImage(img, 'PNG', 170, 15, imgWidth, imgHeight);
            resolve();
          } catch (error) {
            reject(error);
          }
        };
        img.onerror = reject;
      });

      // Iniciar o carregamento da imagem
      img.src = clubInfo.logo_url;

      // Aguardar o carregamento e processamento da imagem
      await loadImage;
    } catch (logoError) {
      console.error("Erro ao adicionar logo ao PDF:", logoError);
    }
  }

  // Adicionar data do relatório
  const currentDate = new Date().toLocaleDateString('pt-BR');
  doc.setFontSize(10);
  doc.text(`Data: ${currentDate}`, 170, 50, { align: 'right' });

  // Ordem das posições para o relatório
  const POSITION_ORDER = [
    "Goleiro",
    "Zagueiro",
    "Lateral",
    "Volante",
    "Meio-campista",
    "Meias", // Incluir variação encontrada no sistema
    "Extremo",
    "Atacante",
    "Centroavante", // Incluir variação encontrada no sistema
    "Outro"
  ];

  // Ordenar jogadores primeiro por posição, depois por nome
  const sortedPlayers = [...players].sort((a, b) => {
    const posA = POSITION_ORDER.indexOf(a.position);
    const posB = POSITION_ORDER.indexOf(b.position);

    // Se ambas as posições estão na lista, ordenar conforme a lista
    if (posA !== -1 && posB !== -1) {
      if (posA !== posB) {
        return posA - posB;
      }
    }

    // Se apenas uma posição está na lista, ela vem primeiro
    if (posA !== -1 && posB === -1) return -1;
    if (posA === -1 && posB !== -1) return 1;

    // Se nenhuma posição está na lista ou são da mesma posição, ordenar alfabeticamente por nome
    return a.name.localeCompare(b.name);
  });

  // Preparar cabeçalhos da tabela
  const headers = ['Posição', 'Nome', 'Apelido', 'Data Nasc.', 'NC', '□', '□', '□', '□', '□', '□', '□'];

  // Preparar dados para a tabela
  const tableData = sortedPlayers.map(player => [
    player.position || '-',
    player.name || '-',
    player.nickname || '-',
    player.birth_date ? new Date(player.birth_date).toLocaleDateString('pt-BR') : '-',
    player.registration_number || '-',
    '', '', '', '', '', '', '' // Quadrados vazios para marcação manual
  ]);

  // Adicionar comissão técnica ao final da tabela
  tableData.push([
    'Técnico',
    'Comissão Técnica',
    '-',
    '-',
    '-',
    '', '', '', '', '', '', ''
  ]);

  tableData.push([
    'Auxiliar',
    'Comissão Técnica',
    '-',
    '-',
    '-',
    '', '', '', '', '', '', ''
  ]);

  // Adicionar a tabela ao PDF
  autoTable(doc, {
    startY: 60,
    head: [headers],
    body: tableData,
    theme: 'grid',
    headStyles: { fillColor: getLighterClubColor(0.7), textColor: [255, 255, 255], fontStyle: 'bold' },
    columnStyles: {
      0: { cellWidth: 20 },
      1: { cellWidth: 40 },
      2: { cellWidth: 30 },
      3: { cellWidth: 20 },
      4: { cellWidth: 15 },
      5: { cellWidth: 8 },
      6: { cellWidth: 8 },
      7: { cellWidth: 8 },
      8: { cellWidth: 8 },
      9: { cellWidth: 8 },
      10: { cellWidth: 8 },
      11: { cellWidth: 8 }
    },
    didDrawPage: (data) => {
      // Adicionar cabeçalho em cada página
      if (data.pageNumber > 1) {
        doc.setFontSize(10);
        doc.text(title, 14, 10);
        doc.setFontSize(8);
        doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, 14, 15);
        doc.text(`Data: ${currentDate}`, 14, 20);
      }
    }
  });

  // Adicionar legenda para os quadrados
  const docWithTable = doc as jsPDFWithAutoTable;
  let yPosition = docWithTable.lastAutoTable.finalY + 10;

  doc.setFontSize(10);
  doc.text("Legenda: Marque os quadrados para controle de entrega de itens.", 14, yPosition);

  // Adicionar rodapé
  const pageCount = doc.internal.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(8);
    doc.text(
      `Relatório gerado em ${currentDate} - Página ${i} de ${pageCount}`,
      14,
      doc.internal.pageSize.height - 10
    );
  }

  // Retornar o PDF como um Blob
  return doc.output('blob');
}
