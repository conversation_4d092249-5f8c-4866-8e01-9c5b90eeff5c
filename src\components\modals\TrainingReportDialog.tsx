import { useState, useEffect } from "react";import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Loader2, FileText, Download } from "lucide-react";
import type { TrainingReportOptions } from "@/utils/trainingReportGenerator";
import { useCategoriesStore } from "@/store/useCategoriesStore";
import { useCurrentClubId } from "@/context/ClubContext";

interface TrainingReportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onGenerate: (options: TrainingReportOptions) => Promise<void>;
  loading?: boolean;
}

export function TrainingReportDialog({
  open,
  onOpenChange,
  onGenerate,
  loading = false
}: TrainingReportDialogProps) {
  const [options, setOptions] = useState<TrainingReportOptions>({
    includeStatistics: true,
    includeGoals: true,
    includeExercises: true,
    includeUpcoming: true,
    includeCompleted: true,
    includeDescription: true,
    includeMaterials: true,
    period: {
      startDate: undefined,
      endDate: undefined
    },
    categoryFilter: undefined
  });

  const { categories, fetchCategories } = useCategoriesStore();
  const clubId = useCurrentClubId();

  useEffect(() => {
    fetchCategories(clubId);
  }, [fetchCategories, clubId]);


  const handleOptionChange = (key: keyof TrainingReportOptions, value: any) => {
    setOptions(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handlePeriodChange = (key: 'startDate' | 'endDate', value: any) => {
    setOptions(prev => ({
      ...prev,
      period: {
        ...prev.period,
        [key]: value || undefined
      }
    }));
  };

  const handleGenerate = async () => {
    try {
      await onGenerate(options);
      onOpenChange(false);
    } catch (error) {
      console.error('Erro ao gerar relatório:', error);
    }
  };

  const handleCategoryChange = (value: string) => {
    setOptions(prev => ({ ...prev, categoryFilter: value === 'all' ? undefined : value }));
  };


  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Relatório de Treinamentos
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Período */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Período</Label>
            <div className="grid grid-cols-2 gap-3">
              <div>
                <Label className="text-xs text-muted-foreground">Início</Label>
                <Input
                  type="date"
                  value={options.period?.startDate || ''}
                  onChange={(e) => handlePeriodChange('startDate', e.target.value)}
                />
              </div>
              <div>
                <Label className="text-xs text-muted-foreground">Fim</Label>
                <Input
                  type="date"
                  value={options.period?.endDate || ''}
                  onChange={(e) => handlePeriodChange('endDate', e.target.value)}
                />
              </div>
            </div>
          </div>

          {/* Categoria */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Categoria</Label>
            <Select value={options.categoryFilter || 'all'} onValueChange={handleCategoryChange}>
              <SelectTrigger>
                <SelectValue placeholder="Todas" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todas</SelectItem>
                {categories.map(cat => (
                  <SelectItem key={cat.id} value={cat.name}>
                    {cat.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Seções a incluir */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Seções a incluir</Label>
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="includeStatistics" 
                  checked={options.includeStatistics}
                  onCheckedChange={(checked) => handleOptionChange('includeStatistics', checked)}
                />
                <Label htmlFor="includeStatistics">Incluir estatísticas/resumo</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="includeGoals" 
                  checked={options.includeGoals}
                  onCheckedChange={(checked) => handleOptionChange('includeGoals', checked)}
                />
                <Label htmlFor="includeGoals">Incluir objetivos de treinamento</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="includeExercises" 
                  checked={options.includeExercises}
                  onCheckedChange={(checked) => handleOptionChange('includeExercises', checked)}
                />
                <Label htmlFor="includeExercises">Incluir banco de exercícios</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="includeUpcoming" 
                  checked={options.includeUpcoming}
                  onCheckedChange={(checked) => handleOptionChange('includeUpcoming', checked)}
                />
                <Label htmlFor="includeUpcoming">Incluir treinamentos agendados</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="includeCompleted" 
                  checked={options.includeCompleted}
                  onCheckedChange={(checked) => handleOptionChange('includeCompleted', checked)}
                />
                <Label htmlFor="includeCompleted">Incluir treinamentos concluídos</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="includeDescription" 
                  checked={options.includeDescription}
                  onCheckedChange={(checked) => handleOptionChange('includeDescription', checked)}
                />
                <Label htmlFor="includeDescription">Incluir descrição dos treinamentos</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="includeMaterials" 
                  checked={options.includeMaterials}
                  onCheckedChange={(checked) => handleOptionChange('includeMaterials', checked)}
                />
                <Label htmlFor="includeMaterials">Incluir material necessário</Label>
              </div>
            </div>
          </div>

          {/* Preview do relatório */}
          <div className="bg-muted/50 p-3 rounded-md">
            <div className="flex items-start gap-2">
              <FileText className="h-4 w-4 text-muted-foreground mt-0.5" />
              <div className="text-xs text-muted-foreground">
                <div className="font-medium mb-1">Relatório de Treinamentos</div>
                <div>
                {options.period?.startDate && options.period?.endDate
                    ? `Período: ${options.period.startDate} a ${options.period.endDate}`
                    : 'Período: Todos os registros'}
                </div>
                <div className="mt-1">
                  Seções: {[
                    options.includeStatistics && 'Estatísticas',
                    options.includeGoals && 'Objetivos',
                    options.includeUpcoming && 'Agendados',
                    options.includeCompleted && 'Concluídos',
                    options.includeExercises && 'Exercícios',
                    options.includeDescription && 'Descrições',
                    options.includeMaterials && 'Materiais'
                  ].filter(Boolean).join(', ')}
                </div>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={loading}>
            Cancelar
          </Button>
          <Button onClick={handleGenerate} disabled={loading}>
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Gerando...
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                Gerar Relatório
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
