import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { usePlayersStore } from "@/store/usePlayersStore";
import { useCurrentClubId } from "@/context/ClubContext";
import { useMemo, useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { ChevronRight } from "lucide-react";
import { useCategoriesStore } from "@/store/useCategoriesStore";
import { Player } from "@/api/api";
// import { useRouter } from "next/router";

export function SquadStatus() {
  const clubId = useCurrentClubId();
  const { players, loading: playersLoading } = usePlayersStore();
  const { selectedCategory, getCategoryPlayers } = useCategoriesStore();
  const [filteredPlayers, setFilteredPlayers] = useState<Player[]>([]);
  const [loading, setLoading] = useState(true);
  // const router = useRouter();
  
  // Adicionar um listener para o evento de mudança de categoria
  useEffect(() => {
    const handleCategoryChange = () => {
      // Forçar uma nova busca quando a categoria for alterada
      if (clubId && selectedCategory) {
        fetchCategoryPlayers();
      } else {
        setFilteredPlayers(players);
        setLoading(false);
      }
    };
    
    window.addEventListener('categoryChanged', handleCategoryChange);
    return () => window.removeEventListener('categoryChanged', handleCategoryChange);
  }, [clubId, selectedCategory, players]);

  // Função para buscar jogadores da categoria selecionada
  const fetchCategoryPlayers = async () => {
    if (!clubId) return;
    
    setLoading(true);
    
    try {
      if (selectedCategory) {
        console.log(`Buscando jogadores para a categoria ${selectedCategory.id} (${selectedCategory.name})`);
        const categoryPlayers = await getCategoryPlayers(clubId, selectedCategory.id);
        console.log(`Encontrados ${categoryPlayers.length} jogadores na categoria ${selectedCategory.name}`);
        setFilteredPlayers(categoryPlayers);
      } else {
        console.log('Nenhuma categoria selecionada, usando todos os jogadores');
        setFilteredPlayers(players);
      }
    } catch (error) {
      console.error('Erro ao buscar jogadores da categoria:', error);
      setFilteredPlayers(players); // Fallback para todos os jogadores em caso de erro
    } finally {
      setLoading(false);
    }
  };

  // Efeito para buscar jogadores quando o componente for montado ou o clube mudar
  useEffect(() => {
    if (clubId) {
      fetchCategoryPlayers();
    }
  }, [clubId]);

  // Ordenar e limitar jogadores para exibição
  const displayPlayers = useMemo(() => {
    if (loading) return [];
    
    // Primeiro ordenar por status (disponível primeiro, depois em recuperação, depois lesionado)
    // Depois por minutos jogados (maior para menor)
    return [...filteredPlayers]
      .sort((a, b) => {
        // Primeiro por status
        if (a.status !== b.status) {
          if (a.status === "disponível") return -1;
          if (b.status === "disponível") return 1;
          if (a.status === "em recuperação") return -1;
          if (b.status === "em recuperação") return 1;
        }

        // Depois por minutos jogados
        const minutesA = a.stats?.minutes || 0;
        const minutesB = b.stats?.minutes || 0;
        return minutesB - minutesA;
      })
      .slice(0, 10); // Limitar a 10 jogadores
  }, [filteredPlayers, loading]);

  // Média de minutos jogados (apenas dos jogadores filtrados)
  const { avgMinutes, totalPlayers } = useMemo(() => {
    if (loading) return { avgMinutes: 0, totalPlayers: 0 };
    
    const minutesValues = filteredPlayers
      .map((p) => (p.stats?.minutes ? p.stats.minutes : null))
      .filter((v): v is number => v !== null);
      
    return {
      avgMinutes: minutesValues.length > 0
        ? Math.round(minutesValues.reduce((a, b) => a + b, 0) / minutesValues.length)
        : 0,
      totalPlayers: filteredPlayers.length
    };
  }, [filteredPlayers, loading]);

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div className="flex items-center gap-2">
          <CardTitle>Status do Elenco</CardTitle>
          {selectedCategory && (
            <span className="text-sm text-muted-foreground">
              ({selectedCategory.name})
            </span>
          )}
        </div>
        {!loading && totalPlayers > 10 && (
          <Button
            variant="ghost"
            size="sm"
            className="text-xs flex items-center"
            // onClick={() => router.push('/elenco')}
          >
            Ver todos <ChevronRight className="h-4 w-4 ml-1" />
          </Button>
        )}
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {loading ? (
            <div className="text-muted-foreground text-center py-4">Carregando jogadores...</div>
          ) : filteredPlayers.length === 0 ? (
            <div className="text-muted-foreground text-center py-4">
              {selectedCategory 
                ? `Nenhum jogador encontrado na categoria ${selectedCategory.name}.` 
                : 'Nenhum jogador cadastrado no clube.'}
            </div>
          ) : (
            displayPlayers.map((player) => (
              <div key={player.id} className="space-y-1">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-sm">{player.name}</p>
                    <p className="text-xs text-muted-foreground">{player.position}</p>
                  </div>
                  <div
                    className={`text-xs px-2 py-1 rounded-full ${
                      player.status === "disponível"
                        ? "bg-green-100 text-green-800"
                        : player.status === "em recuperação"
                        ? "bg-amber-100 text-amber-800"
                        : player.status === "lesionado"
                        ? "bg-rose-100 text-rose-800"
                        : player.status === "suspenso"
                        ? "bg-orange-100 text-orange-800"
                        : player.status === "inativo"
                        ? "bg-gray-100 text-gray-800"
                        : player.status === "emprestado"
                        ? "bg-blue-100 text-blue-800"
                        : "bg-gray-100 text-gray-800"
                    }`}
                  >
                    {player.status}
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <p className="text-xs font-medium">Minutos jogados</p>
                  <p className="text-xs font-medium">{player.stats?.minutes ?? "Sem dados"}m</p>
                </div>
                <Progress
                  value={typeof player.stats?.minutes === "number" ? player.stats.minutes : 0}
                  className={`h-2 ${
                    typeof player.stats?.minutes === "number" && player.stats.minutes > 80
                      ? "bg-muted text-team-green"
                      : typeof player.stats?.minutes === "number" && player.stats.minutes > 60
                      ? "bg-muted text-amber-500"
                      : "bg-muted text-rose-500"
                  }`}
                />
              </div>
            ))
          )}
          <div className="mt-4 text-xs text-muted-foreground text-center">
            Média de minutos jogados: <span className="font-bold">{avgMinutes}m</span>
            {totalPlayers > 10 && (
              <span className="ml-2 text-xs text-muted-foreground">
                (Mostrando {Math.min(10, totalPlayers)} de {totalPlayers} jogadores{selectedCategory ? ` na categoria ${selectedCategory.name}` : ''})
              </span>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
