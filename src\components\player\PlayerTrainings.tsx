import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { RefreshCw, AlertTriangle } from 'lucide-react';
import { useTrainingsStore } from '@/store/useTrainingsStore';
import { getPlayerTrainingsAlternative } from '@/api/api';
import { useCurrentClubId } from '@/context/ClubContext';
import { useToast } from '@/hooks/use-toast';
import type { Training } from '@/api/trainings';
import { TrainingImages } from '@/components/training/TrainingImages';
import { formatDate } from '@/lib/utils';

interface PlayerTrainingsProps {
  playerId: string;
  maxDisplay?: number;
  showTitle?: boolean;
}

export function PlayerTrainings({ playerId, maxDisplay = 3, showTitle = true }: PlayerTrainingsProps) {
  const [alternativeTrainings, setAlternativeTrainings] = useState<Training[]>([]);
  const [isLoadingAlternative, setIsLoadingAlternative] = useState(false);
  const [useAlternative, setUseAlternative] = useState(false);
  const { trainings } = useTrainingsStore();
  const clubId = useCurrentClubId();
  const { toast } = useToast();

  // Filtrar treinamentos do store normal
  const normalTrainings = trainings
    .filter(t => {
      // Verificar se o jogador está associado ao treino
      if (!t.player_ids || !Array.isArray(t.player_ids) || !t.player_ids.includes(playerId)) {
        return false;
      }

      // Filtrar apenas treinos futuros e não concluídos
      const now = new Date();
      const trainingDateTime = new Date(`${t.date}T${t.end_time || '00:00'}`);
      return trainingDateTime >= now && t.status !== "concluído";
    })
    .sort((a, b) => {
      // Ordenar por data (mais próximos primeiro)
      const dateA = new Date(`${a.date}T${a.start_time || '00:00'}`);
      const dateB = new Date(`${b.date}T${b.start_time || '00:00'}`);
      return dateA.getTime() - dateB.getTime();
    })
    .slice(0, maxDisplay);

  // Função para carregar treinamentos alternativos
  const loadAlternativeTrainings = async () => {
    if (!clubId) return;

    setIsLoadingAlternative(true);
    try {
      console.log('🔄 Carregando treinamentos alternativos...');
      const altTrainings = await getPlayerTrainingsAlternative(clubId, playerId);
      setAlternativeTrainings(altTrainings.slice(0, maxDisplay));
      setUseAlternative(true);
      
      toast({
        title: "Treinamentos carregados",
        description: `${altTrainings.length} treinamento(s) encontrado(s) usando método alternativo.`,
        variant: "default"
      });
    } catch (error) {
      console.error('Erro ao carregar treinamentos alternativos:', error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar os treinamentos alternativos.",
        variant: "destructive"
      });
    } finally {
      setIsLoadingAlternative(false);
    }
  };

  // Verificar automaticamente se deve usar método alternativo
  useEffect(() => {
    if (normalTrainings.length === 0 && !useAlternative && !isLoadingAlternative) {
      // Se não há treinamentos normais, tentar método alternativo automaticamente
      loadAlternativeTrainings();
    }
  }, [normalTrainings.length, useAlternative, isLoadingAlternative]);

  const displayTrainings = useAlternative ? alternativeTrainings : normalTrainings;
  const hasTrainings = displayTrainings.length > 0;

  const renderTraining = (training: Training) => (
    <div key={training.id} className="border rounded-lg p-4 mb-2">
      <div className="flex items-center justify-between">
        <span className="font-medium">{training.name}</span>
        <span className="text-xs text-muted-foreground">
        {formatDate(training.date)}
        </span>
      </div>
      <div className="flex flex-wrap gap-x-4 gap-y-1 mt-2 text-xs text-muted-foreground">
        <div>Horário: {training.start_time}{training.end_time ? ` - ${training.end_time}` : ''}</div>
        <div>Local: {training.location}</div>
        <div>Tipo: {training.type}</div>
        {training.coach && <div>Treinador: {training.coach}</div>}
      </div>
      {training.description && <div className="text-sm mt-2">{training.description}</div>}
      {training.images && training.images.length > 0 && (
        <TrainingImages images={training.images} maxDisplay={2} />
      )}
    </div>
  );

  const content = (
    <div>
      {hasTrainings ? (
        displayTrainings.map(renderTraining)
      ) : (
        <div className="space-y-3">
          <div className="text-muted-foreground text-sm">Nenhum treinamento encontrado.</div>
          
          {!useAlternative && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-2">
                  <p>Não foram encontrados treinamentos usando o método padrão.</p>
                  <Button 
                    onClick={loadAlternativeTrainings}
                    disabled={isLoadingAlternative}
                    size="sm"
                    variant="outline"
                    className="flex items-center gap-2"
                  >
                    {isLoadingAlternative ? (
                      <RefreshCw className="h-3 w-3 animate-spin" />
                    ) : (
                      <RefreshCw className="h-3 w-3" />
                    )}
                    {isLoadingAlternative ? 'Carregando...' : 'Tentar Método Alternativo'}
                  </Button>
                </div>
              </AlertDescription>
            </Alert>
          )}
        </div>
      )}

      {useAlternative && (
        <div className="mt-3 p-2 bg-blue-50 border border-blue-200 rounded text-xs text-blue-700">
          ℹ️ Treinamentos carregados usando método alternativo
        </div>
      )}
    </div>
  );

  if (showTitle) {
    return (
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Próximos Treinamentos</CardTitle>
          {hasTrainings && (
            <Button 
              onClick={loadAlternativeTrainings}
              disabled={isLoadingAlternative}
              size="sm"
              variant="outline"
              className="flex items-center gap-2"
            >
              {isLoadingAlternative ? (
                <RefreshCw className="h-3 w-3 animate-spin" />
              ) : (
                <RefreshCw className="h-3 w-3" />
              )}
              Recarregar
            </Button>
          )}
        </CardHeader>
        <CardContent>
          {content}
        </CardContent>
      </Card>
    );
  }

  return content;
}
