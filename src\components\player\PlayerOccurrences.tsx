import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { useUser } from "@/context/UserContext";
import { usePermission } from "@/hooks/usePermission";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle, Plus, History, FileText, CheckCircle, XCircle, AlertTriangle } from "lucide-react";
import { formatDate } from "@/lib/utils";
import { Player, PlayerOccurrence, getPlayerOccurrences, createPlayerOccurrence, updatePlayerOccurrence, deletePlayerOccurrence } from "@/api/api";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { PermissionControl } from "@/components/PermissionControl";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { OccurrenceForm } from "./OccurrenceForm";

interface PlayerOccurrencesProps {
  playerId: string;
  clubId: number;
}

export function PlayerOccurrences({ playerId, clubId }: PlayerOccurrencesProps) {
  const { user } = useUser();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("active");
  const [occurrences, setOccurrences] = useState<PlayerOccurrence[]>([]);
  const [loading, setLoading] = useState(true);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [selectedOccurrence, setSelectedOccurrence] = useState<PlayerOccurrence | null>(null);
  const { hasPermission } = usePermission();

  useEffect(() => {
    loadOccurrences();
  }, [playerId]);

  const loadOccurrences = async () => {
    try {
      const data = await getPlayerOccurrences(clubId, playerId);
      setOccurrences(data);
    } catch (error) {
      console.error("Erro ao carregar ocorrências:", error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar as ocorrências do jogador.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreateOccurrence = () => {
    setSelectedOccurrence(null);
    setIsFormOpen(true);
  };

  // Esquema de validação do formulário
  const occurrenceSchema = z.object({
    type: z.enum(["divergence", "punishment"]),
    title: z.string().min(1, "Título é obrigatório"),
    description: z.string().min(1, "Descrição é obrigatória"),
    severity: z.enum(["low", "medium", "high"]),
    status: z.enum(["active", "resolved", "archived"]),
    resolution_notes: z.string().optional(),
  });

  // Inicializar o formulário
  const form = useForm<z.infer<typeof occurrenceSchema>>({
    resolver: zodResolver(occurrenceSchema),
    defaultValues: {
      type: "divergence",
      title: "",
      description: "",
      severity: "medium",
      status: "active",
      resolution_notes: "",
    },
  });

  const handleEditOccurrence = (occurrence: PlayerOccurrence) => {
    // Criar um objeto com os campos necessários para o formulário
    const formData = {
      type: occurrence.type,
      title: occurrence.title,
      description: occurrence.description,
      severity: occurrence.severity,
      status: occurrence.status,
      resolution_notes: occurrence.resolution_notes || ''
    };
    
    setSelectedOccurrence(occurrence);
    // Resetar o formulário com os dados da ocorrência
    form.reset(formData);
    setIsFormOpen(true);
  };

  const handleDeleteOccurrence = async (occurrenceId: number) => {
    try {
      await deletePlayerOccurrence(occurrenceId);
      await loadOccurrences();
      toast({
        title: "Sucesso",
        description: "Ocorrência excluída com sucesso.",
      });
    } catch (error) {
      console.error("Erro ao excluir ocorrência:", error);
      toast({
        title: "Erro",
        description: "Não foi possível excluir a ocorrência.",
        variant: "destructive",
      });
    }
  };

  const handleSubmit = async (data: any) => {
    try {
      if (selectedOccurrence) {
        await updatePlayerOccurrence(selectedOccurrence.id, {
          ...data,
          club_id: clubId,
          player_id: playerId,
        });
        toast({
          title: "Sucesso",
          description: "Ocorrência atualizada com sucesso.",
        });
      } else {
        await createPlayerOccurrence(clubId, playerId, {
          ...data,
          club_id: clubId,
          player_id: playerId,
        });
        toast({
          title: "Sucesso",
          description: "Ocorrência criada com sucesso.",
        });
      }
      setIsFormOpen(false);
      await loadOccurrences();
    } catch (error) {
      console.error("Erro ao salvar ocorrência:", error);
      toast({
        title: "Erro",
        description: "Não foi possível salvar a ocorrência.",
        variant: "destructive",
      });
    }
  };

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case "low":
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Baixa</Badge>;
      case "medium":
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Média</Badge>;
      case "high":
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Alta</Badge>;
      default:
        return null;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Ativa</Badge>;
      case "resolved":
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Resolvida</Badge>;
      case "archived":
        return <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">Arquivada</Badge>;
      default:
        return null;
    }
  };

  const getTypeBadge = (type: string) => {
    switch (type) {
      case "divergence":
        return <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">Divergência</Badge>;
      case "punishment":
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Punição</Badge>;
      default:
        return null;
    }
  };

  const filteredOccurrences = occurrences.filter(occurrence => {
    if (activeTab === "active") return occurrence.status === "active";
    if (activeTab === "resolved") return occurrence.status === "resolved";
    if (activeTab === "archived") return occurrence.status === "archived";
    return true;
  });

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Ocorrências</h2>
          <p className="text-muted-foreground">
            Registro de ocorrências, divergências e punições
          </p>
        </div>
        <PermissionControl permissions={["players.occurrences.create", "players.occurrences.edit"]}>
          <Button onClick={handleCreateOccurrence}>
            <Plus className="h-4 w-4 mr-2" />
            Nova Ocorrência
          </Button>
        </PermissionControl>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="active" className="flex items-center gap-1">
            <FileText className="h-4 w-4" />
            <span>Ativas</span>
          </TabsTrigger>
          <TabsTrigger value="resolved" className="flex items-center gap-1">
            <CheckCircle className="h-4 w-4" />
            <span>Resolvidas</span>
          </TabsTrigger>
          <TabsTrigger value="archived" className="flex items-center gap-1">
            <History className="h-4 w-4" />
            <span>Arquivadas</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab}>
          {loading ? (
            <div className="text-center py-8">Carregando ocorrências...</div>
          ) : filteredOccurrences.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-8">
                <AlertTriangle className="h-12 w-12 text-muted-foreground mb-4" />
                <p className="text-muted-foreground">Nenhuma ocorrência registrada</p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {filteredOccurrences.map((occurrence) => (
                <Card key={occurrence.id}>
                  <CardHeader className="flex flex-row items-center justify-between">
                    <div>
                      <CardTitle className="text-lg">{occurrence.title}</CardTitle>
                      <div className="flex gap-2 mt-2">
                        {getTypeBadge(occurrence.type)}
                        {getSeverityBadge(occurrence.severity)}
                        {getStatusBadge(occurrence.status)}
                      </div>
                      <p className="text-sm text-muted-foreground mt-2">
                        {format(new Date(occurrence.created_at), "dd 'de' MMMM 'de' yyyy", { locale: ptBR })}
                      </p>
                    </div>
                    <PermissionControl permissions={["players.occurrences.edit", "players.occurrences.delete"]}>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm" onClick={() => handleEditOccurrence(occurrence)}>
                          Editar
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => {
                            if (confirm("Tem certeza que deseja excluir esta ocorrência?")) {
                              handleDeleteOccurrence(Number(occurrence.id));
                            }
                          }}
                        >
                          Excluir
                        </Button>
                      </div>
                    </PermissionControl>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-medium mb-2">Descrição</h4>
                        <p className="text-muted-foreground">{occurrence.description}</p>
                      </div>
                      {occurrence.status === "resolved" && occurrence.resolution_notes && (
                        <div>
                          <h4 className="font-medium mb-2">Resolução</h4>
                          <p className="text-muted-foreground">{occurrence.resolution_notes}</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>

      <OccurrenceForm
        isOpen={isFormOpen}
        onClose={() => {
          setIsFormOpen(false);
          setSelectedOccurrence(null);
          form.reset();
        }}
        onSubmit={handleSubmit}
        form={form}
      />
    </div>
  );
} 