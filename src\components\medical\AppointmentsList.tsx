import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { format, parseISO, isToday, isTomorrow, isThisWeek, isAfter } from "date-fns";
import { ptBR } from "date-fns/locale";
import { CalendarIcon, Clock, MoreHorizontal, Search, Check, X, Play, Trash2 } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { useMedicalAppointmentsStore } from "@/store/useMedicalAppointmentsStore";
import { AppointmentScheduler } from "./AppointmentScheduler";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { usePermission } from "@/hooks/usePermission";
import { getMedicalProfessionalByUserId, MedicalProfessional } from "@/api/api";

interface AppointmentsListProps {
  title?: string;
  description?: string;
  showFilters?: boolean;
  showScheduleButton?: boolean;
  defaultProfessionalId?: number;
  defaultPlayerId?: string;
}

export function AppointmentsList({
  title = "Agenda de Atendimentos",
  description = "Gerencie os agendamentos médicos",
  showFilters = true,
  showScheduleButton = true,
  defaultProfessionalId,
  defaultPlayerId,
}: AppointmentsListProps) {
  const { toast } = useToast();
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const { role } = usePermission();
  const {
    appointments,
    loading,
    error,
    fetchAppointments,
    updateStatus,
    updateAppointment,
    deleteAppointment,
  } = useMedicalAppointmentsStore();

  // State for filters
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  // Default to today's appointments when the list is first rendered
  const [dateFilter, setDateFilter] = useState<string>("today");
  const [professionalId, setProfessionalId] = useState<number | undefined>(defaultProfessionalId);
  const [playerId, setPlayerId] = useState<string | undefined>(defaultPlayerId);

  // State for cancel dialog
  const [cancelDialogOpen, setCancelDialogOpen] = useState(false);
  const [appointmentToCancel, setAppointmentToCancel] = useState<number | null>(null);
  const [cancelReason, setCancelReason] = useState("");

  // State for delete dialog
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [appointmentToDelete, setAppointmentToDelete] = useState<number | null>(null);

  // State for medical professional
  const [medicalProfessional, setMedicalProfessional] = useState<MedicalProfessional | null>(null);

  // Load appointments when component mounts
  useEffect(() => {
    if (clubId) {
      loadAppointments();
    }
  }, [clubId, professionalId, playerId]);

  // Load medical professional if user is medical
  useEffect(() => {
    if (clubId && user?.id && role === "medical") {
      loadMedicalProfessional();
    }
  }, [clubId, user?.id, role]);

  // Load medical professional data
  const loadMedicalProfessional = async () => {
    try {
      const data = await getMedicalProfessionalByUserId(clubId, user?.id || "");
      if (data) {
        setMedicalProfessional(data);
        setProfessionalId(data.id);
      }
    } catch (error) {
      console.error("Erro ao carregar dados do profissional médico:", error);
    }
  };

  // Load appointments with filters
  const loadAppointments = async () => {
    try {
      const filters: any = {};

      if (professionalId) {
        filters.professionalId = professionalId;
      }

      if (playerId) {
        filters.playerId = playerId;
      }

      // Date filters
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      if (dateFilter === "today") {
        filters.startDate = format(today, "yyyy-MM-dd");
        filters.endDate = format(today, "yyyy-MM-dd");
      } else if (dateFilter === "tomorrow") {
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        filters.startDate = format(tomorrow, "yyyy-MM-dd");
        filters.endDate = format(tomorrow, "yyyy-MM-dd");
      } else if (dateFilter === "week") {
        filters.startDate = format(today, "yyyy-MM-dd");
        const endOfWeek = new Date(today);
        endOfWeek.setDate(endOfWeek.getDate() + 7);
        filters.endDate = format(endOfWeek, "yyyy-MM-dd");
      }

      // Status filter
      if (statusFilter !== "all") {
        filters.status = statusFilter;
      }

      await fetchAppointments(clubId, filters);
    } catch (error) {
      console.error("Erro ao carregar agendamentos:", error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar os agendamentos",
        variant: "destructive",
      });
    }
  };

  // Filter appointments by search query
  const filteredAppointments = appointments.filter((appointment) => {
    const playerName = appointment.player_name || "";
    const professionalName = appointment.professional_name || "";
    const appointmentType = appointment.appointment_type || "";

    return (
      playerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      professionalName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      appointmentType.toLowerCase().includes(searchQuery.toLowerCase())
    );
  });

  // Handle status change
  const handleStatusChange = async (id: number, status: "Agendada" | "Em andamento" | "Finalizada" | "Cancelada") => {
    try {
      await updateStatus(clubId, user?.id || "", id, status);
      toast({
        title: "Status atualizado",
        description: `O status do agendamento foi alterado para ${status}`,
      });
    } catch (error) {
      console.error("Erro ao atualizar status:", error);
      toast({
        title: "Erro",
        description: "Não foi possível atualizar o status do agendamento",
        variant: "destructive",
      });
    }
  };

  // Handle appointment cancellation
  const handleCancelAppointment = async () => {
    if (!appointmentToCancel) return;

    try {
      await updateAppointment(clubId, user?.id || "", appointmentToCancel, {
        status: "Cancelada",
        notes: cancelReason ? `Cancelado: ${cancelReason}` : "Cancelado pelo usuário",
      });

      toast({
        title: "Agendamento cancelado",
        description: "O agendamento foi cancelado com sucesso",
      });

      setCancelDialogOpen(false);
      setAppointmentToCancel(null);
      setCancelReason("");
    } catch (error) {
      console.error("Erro ao cancelar agendamento:", error);
      toast({
        title: "Erro",
        description: "Não foi possível cancelar o agendamento",
        variant: "destructive",
      });
    }
  };

  // Handle appointment deletion
  const handleDeleteAppointment = async () => {
    if (!appointmentToDelete) return;

    try {
      await deleteAppointment(clubId, user?.id || "", appointmentToDelete);

      toast({
        title: "Agendamento excluído",
        description: "O agendamento foi excluído permanentemente",
      });

      setDeleteDialogOpen(false);
      setAppointmentToDelete(null);
    } catch (error) {
      console.error("Erro ao excluir agendamento:", error);
      toast({
        title: "Erro",
        description: "Não foi possível excluir o agendamento",
        variant: "destructive",
      });
    }
  };

  // Get badge color based on status
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Agendada":
        return <Badge variant="outline">Agendada</Badge>;
      case "Em andamento":
        return <Badge className="bg-primary">Em andamento</Badge>;
      case "Finalizada":
        return <Badge className="bg-green-500">Finalizada</Badge>;
      case "Cancelada":
        return <Badge variant="destructive">Cancelada</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Format date for display
  const formatAppointmentDate = (dateStr: string) => {
    const date = parseISO(dateStr);

    if (isToday(date)) {
      return "Hoje";
    } else if (isTomorrow(date)) {
      return "Amanhã";
    } else {
      return format(date, "dd/MM/yyyy", { locale: ptBR });
    }
  };

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>{title}</CardTitle>
            <CardDescription>{description}</CardDescription>
          </div>
          {showScheduleButton && (
            <AppointmentScheduler onAppointmentCreated={loadAppointments} />
          )}
        </CardHeader>
        <CardContent>
          {showFilters && (
            <div className="mb-4 flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
              <div className="relative flex-1">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Buscar agendamentos..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os status</SelectItem>
                  <SelectItem value="Agendada">Agendada</SelectItem>
                  <SelectItem value="Em andamento">Em andamento</SelectItem>
                  <SelectItem value="Finalizada">Finalizada</SelectItem>
                  <SelectItem value="Cancelada">Cancelada</SelectItem>
                </SelectContent>
              </Select>
              <Select value={dateFilter} onValueChange={setDateFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Data" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todas as datas</SelectItem>
                  <SelectItem value="today">Hoje</SelectItem>
                  <SelectItem value="tomorrow">Amanhã</SelectItem>
                  <SelectItem value="week">Próximos 7 dias</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" onClick={loadAppointments}>
                Filtrar
              </Button>
            </div>
          )}

          {loading ? (
            <div className="flex justify-center py-8">
              <p>Carregando agendamentos...</p>
            </div>
          ) : error ? (
            <div className="flex justify-center py-8">
              <p className="text-red-500">{error}</p>
            </div>
          ) : filteredAppointments.length === 0 ? (
            <div className="flex justify-center py-8">
              <p className="text-muted-foreground">Nenhum agendamento encontrado</p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Data</TableHead>
                    <TableHead>Horário</TableHead>
                    <TableHead>Atleta</TableHead>
                    <TableHead>Profissional</TableHead>
                    <TableHead>Tipo</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAppointments.map((appointment) => (
                    <TableRow key={appointment.id}>
                      <TableCell>
                        <div className="flex items-center">
                          <CalendarIcon className="mr-2 h-4 w-4 text-muted-foreground" />
                          {formatAppointmentDate(appointment.appointment_date)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                          {appointment.appointment_time.substring(0, 5)}
                        </div>
                      </TableCell>
                      <TableCell>{appointment.player_name}</TableCell>
                      <TableCell>{appointment.professional_name}</TableCell>
                      <TableCell>{appointment.appointment_type}</TableCell>
                      <TableCell>{getStatusBadge(appointment.status)}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Abrir menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            {appointment.status === "Agendada" && (
                              <DropdownMenuItem
                                onClick={() => handleStatusChange(appointment.id, "Em andamento")}
                              >
                                <Play className="mr-2 h-4 w-4" />
                                Iniciar atendimento
                              </DropdownMenuItem>
                            )}
                            {appointment.status === "Em andamento" && (
                              <DropdownMenuItem
                                onClick={() => handleStatusChange(appointment.id, "Finalizada")}
                              >
                                <Check className="mr-2 h-4 w-4" />
                                Finalizar atendimento
                              </DropdownMenuItem>
                            )}
                            {(appointment.status === "Agendada" || appointment.status === "Em andamento") && (
                              <>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  className="text-orange-600"
                                  onClick={() => {
                                    setAppointmentToCancel(appointment.id);
                                    setCancelDialogOpen(true);
                                  }}
                                >
                                  <X className="mr-2 h-4 w-4" />
                                  Cancelar agendamento
                                </DropdownMenuItem>
                              </>
                            )}
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              className="text-red-600"
                              onClick={() => {
                                setAppointmentToDelete(appointment.id);
                                setDeleteDialogOpen(true);
                              }}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Excluir agendamento
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Cancel Appointment Dialog */}
      <Dialog open={cancelDialogOpen} onOpenChange={setCancelDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Cancelar Agendamento</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="reason">Motivo do cancelamento</Label>
              <Textarea
                id="reason"
                placeholder="Informe o motivo do cancelamento"
                value={cancelReason}
                onChange={(e) => setCancelReason(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setCancelDialogOpen(false)}>
              Voltar
            </Button>
            <Button variant="destructive" onClick={handleCancelAppointment}>
              Cancelar Agendamento
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Appointment Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Excluir Agendamento</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p className="text-sm text-muted-foreground">
              Tem certeza que deseja excluir este agendamento permanentemente?
              Esta ação não pode ser desfeita.
            </p>
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-800 font-medium">
                ⚠️ Atenção: O agendamento será removido completamente do sistema.
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              Cancelar
            </Button>
            <Button variant="destructive" onClick={handleDeleteAppointment}>
              <Trash2 className="mr-2 h-4 w-4" />
              Excluir Permanentemente
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
