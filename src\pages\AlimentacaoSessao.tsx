import { useState, useEffect, useCallback } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { toast } from "@/components/ui/use-toast";
import {
  ChevronLeft,
  Users,
  Clock,
  MapPin,
  Download,
  Search
} from "lucide-react";
import { useCurrentClubId } from "@/context/ClubContext";
import { getClubInfo } from "@/api/api";
import {
  getMealSessions,
  getMealParticipants,
  getMealParticipantsByType,
  addMealParticipant,
  removeMealParticipant,
  getClubActiveUsers,
  MealSessionWithDetails,
  MealParticipantWithDetails,
  MealType,
  AccommodationUser
} from "@/api/meals";
import { generateMealReportV2 } from "@/utils/mealReportGeneratorV2";
import { MealTypeSection } from "@/components/MealTypeSection";

interface SessionMealType {
  id: number;
  name?: string;
  location?: string;
  address?: string;
  start_time?: string | null;
  end_time?: string | null;
}

export default function AlimentacaoSessao() {
  const navigate = useNavigate();
  const { sessionId } = useParams<{ sessionId: string }>();
  const clubId = useCurrentClubId();

  // Estados principais
  const [mealSession, setMealSession] = useState<MealSessionWithDetails | null>(null);
  const [participants, setParticipants] = useState<MealParticipantWithDetails[]>([]);
  const [clubUsers, setClubUsers] = useState<AccommodationUser[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<AccommodationUser[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(true);
  const [draggedUser, setDraggedUser] = useState<AccommodationUser | null>(null);
  const [mealTypes, setMealTypes] = useState<SessionMealType[]>([]);
  const [participantsByType, setParticipantsByType] = useState<Record<number, MealParticipantWithDetails[]>>({});

  // Função para carregar dados da sessão
  const fetchSessionData = useCallback(async () => {
    if (!sessionId) return;

    setLoading(true);
    try {
      // Buscar dados da sessão
      const sessions = await getMealSessions(clubId);
      const session = sessions.find(s => s.id === parseInt(sessionId));

      if (!session) {
        toast({
          title: "Erro",
          description: "Sessão de alimentação não encontrada.",
          variant: "destructive"
        });
        navigate("/alimentacao");
        return;
      }

      setMealSession(session);

      // Usar apenas os tipos associados à sessão
      const mealTypesData = (session.meal_types || []) as SessionMealType[];
      setMealTypes(mealTypesData);

      // Buscar participantes da sessão
      const participantsData = await getMealParticipants(clubId, parseInt(sessionId));
      setParticipants(participantsData);

      // Organizar participantes por tipo de refeição
      const participantsByTypeData: Record<number, MealParticipantWithDetails[]> = {};
      for (const mealType of mealTypesData) {
        const typeParticipants = await getMealParticipantsByType(clubId, parseInt(sessionId), mealType.id);
        participantsByTypeData[mealType.id] = typeParticipants;
      }
      setParticipantsByType(participantsByTypeData);

      // Buscar todos os usuários ativos do clube
      const users = await getClubActiveUsers(clubId);
      setClubUsers(users);
      setFilteredUsers(users);

      setLoading(false);
    } catch (error) {
      console.error("Erro ao carregar dados da sessão:", error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar os dados da sessão.",
        variant: "destructive"
      });
      setLoading(false);
    }
  }, [clubId, sessionId, navigate]);

  // Carregar dados quando o componente montar
  useEffect(() => {
    if (clubId && sessionId) {
      fetchSessionData();
    }
  }, [clubId, sessionId, fetchSessionData]);

  // Filtrar usuários baseado na busca
  useEffect(() => {
    if (!searchTerm) {
      setFilteredUsers(clubUsers);
    } else {
      const filtered = clubUsers.filter(user =>
        user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (user.nickname && user.nickname.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (user.role && user.role.toLowerCase().includes(searchTerm.toLowerCase()))
      );
      setFilteredUsers(filtered);
    }
  }, [searchTerm, clubUsers]);

  // Handlers para drag and drop
  const handleDragStart = (user: AccommodationUser) => {
    setDraggedUser(user);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = async (e: React.DragEvent, mealTypeId: number) => {
    e.preventDefault();

    if (!draggedUser || !sessionId) return;

    // Verificar se o usuário já está na lista de participantes para este tipo de refeição
    const isAlreadyParticipant = participantsByType[mealTypeId]?.some(p => {
      if (draggedUser.type === 'player') {
        return p.player_uuid === draggedUser.id && p.participant_type === 'player';
      }
      return p.participant_id === draggedUser.id && p.participant_type === 'collaborator';
    });

    if (isAlreadyParticipant) {
      toast({
        title: "Aviso",
        description: "Este usuário já está participando deste tipo de refeição.",
        variant: "destructive"
      });
      setDraggedUser(null);
      return;
    }

    try {
      await addMealParticipant(clubId, parseInt(sessionId), draggedUser.id, draggedUser.type, mealTypeId);
      toast({
        title: "Sucesso",
        description: `${draggedUser.name} foi adicionado à refeição.`,
      });

      // Recarregar participantes por tipo
      const typeParticipants = await getMealParticipantsByType(clubId, parseInt(sessionId), mealTypeId);
      setParticipantsByType(prev => ({
        ...prev,
        [mealTypeId]: typeParticipants
      }));

      // Recarregar participantes gerais
      const participantsData = await getMealParticipants(clubId, parseInt(sessionId));
      setParticipants(participantsData);
    } catch (error: any) {
      toast({
        title: "Erro",
        description: error.message || "Erro ao adicionar participante.",
        variant: "destructive"
      });
    }

    setDraggedUser(null);
  };

  // Handler para remover participante
  const handleRemoveParticipant = async (participant: MealParticipantWithDetails) => {
    if (window.confirm(`Tem certeza que deseja remover ${participant.participant_name} desta refeição?`)) {
      try {
        await removeMealParticipant(clubId, participant.id);
        toast({
          title: "Sucesso",
          description: "Participante removido da refeição.",
        });

        // Recarregar participantes por tipo
        if (participant.meal_type_id) {
          const typeParticipants = await getMealParticipantsByType(clubId, parseInt(sessionId!), participant.meal_type_id);
          setParticipantsByType(prev => ({
            ...prev,
            [participant.meal_type_id!]: typeParticipants
          }));
        }

        // Recarregar participantes gerais
        const participantsData = await getMealParticipants(clubId, parseInt(sessionId!));
        setParticipants(participantsData);
      } catch (error: any) {
        toast({
          title: "Erro",
          description: error.message || "Erro ao remover participante.",
          variant: "destructive"
        });
      }
    }
  };

  // Handler para gerar relatório
  const handleGenerateReport = async () => {
    if (!mealSession) return;

    try {
      const clubInfo = await getClubInfo(clubId);
      await generateMealReportV2({
        session: mealSession,
        participantsByType,
        mealTypes,
        clubInfo: {
          name: clubInfo.name,
          logo: clubInfo.logo
        }
      });



      toast({
        title: "Sucesso",
        description: "Relatório gerado com sucesso.",
      });
    } catch (error) {
      console.error("Erro ao gerar relatório:", error);
      toast({
        title: "Erro",
        description: "Erro ao gerar relatório.",
        variant: "destructive"
      });
    }
  };

  // Mostrar todos os usuários filtrados (permitindo múltiplas refeições)
  const availableUsers = filteredUsers;

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-team-blue"></div>
        </div>
      </div>
    );
  }

  if (!mealSession) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Sessão não encontrada</h2>
          <Button onClick={() => navigate("/alimentacao")}>
            Voltar para Alimentação
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Button variant="ghost" onClick={() => navigate("/alimentacao")} className="mr-2">
            <ChevronLeft className="h-4 w-4 mr-1" />
            Voltar
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Sessão de Alimentação</h1>
            <p className="text-gray-600">{new Date(`${mealSession.date}T00:00:00`).toLocaleDateString('pt-BR')}</p>
          </div>
        </div>
        <Button onClick={handleGenerateReport}>
          <Download className="h-4 w-4 mr-1" />
          Gerar Relatório
        </Button>
      </div>

      {/* Informações da sessão */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Detalhes da Sessão</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center">
              <Clock className="h-4 w-4 mr-2 text-gray-500" />
              <div>
                <p className="text-sm text-gray-500">Data</p>
                <p className="font-medium">
                {new Date(`${mealSession.date}T00:00:00`).toLocaleDateString('pt-BR')}
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <MapPin className="h-4 w-4 mr-2 text-gray-500 mt-1" />
              <div>
                <p className="text-sm text-gray-500">Refeições</p>
                {mealSession.meal_types?.length ? (
                  <ul className="text-sm space-y-1">
                   {mealSession.meal_types.map((mt) => {
                      const count = participantsByType[mt.id]?.length || 0;
                      return (
                        <li key={mt.id}>
                          <span className="font-medium">{mt.name}</span> - {mt.location || "Sem local"}
                          {mt.start_time && mt.end_time && (
                            <> ({mt.start_time} - {mt.end_time})</>
                          )} - {count} participante{count !== 1 ? "s" : ""}
                        </li>
                      );
                    })}
                  </ul>
                ) : (
                  <p className="font-medium">Não informado</p>
                )}
              </div>
            </div>

            <div className="flex items-center">
              <Users className="h-4 w-4 mr-2 text-gray-500" />
              <div>
                <p className="text-sm text-gray-500">Participantes</p>
                <p className="font-medium">{participants.length}</p>
              </div>
            </div>
          </div>

          {mealSession.notes && (
            <div className="mt-4">
              <p className="text-sm text-gray-500">Observações</p>
              <p className="text-gray-700">{mealSession.notes}</p>
            </div>
          )}
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Usuários disponíveis */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle>Usuários do Clube</CardTitle>
            <p className="text-sm text-gray-600">
              Arraste os usuários para os tipos de refeição
            </p>
          </CardHeader>
          <CardContent>
            {/* Campo de busca */}
            <div className="relative mb-4">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Buscar por nome..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            {availableUsers.length === 0 ? (
              <p className="text-center text-gray-500 py-4">
                {searchTerm ? "Nenhum usuário encontrado" : "Nenhum usuário ativo encontrado"}
              </p>
            ) : (
              <div className="space-y-2">
                {availableUsers.map((user) => (
                  <div
                    key={`${user.type}-${user.id}`}
                    draggable
                    onDragStart={() => handleDragStart(user)}
                    className="flex items-center p-3 border rounded-lg cursor-move hover:bg-gray-50 transition-colors"
                  >
                    <Avatar className="h-8 w-8 mr-3">
                      <AvatarImage src={user.image} />
                      <AvatarFallback>
                        {user.name.charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <p className="font-medium text-sm">{user.name}</p>
                      {user.nickname && (
                        <p className="text-xs text-gray-500">"{user.nickname}"</p>
                      )}
                      {user.role && (
                        <p className="text-xs text-gray-500">{user.role}</p>
                      )}
                    </div>
                    <Badge variant={user.type === 'player' ? 'default' : 'secondary'} className="text-xs">
                      {user.type === 'player' ? 'Jogador' : 'Colaborador'}
                    </Badge>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Seções de tipos de refeição */}
        <div className="lg:col-span-3 space-y-6">
          {mealTypes.map((mealType) => (
            <MealTypeSection
              key={mealType.id}
              mealType={mealType}
              participants={participantsByType[mealType.id] || []}
              availableUsers={availableUsers}
              onDragOver={handleDragOver}
              onDrop={handleDrop}
              onRemoveParticipant={handleRemoveParticipant}
              sessionDate={mealSession?.date}
            />
          ))}
        </div>
      </div>
    </div>
  );
}