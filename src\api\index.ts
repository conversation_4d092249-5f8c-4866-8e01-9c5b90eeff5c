
// Re-exporta tudo de api.ts
export * from './api';

// Re-export player evaluation invitation functions
export type { PlayerEvaluationInvitation } from './playerEvaluationInvitations';
export {
  createPlayerEvaluationInvitation,
  getPlayerEvaluationInvitation,
  validatePlayerEvaluationInvitation,
  usePlayerEvaluationInvitation,
  getPlayerEvaluationInvitations,
  getPlayersInEvaluation,
  schedulePlayerEvaluation,
  updatePlayerEvaluationStatus as updatePlayerEvaluationInvitationStatus,
  sendEvaluationInvitationEmail,
  sendEvaluationScheduleEmail,
  updateDocumentsStatus,
  updateEvaluationStatus,
  sendDocumentStatusEmail,
  sendEvaluationStatusEmail,
  deletePlayerEvaluationInvitation
} from './playerEvaluationInvitations';

// Re-export match lineup functions
export type {
  MatchLineup,
  MatchSquadMember,
  MatchSubstitution,
  MatchPlayerMinutes
} from './matchLineups';
export {
  getMatchLineup,
  saveMatchLineup,
  getMatchSquad,
  addMatchSquadMember,
  removeMatchSquadMember,
  updateMatchSquadMember,
  getMatchSubstitutions,
  createMatchSubstitution,
  getMatchPlayerMinutes,
  saveMatchPlayerMinutes,
  finalizeMatchMinutes,
  getAvailablePlayersForMatch
} from './matchLineups';
