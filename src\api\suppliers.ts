import { supabase } from "@/integrations/supabase/client";
import { withPermission, withAuditLog } from "./middleware";
import { SUPPLIER_PERMISSIONS } from "@/constants/permissions";

// Types
export type Supplier = {
  id: number;
  club_id: number;
  company_name: string;
  address?: string;
  address_number?: string;
  zip_code?: string;
  state?: string;
  city?: string;
  phone1?: string;
  phone2?: string;
  email?: string;
  expiration_date?: string;
  bank_name?: string;
  bank_agency?: string;
  bank_account?: string;
  bank_pix?: string;
  observation?: string;
  created_at: string;
  updated_at: string;
};

/**
 * Get all suppliers for a club
 * @param clubId Club ID
 * @returns List of suppliers
 */
export async function getSuppliers(clubId: number): Promise<Supplier[]> {
  const { data, error } = await supabase
    .from("suppliers")
    .select("*")
    .eq("club_id", clubId)
    .order("company_name");

  if (error) {
    console.error("Error fetching suppliers:", error);
    throw new Error(`Error fetching suppliers: ${error.message}`);
  }

  return data || [];
}

/**
 * Get a supplier by ID
 * @param clubId Club ID
 * @param id Supplier ID
 * @returns Supplier
 */
export async function getSupplierById(clubId: number, id: number): Promise<Supplier> {
  const { data, error } = await supabase
    .from("suppliers")
    .select("*")
    .eq("club_id", clubId)
    .eq("id", id)
    .single();

  if (error) {
    console.error("Error fetching supplier:", error);
    throw new Error(`Error fetching supplier: ${error.message}`);
  }

  return data;
}

/**
 * Create a new supplier
 * @param clubId Club ID
 * @param userId User ID
 * @param supplier Supplier data
 * @returns Created supplier
 */
export async function createSupplier(
  clubId: number,
  userId: string,
  supplier: Omit<Supplier, "id" | "club_id" | "created_at" | "updated_at">
): Promise<Supplier> {
  return withPermission(
    clubId,
    userId,
    SUPPLIER_PERMISSIONS.CREATE,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "supplier.create",
        { company_name: supplier.company_name },
        async () => {
          try {
            const { data, error } = await supabase
              .from("suppliers")
              .insert({
                club_id: clubId,
                ...supplier,
              })
              .select()
              .single();

            if (error) {
              console.error("Error creating supplier:", error);
              throw new Error(`Error creating supplier: ${error.message}`);
            }

            return data;
          } catch (error: any) {
            console.error("Error creating supplier:", error);
            throw new Error(error.message || "Error creating supplier");
          }
        }
      );
    }
  );
}

/**
 * Update a supplier
 * @param clubId Club ID
 * @param userId User ID
 * @param id Supplier ID
 * @param supplier Supplier data
 * @returns Updated supplier
 */
export async function updateSupplier(
  clubId: number,
  userId: string,
  id: number,
  supplier: Partial<Omit<Supplier, "id" | "club_id" | "created_at" | "updated_at">>
): Promise<Supplier> {
  return withPermission(
    clubId,
    userId,
    SUPPLIER_PERMISSIONS.EDIT,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "supplier.update",
        { id, ...supplier },
        async () => {
          try {
            const { data, error } = await supabase
              .from("suppliers")
              .update({
                ...supplier,
                updated_at: new Date().toISOString(),
              })
              .eq("club_id", clubId)
              .eq("id", id)
              .select()
              .single();

            if (error) {
              console.error("Error updating supplier:", error);
              throw new Error(`Error updating supplier: ${error.message}`);
            }

            return data;
          } catch (error: any) {
            console.error("Error updating supplier:", error);
            throw new Error(error.message || "Error updating supplier");
          }
        }
      );
    }
  );
}

/**
 * Delete a supplier
 * @param clubId Club ID
 * @param userId User ID
 * @param id Supplier ID
 * @returns Success status
 */
export async function deleteSupplier(
  clubId: number,
  userId: string,
  id: number
): Promise<boolean> {
  return withPermission(
    clubId,
    userId,
    SUPPLIER_PERMISSIONS.DELETE,
    () => {
      return withAuditLog(
        clubId,
        userId,
        "supplier.delete",
        { id },
        async () => {
          try {
            const { error } = await supabase
              .from("suppliers")
              .delete()
              .eq("club_id", clubId)
              .eq("id", id);

            if (error) {
              console.error("Error deleting supplier:", error);
              throw new Error(`Error deleting supplier: ${error.message}`);
            }

            return true;
          } catch (error: any) {
            console.error("Error deleting supplier:", error);
            throw new Error(error.message || "Error deleting supplier");
          }
        }
      );
    }
  );
}
