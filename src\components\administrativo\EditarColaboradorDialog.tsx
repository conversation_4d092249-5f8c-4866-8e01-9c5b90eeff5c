import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { toast } from "@/components/ui/use-toast";
import { Collaborator, updateCollaborator } from "@/api/api";
import { useUser } from "@/context/UserContext";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Loader2 } from "lucide-react";
import { validateCPF } from "@/lib/validators";
import { fetchAddressByCEP } from "@/lib/cep";

interface EditarColaboradorDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: number;
  collaborator: Collaborator;
  onSuccess?: () => void;
}

export function EditarColaboradorDialog({
  open,
  onOpenChange,
  clubId,
  collaborator,
  onSuccess
}: EditarColaboradorDialogProps) {
  const { user } = useUser();
  const [activeTab, setActiveTab] = useState("basic");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Campos básicos
  const [fullName, setFullName] = useState(collaborator.full_name || "");
  const [role, setRole] = useState(collaborator.role || "");
  const [phone, setPhone] = useState(collaborator.phone || "");
  const [email, setEmail] = useState(collaborator.email || "");

  // Campos de documentos
  const [cpf, setCpf] = useState(collaborator.cpf || "");
  const [birthDate, setBirthDate] = useState(collaborator.birth_date ?
    new Date(collaborator.birth_date).toISOString().split('T')[0] : "");
  const [credentialNumber, setCredentialNumber] = useState(collaborator.credential_number || "");

  // Campos de endereço
  const [zipCode, setZipCode] = useState(collaborator.zip_code || "");
  const [state, setState] = useState(collaborator.state || "");
  const [city, setCity] = useState(collaborator.city || "");
  const [address, setAddress] = useState(collaborator.address || "");
  const [addressNumber, setAddressNumber] = useState(collaborator.address_number || "");

  // Atualizar campos quando o colaborador mudar
  useEffect(() => {
    setFullName(collaborator.full_name || "");
    setRole(collaborator.role || "");
    setPhone(collaborator.phone || "");
    setEmail(collaborator.email || "");
    setCpf(collaborator.cpf || "");
    setBirthDate(collaborator.birth_date ?
      new Date(collaborator.birth_date).toISOString().split('T')[0] : "");
    setCredentialNumber(collaborator.credential_number || "");
    setZipCode(collaborator.zip_code || "");
    setState(collaborator.state || "");
    setCity(collaborator.city || "");
    setAddress(collaborator.address || "");
    setAddressNumber(collaborator.address_number || "");
  }, [collaborator]);

  // Função para buscar endereço pelo CEP
  const handleZipCodeBlur = async () => {
    if (zipCode.length === 8 || zipCode.length === 9) {
      try {
        const addressData = await fetchAddressByCEP(zipCode);
        if (addressData) {
          setState(addressData.state);
          setCity(addressData.city);
          setAddress(addressData.street);
        }
      } catch (err) {
        console.error("Erro ao buscar CEP:", err);
      }
    }
  };

  // Função para salvar o colaborador
  const handleSave = async () => {
    try {
      setLoading(true);
      setError(null);

      // Validar campos obrigatórios
      if (!fullName) {
        throw new Error("O nome completo é obrigatório");
      }

      if (!role) {
        throw new Error("A função é obrigatória");
      }

      // Validar CPF se preenchido
      if (cpf && !validateCPF(cpf)) {
        throw new Error("CPF inválido");
      }

      // Atualizar o colaborador
      await updateCollaborator(
        clubId,
        user?.id || "",
        collaborator.id,
        {
          full_name: fullName,
          role,
          role_type: "technical", // Mantido para compatibilidade com o banco de dados
          phone: phone || undefined,
          email: email || undefined,
          cpf: cpf || undefined,
          birth_date: birthDate || undefined,
          credential_number: credentialNumber || undefined,
          zip_code: zipCode || undefined,
          state: state || undefined,
          city: city || undefined,
          address: address || undefined,
          address_number: addressNumber || undefined,
        }
      );

      toast({
        title: "Sucesso",
        description: "Colaborador atualizado com sucesso",
      });

      // Fechar o diálogo
      onOpenChange(false);

      // Chamar callback de sucesso
      if (onSuccess) {
        onSuccess();
      }
    } catch (err: any) {
      console.error("Erro ao atualizar colaborador:", err);
      setError(err.message || "Erro ao atualizar colaborador");
      toast({
        title: "Erro",
        description: err.message || "Erro ao atualizar colaborador",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Editar Colaborador</DialogTitle>
        </DialogHeader>

        {error && (
          <div className="bg-red-50 text-red-700 p-3 rounded-md text-sm mb-4">
            {error}
          </div>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="basic">Informações Básicas</TabsTrigger>
            <TabsTrigger value="documents">Documentos</TabsTrigger>
            <TabsTrigger value="address">Endereço</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label htmlFor="fullName">Nome Completo *</Label>
                <Input
                  id="fullName"
                  value={fullName}
                  onChange={(e) => setFullName(e.target.value)}
                  placeholder="Nome completo do colaborador"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="role">Função *</Label>
                <Select
                  value={role}
                  onValueChange={setRole}
                >
                  <SelectTrigger id="role">
                    <SelectValue placeholder="Selecione a função" />
                  </SelectTrigger>
                  <SelectContent className="max-h-[200px] overflow-y-auto">
                  <SelectItem value="Técnico">Técnico</SelectItem>
                      <SelectItem value="Auxiliar técnico">Auxiliar técnico</SelectItem>
                      <SelectItem value="Preparador de goleiro">Preparador de goleiro</SelectItem>
                      <SelectItem value="Preparador físico">Preparador físico</SelectItem>
                      <SelectItem value="Supervisor">Supervisor</SelectItem>
                      <SelectItem value="Massagista">Massagista</SelectItem>
                      <SelectItem value="Analista de Desempenho">Analista de Desempenho</SelectItem>
                      <SelectItem value="Assessor de Imprensa">Assessor de Imprensa</SelectItem>
                      <SelectItem value="Fisioterapeuta">Fisioterapeuta</SelectItem>
                      <SelectItem value="Fisiologista">Fisiologista</SelectItem>
                      <SelectItem value="Psicóloga">Psicóloga</SelectItem>
                      <SelectItem value="Nutricionista">Nutricionista</SelectItem>
                      <SelectItem value="Coordenador">Coordenador</SelectItem>
                      <SelectItem value="Motorista">Motorista</SelectItem>
                      <SelectItem value="Roupeiro">Roupeiro</SelectItem>
                      <SelectItem value="Massa terapeuta">Massa terapeuta</SelectItem>
                      <SelectItem value="Gerente de futebol">Gerente de futebol</SelectItem>
                      <SelectItem value="CEO">CEO</SelectItem>
                      <SelectItem value="Gerente administrativo">Gerente administrativo</SelectItem>
                      <SelectItem value="Cozinheira">Cozinheira</SelectItem>
                      <SelectItem value="Cozinheiro">Cozinheiro</SelectItem>
                      <SelectItem value="Gerente de operações">Gerente de operações</SelectItem>
                      <SelectItem value="Presidente">Presidente</SelectItem>
                      <SelectItem value="Vice presidente">Vice presidente</SelectItem>
                      <SelectItem value="Sócio">Sócio</SelectItem>
                      <SelectItem value="Fotógrafo">Fotógrafo</SelectItem>
                      <SelectItem value="Diretor">Diretor</SelectItem>
                      <SelectItem value="Comunicação">Comunicação</SelectItem>
                      <SelectItem value="Auxiliar de limpeza">Auxiliar de limpeza</SelectItem>
                      <SelectItem value="Serviço geral">Serviço geral</SelectItem>
                      <SelectItem value="Jardineiro">Jardineiro</SelectItem>
                      <SelectItem value="Lavadora de roupas">Lavadora de roupas</SelectItem>
                      <SelectItem value="Assistente social">Assistente social</SelectItem>
                      <SelectItem value="Outros">Outros</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="phone">Telefone</Label>
                  <Input
                    id="phone"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                    placeholder="(00) 00000-0000"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">E-mail</Label>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="documents" className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="cpf">CPF</Label>
                  <Input
                    id="cpf"
                    value={cpf}
                    onChange={(e) => setCpf(e.target.value)}
                    placeholder="000.000.000-00"
                    maxLength={14}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="birthDate">Data de Nascimento</Label>
                  <Input
                    id="birthDate"
                    type="date"
                    value={birthDate}
                    onChange={(e) => setBirthDate(e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="credentialNumber">Número de Registro no Conselho</Label>
                <Input
                  id="credentialNumber"
                  value={credentialNumber}
                  onChange={(e) => setCredentialNumber(e.target.value)}
                  placeholder="Número de registro profissional"
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="address" className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="zipCode">CEP</Label>
                  <Input
                    id="zipCode"
                    value={zipCode}
                    onChange={(e) => setZipCode(e.target.value)}
                    onBlur={handleZipCodeBlur}
                    placeholder="00000-000"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="state">Estado</Label>
                  <Input
                    id="state"
                    value={state}
                    onChange={(e) => setState(e.target.value)}
                    placeholder="UF"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="city">Cidade</Label>
                <Input
                  id="city"
                  value={city}
                  onChange={(e) => setCity(e.target.value)}
                  placeholder="Cidade"
                />
              </div>

              <div className="grid grid-cols-4 gap-4">
                <div className="col-span-3 space-y-2">
                  <Label htmlFor="address">Endereço</Label>
                  <Input
                    id="address"
                    value={address}
                    onChange={(e) => setAddress(e.target.value)}
                    placeholder="Rua, Avenida, etc."
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="addressNumber">Número</Label>
                  <Input
                    id="addressNumber"
                    value={addressNumber}
                    onChange={(e) => setAddressNumber(e.target.value)}
                    placeholder="Nº"
                  />
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleSave} disabled={loading}>
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Salvar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
