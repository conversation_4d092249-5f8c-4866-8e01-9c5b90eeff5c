import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { DatePicker } from "@/components/ui/date-picker";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "@/components/ui/use-toast";
import { Loader2, Plus, FileText, Trash2, Edit, Download } from "lucide-react";
import { useCurrentClubId } from "@/context/ClubContext";
import { useUser } from "@/context/UserContext";
import { useCategoriesStore } from "@/store/useCategoriesStore";
import {
  getCallups,
  getCallupById,
  createCallup,
  updateCallup,
  deleteCallup,
  getCallupPlayers,
  addPlayerToCallup,
  removePlayerFromCallup,
  generateCallupPDF,
  Callup,
  CallupPlayer
} from "@/api/callups";
import { getCategories, getCategoryPlayers, Category } from "@/api/api";
import { getClubInfo, ClubInfo } from "@/api/api";
import { getClubUsers, ClubUser } from "@/api/users";
import { ImageUpload } from "@/components/ui/image-upload";
import { uploadClubLogo } from "@/api/storage-simple";

// Componente principal da página de Convocação
export default function ConvocacaoPage() {
  const navigate = useNavigate();
  const clubId = useCurrentClubId();
  const { user } = useUser();
  const { selectedCategory } = useCategoriesStore();

  const [callups, setCallups] = useState<Callup[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedCallup, setSelectedCallup] = useState<Callup | null>(null);
  const [clubInfo, setClubInfo] = useState<ClubInfo | null>(null);

  // Carregar convocações e categorias
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Carregar categorias
        const categoriesData = await getCategories(clubId);
        setCategories(categoriesData);

        // Carregar convocações
        const callupsData = await getCallups(clubId, selectedCategory?.id || undefined);
        setCallups(callupsData);

        // Carregar informações do clube
        const clubInfoData = await getClubInfo(clubId);
        setClubInfo(clubInfoData);
      } catch (error) {
        console.error("Erro ao carregar dados:", error);
        toast({
          title: "Erro",
          description: "Não foi possível carregar as convocações.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [clubId, selectedCategory]);

  // Abrir o diálogo para criar uma nova convocação
  const handleNewCallup = () => {
    setSelectedCallup(null);
    setOpenDialog(true);
  };

  // Abrir o diálogo para editar uma convocação existente
  const handleEditCallup = async (callup: Callup) => {
    setSelectedCallup(callup);
    setOpenDialog(true);
  };

  // Excluir uma convocação
  const handleDeleteCallup = async (callup: Callup) => {
    if (!confirm(`Tem certeza que deseja excluir a convocação "${callup.tournament_type}"?`)) {
      return;
    }

    try {
      await deleteCallup(clubId, callup.id, user?.id);

      // Atualizar a lista de convocações
      setCallups(callups.filter(c => c.id !== callup.id));

      toast({
        title: "Sucesso",
        description: "Convocação excluída com sucesso.",
      });
    } catch (error) {
      console.error("Erro ao excluir convocação:", error);
      toast({
        title: "Erro",
        description: "Não foi possível excluir a convocação.",
        variant: "destructive",
      });
    }
  };

  // Visualizar detalhes de uma convocação
  const handleViewCallup = (callup: Callup) => {
    navigate(`/convocacao/${callup.id}`);
  };

  // Renderizar a lista de convocações
  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Convocações</h1>
          <p className="text-muted-foreground">
            Gerencie as convocações de jogadores para partidas
          </p>
        </div>
        <Button onClick={handleNewCallup}>
          <Plus className="mr-2 h-4 w-4" />
          Nova Convocação
        </Button>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      ) : callups.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center h-64">
            <p className="text-muted-foreground mb-4">
              Nenhuma convocação encontrada
            </p>
            <Button onClick={handleNewCallup}>
              <Plus className="mr-2 h-4 w-4" />
              Criar Convocação
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {callups.map((callup) => (
            <Card key={callup.id} className="overflow-hidden">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">{callup.tournament_type}</CardTitle>
                <CardDescription className="text-sm">
                  {format(new Date(callup.match_date), "dd/MM/yyyy", { locale: ptBR })}
                </CardDescription>
              </CardHeader>
              <CardContent className="pb-3 space-y-2">
                <div className="flex items-center justify-center gap-3 mb-2">
                  {callup.home_club_logo ? (
                    <img
                      src={callup.home_club_logo}
                      alt="Mandante"
                      className="h-10 w-10 object-contain"
                    />
                  ) : (
                    <div className="h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center">
                      <span className="text-gray-500 text-sm">M</span>
                    </div>
                  )}
                  <span className="text-lg font-semibold">vs</span>
                  {callup.away_club_logo ? (
                    <img
                      src={callup.away_club_logo}
                      alt="Visitante"
                      className="h-10 w-10 object-contain"
                    />
                  ) : (
                    <div className="h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center">
                      <span className="text-gray-500 text-sm">V</span>
                    </div>
                  )}
                </div>
                <p className="text-xs text-muted-foreground">
                  <strong>Local:</strong> {callup.match_location}
                </p>
                <p className="text-xs text-muted-foreground">
                  <strong>Categoria:</strong> {categories.find(c => c.id === callup.category_id)?.name || "Não definida"}
                </p>
              </CardContent>
              <CardFooter className="flex justify-between pt-2 gap-2">
                <Button variant="outline" size="sm" onClick={() => handleViewCallup(callup)} className="flex-1">
                  <FileText className="h-3 w-3 mr-1" />
                  Detalhes
                </Button>
                <div className="flex gap-1">
                  <Button variant="outline" size="sm" onClick={() => handleEditCallup(callup)}>
                    <Edit className="h-3 w-3" />
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => handleDeleteCallup(callup)}>
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}

      {/* Diálogo para criar/editar convocação */}
      {openDialog && (
        <CallupDialog
          open={openDialog}
          onOpenChange={setOpenDialog}
          callup={selectedCallup}
          categories={categories}
          clubId={clubId}
          userId={user?.id || ""}
          onSuccess={(newCallup) => {
            if (selectedCallup) {
              // Atualizar convocação existente na lista
              setCallups(callups.map(c => c.id === newCallup.id ? newCallup : c));
            } else {
              // Adicionar nova convocação à lista
              setCallups([newCallup, ...callups]);
            }
            setOpenDialog(false);
          }}
        />
      )}
    </div>
  );
}

// Componente de diálogo para criar/editar convocação
interface CallupDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  callup: Callup | null;
  categories: Category[];
  clubId: number;
  userId: string;
  onSuccess: (callup: Callup) => void;
}

function CallupDialog({
  open,
  onOpenChange,
  callup,
  categories,
  clubId,
  userId,
  onSuccess
}: CallupDialogProps) {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<Partial<Callup>>(
    callup || {
      category_id: categories.length > 0 ? categories[0].id : undefined,
      tournament_type: "",
      match_date: new Date().toISOString(),
      match_location: "",
      match_schedule: "",
      hotel_control: ""
    }
  );

  // Atualizar o estado do formulário quando o callup ou as categorias mudarem
  useEffect(() => {
    if (callup) {
      setFormData(callup);
    } else {
      setFormData({
        category_id: categories.length > 0 ? categories[0].id : undefined,
        tournament_type: "",
        match_date: new Date().toISOString(),
        match_location: "",
        match_schedule: "",
        hotel_control: ""
      });
    }
  }, [callup, categories]);

  // Manipular mudanças nos campos do formulário
  const handleChange = (field: string, value: any) => {
    setFormData({
      ...formData,
      [field]: value
    });
  };

  // Manipular envio do formulário
  const handleSubmit = async () => {
    try {
      setLoading(true);

      // Validar campos obrigatórios
      if (!formData.category_id || !formData.tournament_type || !formData.match_date || !formData.match_location) {
        toast({
          title: "Erro",
          description: "Preencha todos os campos obrigatórios.",
          variant: "destructive",
        });
        return;
      }

      // Processar as imagens para garantir que sejam URLs válidas do Supabase
      const processedFormData = { ...formData };

      // Função para processar uma imagem
      const processImage = async (imageUrl: string | undefined, fieldName: string) => {
        if (!imageUrl) return undefined;

        console.log(`Processing image ${fieldName}:`, imageUrl);

        // Se a URL já é do Supabase, mantê-la
        if (imageUrl.includes('supabase.co')) {
          console.log(`Image ${fieldName} is already a Supabase URL`);
          return imageUrl;
        }

        // Se é uma URL blob, precisamos convertê-la para um arquivo e fazer upload
        if (imageUrl.startsWith('blob:')) {
          try {
            console.log(`Image ${fieldName} is a blob URL, converting to file`);
            // Buscar o blob
            const response = await fetch(imageUrl);
            const blob = await response.blob();
            console.log(`Blob type for ${fieldName}:`, blob.type);

            // Criar um arquivo a partir do blob
            const file = new File([blob], `${fieldName}.${blob.type.split('/')[1] || 'png'}`, { type: blob.type });
            console.log(`Created file for ${fieldName}:`, file.name, file.type, file.size);

            // Fazer upload para o Supabase
            const uploadedUrl = await uploadClubLogo(clubId.toString(), file);
            console.log(`Uploaded ${fieldName} to Supabase:`, uploadedUrl);
            return uploadedUrl;
          } catch (err) {
            console.error(`Erro ao processar imagem ${fieldName}:`, err);
            return undefined;
          }
        }

        console.log(`Image ${fieldName} is not a blob or Supabase URL, returning as is`);
        return imageUrl;
      };

      // Processar cada imagem
      if (formData.home_club_logo) {
        processedFormData.home_club_logo = await processImage(formData.home_club_logo, 'home_club_logo');
        console.log("Processed home_club_logo:", processedFormData.home_club_logo);
      }

      if (formData.away_club_logo) {
        processedFormData.away_club_logo = await processImage(formData.away_club_logo, 'away_club_logo');
        console.log("Processed away_club_logo:", processedFormData.away_club_logo);
      }

      if (formData.competition_image) {
        processedFormData.competition_image = await processImage(formData.competition_image, 'competition_image');
        console.log("Processed competition_image:", processedFormData.competition_image);
      }

      let result: Callup;

      if (callup) {
        // Atualizar convocação existente
        result = await updateCallup(clubId, callup.id, processedFormData, userId);
        toast({
          title: "Sucesso",
          description: "Convocação atualizada com sucesso.",
        });
      } else {
        // Criar nova convocação
        result = await createCallup(clubId, processedFormData as Omit<Callup, "id" | "club_id" | "created_at" | "updated_at">, userId);
        toast({
          title: "Sucesso",
          description: "Convocação criada com sucesso.",
        });
      }

      onSuccess(result);
    } catch (error) {
      console.error("Erro ao salvar convocação:", error);
      toast({
        title: "Erro",
        description: "Não foi possível salvar a convocação.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {callup ? "Editar Convocação" : "Nova Convocação"}
          </DialogTitle>
          <DialogDescription>
            Preencha os dados da convocação para jogadores
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="category_id">Categoria *</Label>
              <Select
                value={formData.category_id?.toString()}
                onValueChange={(value) => handleChange("category_id", parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione uma categoria" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id.toString()}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="tournament_type">Tipo de Torneio *</Label>
              <Input
                id="tournament_type"
                value={formData.tournament_type || ""}
                onChange={(e) => handleChange("tournament_type", e.target.value)}
                placeholder="Ex: Campeonato Paulista A3"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="match_date">Data do Jogo *</Label>
              <DatePicker
                date={formData.match_date ? new Date(formData.match_date) : undefined}
                setDate={(date) => handleChange("match_date", date?.toISOString())}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="match_location">Local do Jogo *</Label>
              <Input
                id="match_location"
                value={formData.match_location || ""}
                onChange={(e) => handleChange("match_location", e.target.value)}
                placeholder="Ex: Estádio Municipal"
              />
            </div>
          </div>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Escudo do Clube Mandante</Label>
              <ImageUpload
                value={formData.home_club_logo || ""}
                onChange={(value, file) => {
                  handleChange("home_club_logo", value);
                }}
                placeholder="Escudo do Mandante"
              />
            </div>

            <div className="space-y-2">
              <Label>Escudo do Clube Visitante</Label>
              <ImageUpload
                value={formData.away_club_logo || ""}
                onChange={(value, file) => {
                  handleChange("away_club_logo", value);
                }}
                placeholder="Escudo do Visitante"
              />
            </div>
          </div>
        </div>

        <div className="space-y-4 mt-4">
          <div className="space-y-2">
            <Label htmlFor="match_schedule">Programação do Jogo</Label>
            <Textarea
              id="match_schedule"
              value={formData.match_schedule || ""}
              onChange={(e) => handleChange("match_schedule", e.target.value)}
              placeholder="Detalhes da programação do jogo (horários, logística, etc.)"
              rows={5}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="hotel_control">Controle de Hotel</Label>
            <Textarea
              id="hotel_control"
              value={formData.hotel_control || ""}
              onChange={(e) => handleChange("hotel_control", e.target.value)}
              placeholder="Informações sobre hospedagem (quartos, responsáveis, etc.)"
              rows={5}
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {callup ? "Atualizar" : "Criar"} Convocação
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
