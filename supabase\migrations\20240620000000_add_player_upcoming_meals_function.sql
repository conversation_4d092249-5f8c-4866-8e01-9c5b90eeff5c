-- Cria a função para buscar refeições futuras de um jogador
CREATE OR REPLACE FUNCTION public.get_player_upcoming_meals(
  p_club_id bigint,
  p_player_id uuid,
  p_today date
)
RETURNS TABLE (
  id bigint,
  meal_session_id bigint,
  meal_type_name text,
  meal_type_location text,
  meal_type_address text,
  date text,
  meal_time text
)
LANGUAGE sql
AS $$
  SELECT 
    mp.id,
    mp.meal_session_id,
    mt.name as meal_type_name,
    COALESCE(mt.location, '') as meal_type_location,
    COALESCE(mt.address, '') as meal_type_address,
    ms.date::text as date,
    CASE 
      WHEN msl.start_time IS NOT NULL AND msl.end_time IS NOT NULL 
      THEN msl.start_time || ' - ' || msl.end_time
      WHEN ms.time IS NOT NULL 
      THEN ms.time::text
      ELSE ''
    END as meal_time
  FROM 
    meal_participants mp
    JOIN meal_sessions ms ON mp.meal_session_id = ms.id
    JOIN meal_types mt ON mp.meal_type_id = mt.id
    LEFT JOIN meal_session_locations msl ON ms.id = msl.meal_session_id AND (mt.id = msl.meal_type_id OR msl.meal_type_id IS NULL)
  WHERE 
    mp.club_id = p_club_id
    AND mp.participant_type = 'player'
    AND mp.player_uuid = p_player_id
    -- Mostrar apenas refeições futuras ou de hoje que ainda não terminaram
    AND (
      -- Se for uma data futura, sempre mostra
      ms.date > p_today
      -- Se for hoje, verifica o horário
      OR (
        ms.date = p_today
        AND (
          -- Se não tem horário definido, mostra
          msl.end_time IS NULL
          -- Ou se tem horário definido, verifica se o horário de término é maior que o atual
          OR (
            msl.end_time IS NOT NULL 
            AND (
              (msl.end_time::time > '00:00' AND msl.end_time::time > timezone('America/Sao_Paulo', now())::time)
              -- Ou se for após a meia-noite (para refeições que terminam no dia seguinte)
              OR (msl.end_time::time <= '00:00' AND timezone('America/Sao_Paulo', now())::time < '06:00')
            )
          )
        )
      )
    )
  ORDER BY 
    ms.date ASC, 
    msl.start_time ASC

$$;

-- Concede permissão para a função ser executada por usuários autenticados
GRANT EXECUTE ON FUNCTION public.get_player_upcoming_meals(bigint, uuid, date) TO authenticated;
