import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers':
    'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

const SUPABASE_URL = Deno.env.get('SUPABASE_URL') ?? ''
const SUPABASE_SERVICE_ROLE_KEY =
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    console.log('CORS preflight for password-reset-confirm')
    return new Response('ok', { status: 200, headers: corsHeaders })
  }

  const missingEnv = []
  if (!SUPABASE_URL) missingEnv.push('SUPABASE_URL')
  if (!SUPABASE_SERVICE_ROLE_KEY) missingEnv.push('SUPABASE_SERVICE_ROLE_KEY')
  if (missingEnv.length) {
    console.log('Missing env vars', missingEnv.join(','))
    return new Response(
      JSON.stringify({ error: 'Configuração do servidor incompleta' }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  }

  try {
    const { token: bodyToken, password } = await req.json().catch(() => ({}))
    const urlToken = new URL(req.url).searchParams.get('token') ?? undefined
    const token = bodyToken ?? urlToken
    console.log('Password reset confirm with token', token)

    if (!token || !password) {
      return new Response(JSON.stringify({ error: 'Token e senha são obrigatórios' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    const supabaseAdmin = createClient(
      SUPABASE_URL,
      SUPABASE_SERVICE_ROLE_KEY,
      { auth: { autoRefreshToken: false, persistSession: false } }
    )

    const { data, error } = await supabaseAdmin
      .from('password_reset_tokens')
      .select('id, user_id, expires_at, status')
      .eq('token', token)
      .single()

    if (error || !data) {
      return new Response(JSON.stringify({ error: 'Token inválido' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    if (data.status !== 'pending' || (data.expires_at && new Date(data.expires_at) < new Date())) {
      return new Response(JSON.stringify({ error: 'Token expirado' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    const { error: updateError } = await supabaseAdmin.auth.admin.updateUserById(data.user_id, { password })

    if (updateError) {
      return new Response(JSON.stringify({ error: updateError.message }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    await supabaseAdmin
      .from('password_reset_tokens')
      .update({ status: 'used' })
      .eq('id', data.id)

    return new Response(JSON.stringify({ success: true }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })
  }
})