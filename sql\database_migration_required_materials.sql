-- Migração para adicionar campo 'required_materials' à tabela trainings
-- Execute este script no seu Supabase SQL Editor se preferir fazer manualmente

-- Adicionar a coluna required_materials à tabela trainings
ALTER TABLE trainings ADD COLUMN required_materials TEXT;

-- Comentário da coluna para documentação
COMMENT ON COLUMN trainings.required_materials IS 'Materiais necessários para o treino (ex: cones, bolas, coletes, etc.)';

-- Verificar se a coluna foi adicionada corretamente
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'trainings' 
AND column_name = 'required_materials';
