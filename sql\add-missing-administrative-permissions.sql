-- Ensure all administrative-related permissions exist
INSERT INTO permissions (name, description, category) VALUES
  ('administrative.documents.create', 'Criar documentos administrativos', 'administrative'),
  ('administrative.documents.edit', 'Editar documentos administrativos', 'administrative'),
  ('administrative.documents.delete', 'Excluir documentos administrativos', 'administrative'),
  ('administrative.documents.sign', 'Assinar documentos administrativos', 'administrative'),
  ('administrative.tasks.create', 'Criar tarefas administrativas', 'administrative'),
  ('administrative.tasks.edit', 'Editar tarefas administrativas', 'administrative'),
  ('administrative.tasks.delete', 'Excluir tarefas administrativas', 'administrative'),
  ('administrative.reminders.create', 'Criar lembretes administrativos', 'administrative'),
  ('administrative.reminders.edit', 'Editar lembretes administrativos', 'administrative'),
  ('administrative.reminders.delete', 'Excluir lembretes administrativos', 'administrative');

-- Prevent duplicates
ON CONFLICT (name) DO NOTHING;
