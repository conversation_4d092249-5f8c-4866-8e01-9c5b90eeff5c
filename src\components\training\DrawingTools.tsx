import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Slider } from "@/components/ui/slider";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  <PERSON><PERSON>ointer,
  Pencil,
  Eraser,
  ArrowRight,
  Circle,
  Square,
  Triangle,
  Minus,
  Type,
  Palette,
  RotateCcw,
  Trash2,
  Copy,
  Move,
  MoreHorizontal
} from 'lucide-react';

interface DrawingToolsProps {
  mode: 'select' | 'draw' | 'erase';
  onModeChange: (mode: 'select' | 'draw' | 'erase') => void;
  selectedElements: string[];
  onUpdateElements: (id: string, updates: any) => void;
}

export function DrawingTools({ 
  mode, 
  onModeChange, 
  selectedElements, 
  onUpdateElements 
}: DrawingToolsProps) {
  const [drawingTool, setDrawingTool] = useState<string>('line');
  const [strokeColor, setStrokeColor] = useState('#ef4444');
  const [strokeWidth, setStrokeWidth] = useState([2]);
  const [fillColor, setFillColor] = useState('#ef444440');
  const [arrowStyle, setArrowStyle] = useState('simple');
  const [textContent, setTextContent] = useState('');
  const [fontSize, setFontSize] = useState([12]);

  const colors = [
    '#ef4444', // red
    '#3b82f6', // blue
    '#10b981', // green
    '#f59e0b', // yellow
    '#8b5cf6', // purple
    '#f97316', // orange
    '#06b6d4', // cyan
    '#84cc16', // lime
    '#ec4899', // pink
    '#000000', // black
    '#ffffff', // white
    '#6b7280', // gray
  ];

  const handleModeChange = (newMode: 'select' | 'draw' | 'erase') => {
    onModeChange(newMode);
  };

  const handleColorChange = (color: string, type: 'stroke' | 'fill') => {
    if (type === 'stroke') {
      setStrokeColor(color);
    } else {
      setFillColor(color + '40'); // Add transparency
    }
    
    // Apply to selected elements
    selectedElements.forEach(id => {
      onUpdateElements(id, {
        properties: {
          [type === 'stroke' ? 'strokeColor' : 'fillColor']: color
        }
      });
    });
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm flex items-center gap-2">
          <Pencil className="h-4 w-4" />
          Ferramentas de Desenho
        </CardTitle>
        <CardDescription className="text-xs">
          Desenhe marcações, setas e anotações no campo
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Modos de interação */}
        <div>
          <Label className="text-xs font-medium mb-2 block">Modo</Label>
          <div className="grid grid-cols-3 gap-1">
            <Button
              variant={mode === 'select' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleModeChange('select')}
              className="flex flex-col items-center p-2 h-auto"
            >
              <MousePointer className="h-4 w-4 mb-1" />
              <span className="text-xs">Selecionar</span>
            </Button>
            <Button
              variant={mode === 'draw' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleModeChange('draw')}
              className="flex flex-col items-center p-2 h-auto"
            >
              <Pencil className="h-4 w-4 mb-1" />
              <span className="text-xs">Desenhar</span>
            </Button>
            <Button
              variant={mode === 'erase' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleModeChange('erase')}
              className="flex flex-col items-center p-2 h-auto"
            >
              <Eraser className="h-4 w-4 mb-1" />
              <span className="text-xs">Apagar</span>
            </Button>
          </div>
        </div>

        <Separator />

        {/* Ferramentas de desenho */}
        {mode === 'draw' && (
          <div>
            <Label className="text-xs font-medium mb-2 block">Ferramenta</Label>
            <div className="grid grid-cols-2 gap-1">
              <Button
                variant={drawingTool === 'line' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setDrawingTool('line')}
                className="flex flex-col items-center p-2 h-auto"
              >
                <Minus className="h-4 w-4 mb-1" />
                <span className="text-xs">Linha</span>
              </Button>
              <Button
                variant={drawingTool === 'arrow' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setDrawingTool('arrow')}
                className="flex flex-col items-center p-2 h-auto"
              >
                <ArrowRight className="h-4 w-4 mb-1" />
                <span className="text-xs">Seta</span>
              </Button>
              <Button
                variant={drawingTool === 'circle' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setDrawingTool('circle')}
                className="flex flex-col items-center p-2 h-auto"
              >
                <Circle className="h-4 w-4 mb-1" />
                <span className="text-xs">Círculo</span>
              </Button>
              <Button
                variant={drawingTool === 'rectangle' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setDrawingTool('rectangle')}
                className="flex flex-col items-center p-2 h-auto"
              >
                <Square className="h-4 w-4 mb-1" />
                <span className="text-xs">Retângulo</span>
              </Button>
              <Button
                variant={drawingTool === 'polygon' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setDrawingTool('polygon')}
                className="flex flex-col items-center p-2 h-auto"
              >
                <Triangle className="h-4 w-4 mb-1" />
                <span className="text-xs">Polígono</span>
              </Button>
              <Button
                variant={drawingTool === 'text' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setDrawingTool('text')}
                className="flex flex-col items-center p-2 h-auto"
              >
                <Type className="h-4 w-4 mb-1" />
                <span className="text-xs">Texto</span>
              </Button>
            </div>
          </div>
        )}

        <Separator />

        {/* Cores */}
        <div>
          <Label className="text-xs font-medium mb-2 block">Cores</Label>
          <div className="space-y-2">
            <div>
              <Label className="text-xs text-muted-foreground mb-1 block">Contorno</Label>
              <div className="grid grid-cols-6 gap-1">
                {colors.map((color) => (
                  <button
                    key={`stroke-${color}`}
                    className={`w-6 h-6 rounded border-2 ${
                      strokeColor === color ? 'border-primary' : 'border-muted'
                    }`}
                    style={{ backgroundColor: color }}
                    onClick={() => handleColorChange(color, 'stroke')}
                  />
                ))}
              </div>
            </div>
            
            <div>
              <Label className="text-xs text-muted-foreground mb-1 block">Preenchimento</Label>
              <div className="grid grid-cols-6 gap-1">
                {colors.map((color) => (
                  <button
                    key={`fill-${color}`}
                    className={`w-6 h-6 rounded border-2 ${
                      fillColor.startsWith(color) ? 'border-primary' : 'border-muted'
                    }`}
                    style={{ backgroundColor: color + '40' }}
                    onClick={() => handleColorChange(color, 'fill')}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>

        <Separator />

        {/* Propriedades */}
        <div className="space-y-3">
          <div>
            <Label className="text-xs font-medium mb-2 block">Espessura da Linha</Label>
            <Slider
              value={strokeWidth}
              onValueChange={setStrokeWidth}
              max={10}
              min={1}
              step={1}
              className="w-full"
            />
            <div className="text-xs text-muted-foreground mt-1">
              {strokeWidth[0]}px
            </div>
          </div>

          {drawingTool === 'text' && (
            <>
              <div>
                <Label className="text-xs font-medium mb-2 block">Tamanho da Fonte</Label>
                <Slider
                  value={fontSize}
                  onValueChange={setFontSize}
                  max={24}
                  min={8}
                  step={1}
                  className="w-full"
                />
                <div className="text-xs text-muted-foreground mt-1">
                  {fontSize[0]}px
                </div>
              </div>
              
              <div>
                <Label className="text-xs font-medium mb-2 block">Texto</Label>
                <Input
                  value={textContent}
                  onChange={(e) => setTextContent(e.target.value)}
                  placeholder="Digite o texto..."
                  className="text-xs"
                />
              </div>
            </>
          )}

          {drawingTool === 'arrow' && (
            <div>
              <Label className="text-xs font-medium mb-2 block">Estilo da Seta</Label>
              <Select value={arrowStyle} onValueChange={setArrowStyle}>
                <SelectTrigger className="text-xs">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="simple">Simples</SelectItem>
                  <SelectItem value="double">Dupla</SelectItem>
                  <SelectItem value="curved">Curva</SelectItem>
                  <SelectItem value="dashed">Tracejada</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}
        </div>

        <Separator />

        {/* Ações para elementos selecionados */}
        {selectedElements.length > 0 && (
          <div>
            <Label className="text-xs font-medium mb-2 block">
              Elementos Selecionados ({selectedElements.length})
            </Label>
            <div className="grid grid-cols-2 gap-1">
              <Button variant="outline" size="sm" className="text-xs">
                <Copy className="h-3 w-3 mr-1" />
                Duplicar
              </Button>
              <Button variant="outline" size="sm" className="text-xs">
                <Move className="h-3 w-3 mr-1" />
                Agrupar
              </Button>
              <Button variant="outline" size="sm" className="text-xs">
                <RotateCcw className="h-3 w-3 mr-1" />
                Girar
              </Button>
              <Button variant="destructive" size="sm" className="text-xs">
                <Trash2 className="h-3 w-3 mr-1" />
                Excluir
              </Button>
            </div>
          </div>
        )}

        {/* Camadas */}
        <div>
          <Label className="text-xs font-medium mb-2 block">Camadas</Label>
          <div className="space-y-1">
            <div className="flex items-center justify-between text-xs">
              <span>Elementos</span>
              <Button variant="ghost" size="icon" className="h-4 w-4">
                <MoreHorizontal className="h-3 w-3" />
              </Button>
            </div>
            <div className="flex items-center justify-between text-xs">
              <span>Desenhos</span>
              <Button variant="ghost" size="icon" className="h-4 w-4">
                <MoreHorizontal className="h-3 w-3" />
              </Button>
            </div>
            <div className="flex items-center justify-between text-xs">
              <span>Anotações</span>
              <Button variant="ghost" size="icon" className="h-4 w-4">
                <MoreHorizontal className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>

        {/* Dicas */}
        <div className="mt-4 p-3 bg-muted/50 rounded-lg">
          <p className="text-xs text-muted-foreground">
            💡 <strong>Dica:</strong> Use Shift para desenhar linhas retas e Ctrl para formas proporcionais.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
