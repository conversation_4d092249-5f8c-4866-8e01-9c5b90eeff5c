import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useCurrentClubId } from "@/context/ClubContext";
import { Player, MedicalRecord } from "@/api/api";
import { getTreatmentEvolutions } from "@/api/api";
import { useMedicalRecordsStore } from "@/store/useMedicalRecordsStore";
import { useRehabSessionsStore } from "@/store/useRehabSessionsStore";
import { useTreatmentEvolutionsStore } from "@/store/useTreatmentEvolutionsStore";
import { useMedicalAppointmentsStore } from "@/store/useMedicalAppointmentsStore";
import { Activity, Calendar, FileText, Clock, Printer } from "lucide-react";
import { useClubInfoStore } from "@/store/useClubInfoStore";
import { generateTreatmentReport, parseRecordDescription } from "@/utils/treatmentReportGenerator"
import { getPlayerCategories } from "@/api/api";
import { format, parseISO } from "date-fns";
import { ptBR } from "date-fns/locale";

interface PlayerMedicalHistoryProps {
  player: Player;
}

export function PlayerMedicalHistory({ player }: PlayerMedicalHistoryProps) {
  const clubId = useCurrentClubId();
  const [activeTab, setActiveTab] = useState("records");
  const [showArchived, setShowArchived] = useState(false);
  const { medicalRecords, fetchMedicalRecords, loading: loadingRecords } = useMedicalRecordsStore();
  const { rehabSessions, fetchRehabSessions, loading: loadingSessions, archiveRehabSession } = useRehabSessionsStore();
  const { loading: loadingEvolutions } = useTreatmentEvolutionsStore();
  const { appointments, fetchAppointments, loading: loadingAppointments } = useMedicalAppointmentsStore();
  const { clubInfo, fetchClubInfo } = useClubInfoStore();
  const [treatmentHistory, setTreatmentHistory] = useState<any[]>([]);
  const [loadingHistory, setLoadingHistory] = useState(false);

  const handlePrint = async (record: MedicalRecord) => {
    if (!clubInfo) return;
    const categories = await getPlayerCategories(clubId, player.id);
    const patient = { ...player, category: categories[0]?.name } as Player & { category?: string };
    const sessionDescriptions = rehabSessions
      .filter(
        s =>
          String(s.player_id) === String(player.id) &&
          (!record.start_date || s.date >= record.start_date) &&
          s.date <= record.date
      )
      .flatMap(s => s.treatment_description || []);
      const parsed = parseRecordDescription(record.description || "");

    await generateTreatmentReport(
      {
        patient,
        startDate: parsed.startDate || record.start_date || '',
        endDate: parsed.endDate || record.date,
        injury: parsed.injury,
        dischargeReason: parsed.dischargeReason,
        treatmentUsed: parsed.treatmentUsed,
        recommendation: parsed.recommendation,
        doctorName: record.doctor || '',
        doctorRole: 'medical',
        treatmentDescription: sessionDescriptions,
      },
      clubInfo
    );
  };


  useEffect(() => {
    fetchMedicalRecords(clubId);
    fetchRehabSessions(clubId, { includeArchived: showArchived });
    // Carregar agendamentos médicos do jogador
    if (player?.id) {
      fetchAppointments(clubId, { playerId: player.id });
    }
    fetchClubInfo(clubId);
  }, [clubId, player?.id, fetchMedicalRecords, fetchRehabSessions, fetchAppointments, showArchived]);

  // Filter records for this player
  const playerRecords = medicalRecords.filter(record =>
    String(record.player_id) === String(player.id)
  );

  // Filter rehab sessions for this player
  const playerSessions = rehabSessions.filter(session =>
    String(session.player_id) === String(player.id)
  );

  // Find records with "Liberado" or "Alta médica" status
  const releasedRecords = playerRecords.filter(record =>
    record.status === "Liberado" || record.status === "Alta médica"
  );

  // Fetch treatment history for released records
  useEffect(() => {
    const fetchTreatmentHistoryForRecords = async () => {
      if (releasedRecords.length === 0) return;

      const history: any[] = [];
      const evolutionsMap = new Map();

      // Fetch evolutions for all records in parallel
      const fetchPromises = releasedRecords.map(async (record) => {
        try {
          const recordEvolutions = await getTreatmentEvolutions(clubId, record.id);
          if (recordEvolutions.length > 0) {
            evolutionsMap.set(record.id, recordEvolutions);
          }
        } catch (error) {
          console.error("Error fetching treatment history:", error);
        }
      });

      // Wait for all fetches to complete
      await Promise.all(fetchPromises);

      // Build history from fetched evolutions
      for (const record of releasedRecords) {
        const recordEvolutions = evolutionsMap.get(record.id);
        if (recordEvolutions && recordEvolutions.length > 0) {
          history.push({
            recordId: record.id,
            recordDate: record.date,
            recordDescription: record.description,
            evolutions: recordEvolutions
          });
        }
      }

      setTreatmentHistory(history);
    };

    fetchTreatmentHistoryForRecords();
  }, [clubId, releasedRecords]);

  // Format date for display - using parseISO to avoid timezone issues
  const formatDate = (dateString: string) => {
    try {
      const date = parseISO(dateString);
      return format(date, "dd/MM/yyyy", { locale: ptBR });
    } catch (error) {
      return dateString;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Histórico Médico</h2>
      </div>

      <Tabs defaultValue="records" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="records">Prontuários</TabsTrigger>
          <TabsTrigger value="appointments">
            <div className="flex items-center gap-1">
              <Clock className="h-4 w-4" />
              <span>Agendamentos</span>
            </div>
          </TabsTrigger>
          <TabsTrigger value="rehab">Sessões de Recuperação</TabsTrigger>
          <TabsTrigger value="treatment-history">Histórico de Tratamento</TabsTrigger>
        </TabsList>

        <TabsContent value="records">
          {loadingRecords ? (
            <div className="text-center py-8">Carregando prontuários...</div>
          ) : playerRecords.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              Nenhum prontuário médico encontrado para este jogador.
            </div>
          ) : (
            <div className="space-y-4">
              {playerRecords.map(record => (
                <Card key={record.id} className="overflow-hidden">
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">{record.description}</CardTitle>
                      {record.completed && (
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={() => handlePrint(record)}>
                          <Printer className="h-4 w-4" />
                        </Button>
                      )}
                      {record.completed ? (
                        <Badge className="bg-blue-500 text-white">Tratamento Concluído</Badge>
                      ) : (
                        <Badge variant="outline" className={
                          record.status === "em tratamento" ? "bg-amber-100 text-amber-800 border-amber-200" :
                          record.status === "recuperado" ? "bg-green-100 text-green-800 border-green-200" :
                          "bg-gray-100 text-gray-800 border-gray-200"
                        }>
                          {record.status}
                        </Badge>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-muted-foreground">Data</p>
                        <p className="font-medium">{formatDate(record.date)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Médico</p>
                        <p className="font-medium">{record.doctor || "Não informado"}</p>
                      </div>
                      {record.diagnosis && (
                        <div className="col-span-2">
                          <p className="text-sm text-muted-foreground">Diagnóstico</p>
                          <p className="font-medium">{record.diagnosis}</p>
                        </div>
                      )}
                      {record.treatment && (
                        <div className="col-span-2">
                          <p className="text-sm text-muted-foreground">Tratamento</p>
                          <p className="font-medium">{record.treatment}</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="appointments">
          {loadingAppointments ? (
            <div className="text-center py-8">Carregando agendamentos...</div>
          ) : appointments.filter(a => String(a.player_id) === String(player.id)).length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              Nenhum agendamento médico encontrado para este jogador.
            </div>
          ) : (
            <div className="space-y-4">
              {appointments
                .filter(a => String(a.player_id) === String(player.id))
                .sort((a, b) => {
                  // Ordenar por data (mais recentes primeiro)
                  const dateA = new Date(`${a.appointment_date}T${a.appointment_time}`);
                  const dateB = new Date(`${b.appointment_date}T${b.appointment_time}`);
                  return dateB.getTime() - dateA.getTime();
                })
                .map(appointment => (
                  <Card key={appointment.id} className="overflow-hidden">
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-lg">{appointment.appointment_type}</CardTitle>
                        <Badge variant="outline" className={
                          appointment.status === "Agendada" ? "bg-blue-100 text-blue-800 border-blue-200" :
                          appointment.status === "Concluída" ? "bg-green-100 text-green-800 border-green-200" :
                          appointment.status === "Cancelada" ? "bg-red-100 text-red-800 border-red-200" :
                          "bg-gray-100 text-gray-800 border-gray-200"
                        }>
                          {appointment.status}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm text-muted-foreground">Data</p>
                          <p className="font-medium">{formatDate(appointment.appointment_date)}</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Horário</p>
                          <p className="font-medium">{appointment.appointment_time.substring(0, 5)}</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Duração</p>
                          <p className="font-medium">{appointment.duration} minutos</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Profissional</p>
                          <p className="font-medium">{appointment.professional_name || "Não informado"}</p>
                        </div>
                        {appointment.location && (
                          <div className="col-span-2">
                            <p className="text-sm text-muted-foreground">Local</p>
                            <p className="font-medium">{appointment.location}</p>
                          </div>
                        )}
                        {appointment.notes && (
                          <div className="col-span-2">
                            <p className="text-sm text-muted-foreground">Observações</p>
                            <p className="font-medium">{appointment.notes}</p>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="rehab">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">Sessões de Recuperação</h3>
            <div className="flex items-center gap-2">
              <label className="text-sm text-muted-foreground">
                <input
                  type="checkbox"
                  checked={showArchived}
                  onChange={(e) => setShowArchived(e.target.checked)}
                  className="mr-2"
                />
                Mostrar arquivadas
              </label>
            </div>
          </div>

          {loadingSessions ? (
            <div className="text-center py-8">Carregando sessões de recuperação...</div>
          ) : playerSessions.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              Nenhuma sessão de recuperação encontrada para este jogador.
            </div>
          ) : (
            <div className="space-y-4">
              {playerSessions
                .filter(s => String(s.player_id) === String(player.id))
                .map(session => (
                  <Card key={session.id} className={`overflow-hidden ${session.archived ? 'border-dashed opacity-70' : ''}`}>
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <CardTitle className="text-lg">{session.activity}</CardTitle>
                          {session.archived && (
                            <Badge variant="outline" className="bg-gray-100 text-gray-800 border-gray-200">
                              Arquivada
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className={
                            session.status === "Agendada" ? "bg-blue-100 text-blue-800 border-blue-200" :
                            session.status === "Concluída" ? "bg-green-100 text-green-800 border-green-200" :
                            session.status === "Cancelada" ? "bg-red-100 text-red-800 border-red-200" :
                            "bg-gray-100 text-gray-800 border-gray-200"
                          }>
                            {session.status}
                          </Badge>
                          <button
                            onClick={() => archiveRehabSession(clubId, session.id, !session.archived)}
                            className="text-xs text-muted-foreground hover:text-foreground"
                            title={session.archived ? "Desarquivar" : "Arquivar"}
                          >
                            {session.archived ? "Desarquivar" : "Arquivar"}
                          </button>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm text-muted-foreground">Data</p>
                          <p className="font-medium">{formatDate(session.date)}</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Horário</p>
                          <p className="font-medium">{session.time.substring(0, 5)}</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Duração</p>
                          <p className="font-medium">{session.duration} minutos</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Profissional</p>
                          <p className="font-medium">{session.professional || "Não informado"}</p>
                        </div>
                        {session.location && (
                          <div className="col-span-2">
                            <p className="text-sm text-muted-foreground">Local</p>
                            <p className="font-medium">{session.location}</p>
                          </div>
                        )}
                        {session.notes && (
                          <div className="col-span-2">
                            <p className="text-sm text-muted-foreground">Observações</p>
                            <p className="font-medium">{session.notes}</p>
                          </div>
                        )}
                      </div>

                      {session.treatment_description && session.treatment_description.length > 0 && (
                        <div className="mt-3 pt-2 border-t">
                          <h5 className="text-xs font-medium mb-1">Histórico de tratamento:</h5>
                          <div className="space-y-1">
                            {session.treatment_description.map((entry, index) => (
                              <div key={index} className="text-xs">
                                {entry}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="treatment-history">
          {loadingEvolutions ? (
            <div className="text-center py-8">Carregando histórico de tratamento...</div>
          ) : treatmentHistory.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              Nenhum histórico de tratamento encontrado para este jogador.
            </div>
          ) : (
            <div className="space-y-6">
              {treatmentHistory.map((history, index) => (
                <Card key={index} className="overflow-hidden">
                  <CardHeader className="pb-2 bg-green-50">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">
                        <Badge className="bg-green-500 mr-2">Alta Médica</Badge>
                        {history.recordDescription}
                      </CardTitle>
                      <div className="text-sm text-muted-foreground">
                        {formatDate(history.recordDate)}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="p-0">
                    <div className="p-4 border-b">
                      <h3 className="font-medium mb-2">Evolução do Tratamento</h3>
                    </div>
                    {history.evolutions.map((evolution: any, idx: number) => (
                      <div key={idx} className="p-4 border-b">
                        <div className="flex justify-between items-center mb-2">
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                            <span className="font-medium">{formatDate(evolution.date)}</span>
                            <Badge variant="outline" className={
                              evolution.status === "Liberado" ? "bg-green-100 text-green-800 border-green-200" :
                              evolution.status === "Em tratamento" ? "bg-amber-100 text-amber-800 border-amber-200" :
                              evolution.status === "Treina e trata" ? "bg-blue-100 text-blue-800 border-blue-200" :
                              "bg-gray-100 text-gray-800 border-gray-200"
                            }>
                              {evolution.status}
                            </Badge>
                          </div>
                        </div>

                        <div className="space-y-2 mt-2">
                          <div>
                            <p className="text-sm text-muted-foreground">Descrição:</p>
                            <p className="text-sm">{evolution.description}</p>
                          </div>

                          {evolution.procedures && evolution.procedures.length > 0 && (
                            <div>
                              <p className="text-sm text-muted-foreground">Procedimentos:</p>
                              <ul className="list-disc list-inside text-sm">
                                {evolution.procedures.map((procedure: string, i: number) => (
                                  <li key={i}>{procedure}</li>
                                ))}
                              </ul>
                            </div>
                          )}

                          {evolution.response && (
                            <div>
                              <p className="text-sm text-muted-foreground">Resposta ao tratamento:</p>
                              <p className="text-sm">{evolution.response}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
