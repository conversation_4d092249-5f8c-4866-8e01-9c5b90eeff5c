import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { signIn } from "@/api/auth";
import { getUserClubs } from "@/api/api";
import { LogIn, Mail, Phone, User, Sparkles, Shield, Zap, Users, ArrowRight, CheckCircle } from "lucide-react";

export default function Login() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [activeTab, setActiveTab] = useState<'login' | 'contact'>('login');
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [animationType, setAnimationType] = useState<'3d' | 'flip' | 'morph' | 'zoom'>('3d');
  const [contactForm, setContactForm] = useState({
    name: "",
    email: "",
    phone: "",
    message: ""
  });
  const [contactSubmitted, setContactSubmitted] = useState(false);
  const [contactLoading, setContactLoading] = useState(false);
  const navigate = useNavigate();

  // Função para alternar abas com animações espetaculares
  const switchTab = (newTab: 'login' | 'contact') => {
    if (newTab === activeTab || isTransitioning) return;

    setIsTransitioning(true);

    // Ciclar entre diferentes tipos de animação para variedade
    const animations: Array<'3d' | 'flip' | 'morph' | 'zoom'> = ['3d', 'flip', 'morph', 'zoom'];
    const currentIndex = animations.indexOf(animationType);
    const nextAnimation = animations[(currentIndex + 1) % animations.length];
    setAnimationType(nextAnimation);

    // Delay para permitir animação de saída antes de trocar o conteúdo
    setTimeout(() => {
      setActiveTab(newTab);
      setTimeout(() => {
        setIsTransitioning(false);
      }, 100);
    }, 600);
  };

// Carregar credenciais salvas, se existirem
  useEffect(() => {
    const savedEmail = localStorage.getItem("savedEmail");
    const savedPassword = localStorage.getItem("savedPassword");
    if (savedEmail) setEmail(savedEmail);
    if (savedPassword) setPassword(savedPassword);
  }, []);

  // Função para lidar com o envio do formulário de contato
  const handleContactSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setContactLoading(true);

    // Simular envio (aqui você integraria com sua API de contato)
    setTimeout(() => {
      setContactSubmitted(true);
      setContactLoading(false);
      // Reset form after 3 seconds
      setTimeout(() => {
        setContactSubmitted(false);
        setContactForm({ name: "", email: "", phone: "", message: "" });
      }, 3000);
    }, 1500);
  };


  async function handleLogin(e: React.FormEvent) {
    e.preventDefault();
    setLoading(true);
    setError("");
    try {
      // 1. Login via Supabase Auth
      const { user, session } = await signIn(email, password);
      if (!user || !user.id) throw new Error("Usuário ou senha inválidos");

      // Salvar dados do usuário
      localStorage.setItem("userId", user.id);
      if (session && session.access_token) {
        localStorage.setItem("token", session.access_token);
      }
      // Salvar credenciais para login automático
      localStorage.setItem("savedEmail", email);
      localStorage.setItem("savedPassword", password);

      // 2. Buscar clubes do usuário
      const clubs = await getUserClubs(user.id);
      if (clubs.length > 0) {
        localStorage.setItem("clubId", String(clubs[0].id));
      } else {
        setError("Usuário não possui clube associado. Cadastre-se novamente.");
        setLoading(false);
        return;
      }

      // Redirecionar para o dashboard
      setLoading(false);
      navigate("/dashboard");
    } catch (err) {
      console.error("Erro de login:", err);
      setError("E-mail ou senha inválidos.");
      setLoading(false);
    }
  }

  return (
    <div className="min-h-screen relative overflow-hidden bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      {/* Animated background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-72 h-72 bg-blue-500/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute top-1/2 right-0 w-96 h-96 bg-purple-500/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute bottom-0 left-1/3 w-80 h-80 bg-cyan-500/20 rounded-full blur-3xl animate-pulse delay-2000"></div>
      </div>

      {/* Floating particles */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-white/20 rounded-full animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 2}s`
            }}
          />
        ))}
      </div>

      <div className="relative z-10 min-h-screen flex items-center justify-center px-4">
        <div className="w-full max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12 animate-fade-in">
            <div className="inline-flex items-center justify-center w-24 h-24 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 mb-6 shadow-2xl">
              <Sparkles className="w-12 h-12 text-white" />
            </div>
            <h1 className="text-5xl md:text-7xl font-bold text-white mb-4 bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
              Game Day Nexus
            </h1>
            <p className="text-xl md:text-2xl text-blue-200 font-light mb-8">
              Plataforma Completa de Gestão Esportiva
            </p>

            {/* Features showcase */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12 max-w-4xl mx-auto">
              <div className="flex items-center justify-center space-x-3 text-white/80">
                <Shield className="w-6 h-6 text-blue-400" />
                <span className="text-lg">Seguro & Confiável</span>
              </div>
              <div className="flex items-center justify-center space-x-3 text-white/80">
                <Zap className="w-6 h-6 text-purple-400" />
                <span className="text-lg">Rápido & Eficiente</span>
              </div>
              <div className="flex items-center justify-center space-x-3 text-white/80">
                <Users className="w-6 h-6 text-cyan-400" />
                <span className="text-lg">Gestão Completa</span>
              </div>
            </div>
          </div>

          {/* Main content area */}
          <div className="flex flex-col items-center justify-center space-y-8">
            {/* Tab navigation */}
            <div className="flex flex-col items-center space-y-4">
              <div className="flex bg-white/10 backdrop-blur-md rounded-full p-1 relative overflow-hidden">
                {/* Animated background indicator */}
                <div
                  className={`absolute top-1 bottom-1 bg-gradient-to-r from-white to-gray-100 rounded-full transition-all duration-500 ease-out shadow-lg ${
                    activeTab === 'login' ? 'left-1 w-[calc(50%-4px)]' : 'left-[calc(50%+2px)] w-[calc(50%-4px)]'
                  }`}
                />

                <button
                  onClick={() => switchTab('login')}
                  disabled={isTransitioning}
                  className={`relative z-10 px-8 py-3 rounded-full font-medium transition-all duration-300 transform hover:scale-105 btn-hover-glow ${
                    activeTab === 'login'
                      ? 'text-blue-900 shadow-lg'
                      : 'text-white hover:text-blue-200'
                  } ${isTransitioning ? 'cursor-not-allowed opacity-70' : ''}`}
                >
                  <span className="flex items-center space-x-2">
                    <LogIn className="w-4 h-4" />
                    <span>Entrar</span>
                  </span>
                </button>

                <button
                  onClick={() => switchTab('contact')}
                  disabled={isTransitioning}
                  className={`relative z-10 px-8 py-3 rounded-full font-medium transition-all duration-300 transform hover:scale-105 btn-hover-glow ${
                    activeTab === 'contact'
                      ? 'text-blue-900 shadow-lg'
                      : 'text-white hover:text-blue-200'
                  } ${isTransitioning ? 'cursor-not-allowed opacity-70' : ''}`}
                >
                  <span className="flex items-center space-x-2">
                    <Mail className="w-4 h-4" />
                    <span>Contato</span>
                  </span>
                </button>
              </div>

              {/* Animation type indicator */}
              <div className="flex items-center space-x-2 text-white/60 text-sm">
                <Sparkles className="w-4 h-4" />
                <span>
                  Animação: {
                    animationType === '3d' ? '3D Slide' :
                    animationType === 'flip' ? 'Flip 3D' :
                    animationType === 'morph' ? 'Morph' :
                    'Zoom Rotate'
                  }
                </span>
                {isTransitioning && (
                  <div className="flex items-center space-x-1">
                    <div className="w-1 h-1 bg-white/60 rounded-full animate-pulse"></div>
                    <div className="w-1 h-1 bg-white/60 rounded-full animate-pulse delay-100"></div>
                    <div className="w-1 h-1 bg-white/60 rounded-full animate-pulse delay-200"></div>
                  </div>
                )}
              </div>
            </div>

            {/* Content cards */}
            <div className="w-full max-w-md relative perspective-1000">
              {/* Login Card */}
              {activeTab === 'login' && (
                <Card className={`transform-gpu bg-white/95 backdrop-blur-xl border-0 shadow-2xl rounded-2xl overflow-hidden ${
                  !isTransitioning
                    ? animationType === '3d' ? 'animate-slide-in-right-3d' :
                      animationType === 'flip' ? 'animate-flip-in-x' :
                      animationType === 'morph' ? 'animate-morph-in' :
                      'animate-zoom-in-rotate'
                    : animationType === '3d' ? 'animate-slide-out-left-3d' :
                      animationType === 'flip' ? 'animate-flip-out-x' :
                      animationType === 'morph' ? 'animate-morph-out' :
                      'animate-zoom-out-rotate'
                }`}>
                  <CardHeader className="text-center pb-6 bg-gradient-to-r from-blue-50 to-purple-50 relative overflow-hidden">
                    {/* Animated background particles */}
                    <div className="absolute inset-0 opacity-30">
                      {[...Array(8)].map((_, i) => (
                        <div
                          key={i}
                          className="absolute w-1 h-1 bg-blue-400 rounded-full animate-pulse"
                          style={{
                            left: `${20 + (i * 10)}%`,
                            top: `${30 + (i % 3) * 20}%`,
                            animationDelay: `${i * 0.2}s`,
                            animationDuration: `${1.5 + (i % 3) * 0.5}s`
                          }}
                        />
                      ))}
                    </div>
                    <CardTitle className="text-3xl font-bold text-gray-800 mb-2 relative z-10">Bem-vindo de volta!</CardTitle>
                    <CardDescription className="text-gray-600 text-lg relative z-10">
                      Acesse sua conta para gerenciar seu clube
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="p-8">
                    <form className="space-y-6" onSubmit={handleLogin}>
                      <div className="space-y-2">
                        <label className="block text-sm font-semibold text-gray-700 mb-2">E-mail</label>
                        <div className="relative group">
                          <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 group-focus-within:text-blue-500 transition-colors" />
                          <Input
                            type="email"
                            autoComplete="username"
                            value={email}
                            onChange={e => setEmail(e.target.value)}
                            required
                            placeholder="<EMAIL>"
                            className="pl-12 pr-4 py-3 rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:ring-0 block w-full text-gray-800 placeholder-gray-400 transition-all duration-200 hover:border-gray-300"
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <label className="block text-sm font-semibold text-gray-700 mb-2">Senha</label>
                        <div className="relative group">
                          <Shield className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 group-focus-within:text-blue-500 transition-colors" />
                          <Input
                            type="password"
                            autoComplete="current-password"
                            value={password}
                            onChange={e => setPassword(e.target.value)}
                            required
                            placeholder="••••••••"
                            className="pl-12 pr-4 py-3 rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:ring-0 block w-full text-gray-800 placeholder-gray-400 transition-all duration-200 hover:border-gray-300"
                          />
                        </div>
                      </div>

                      {error && (
                        <div className="bg-red-50 text-red-600 text-sm p-4 rounded-xl border border-red-200 animate-fade-in">
                          {error}
                        </div>
                      )}

                      <Button
                        type="submit"
                        className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                        disabled={loading}
                      >
                        {loading ? (
                          <span className="flex items-center justify-center">
                            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Entrando...
                          </span>
                        ) : (
                          <span className="flex items-center justify-center">
                            <LogIn className="mr-2 h-5 w-5" />
                            Entrar na Plataforma
                            <ArrowRight className="ml-2 h-5 w-5" />
                          </span>
                        )}
                      </Button>

                      <div className="flex justify-between items-center mt-6">
                        <a href="/reset-password" className="text-sm text-blue-600 hover:text-blue-800 font-medium transition-colors">
                          Esqueceu sua senha?
                        </a>
                        <button
                          type="button"
                          onClick={() => switchTab('contact')}
                          disabled={isTransitioning}
                          className="text-sm text-purple-600 hover:text-purple-800 font-medium transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          Não tem conta? Contate-nos!
                        </button>
                      </div>
                    </form>
                  </CardContent>
                </Card>
              )}

              {/* Contact Card */}
              {activeTab === 'contact' && (
                <Card className={`transform-gpu bg-white/95 backdrop-blur-xl border-0 shadow-2xl rounded-2xl overflow-hidden ${
                  !isTransitioning
                    ? animationType === '3d' ? 'animate-slide-in-left-3d' :
                      animationType === 'flip' ? 'animate-flip-in-x' :
                      animationType === 'morph' ? 'animate-morph-in' :
                      'animate-zoom-in-rotate'
                    : animationType === '3d' ? 'animate-slide-out-right-3d' :
                      animationType === 'flip' ? 'animate-flip-out-x' :
                      animationType === 'morph' ? 'animate-morph-out' :
                      'animate-zoom-out-rotate'
                }`}>
                  <CardHeader className="text-center pb-6 bg-gradient-to-r from-purple-50 to-blue-50 relative overflow-hidden">
                    {/* Animated background particles */}
                    <div className="absolute inset-0 opacity-30">
                      {[...Array(8)].map((_, i) => (
                        <div
                          key={i}
                          className="absolute w-1 h-1 bg-purple-400 rounded-full animate-pulse"
                          style={{
                            left: `${20 + (i * 10)}%`,
                            top: `${30 + (i % 3) * 20}%`,
                            animationDelay: `${i * 0.2}s`,
                            animationDuration: `${1.5 + (i % 3) * 0.5}s`
                          }}
                        />
                      ))}
                    </div>
                    <CardTitle className="text-3xl font-bold text-gray-800 mb-2 relative z-10">Entre em Contato</CardTitle>
                    <CardDescription className="text-gray-600 text-lg relative z-10">
                      Interessado em nossos módulos? Fale conosco!
                    </CardDescription>
                  </CardHeader>

                  <CardContent className="p-8">
                    {contactSubmitted ? (
                      <div className="text-center py-8 animate-fade-in">
                        <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
                        <h3 className="text-2xl font-bold text-gray-800 mb-2">Mensagem Enviada!</h3>
                        <p className="text-gray-600">
                          Obrigado pelo seu interesse. Nossa equipe entrará em contato em breve.
                        </p>
                      </div>
                    ) : (
                      <form className="space-y-6" onSubmit={handleContactSubmit}>
                        <div className="space-y-2">
                          <label className="block text-sm font-semibold text-gray-700 mb-2">Nome Completo</label>
                          <div className="relative group">
                            <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 group-focus-within:text-purple-500 transition-colors" />
                            <Input
                              type="text"
                              value={contactForm.name}
                              onChange={e => setContactForm({...contactForm, name: e.target.value})}
                              required
                              placeholder="Seu nome completo"
                              className="pl-12 pr-4 py-3 rounded-xl border-2 border-gray-200 focus:border-purple-500 focus:ring-0 block w-full text-gray-800 placeholder-gray-400 transition-all duration-200 hover:border-gray-300"
                            />
                          </div>
                        </div>

                        <div className="space-y-2">
                          <label className="block text-sm font-semibold text-gray-700 mb-2">E-mail</label>
                          <div className="relative group">
                            <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 group-focus-within:text-purple-500 transition-colors" />
                            <Input
                              type="email"
                              value={contactForm.email}
                              onChange={e => setContactForm({...contactForm, email: e.target.value})}
                              required
                              placeholder="<EMAIL>"
                              className="pl-12 pr-4 py-3 rounded-xl border-2 border-gray-200 focus:border-purple-500 focus:ring-0 block w-full text-gray-800 placeholder-gray-400 transition-all duration-200 hover:border-gray-300"
                            />
                          </div>
                        </div>

                        <div className="space-y-2">
                          <label className="block text-sm font-semibold text-gray-700 mb-2">Telefone</label>
                          <div className="relative group">
                            <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 group-focus-within:text-purple-500 transition-colors" />
                            <Input
                              type="tel"
                              value={contactForm.phone}
                              onChange={e => setContactForm({...contactForm, phone: e.target.value})}
                              required
                              placeholder="(11) 99999-9999"
                              className="pl-12 pr-4 py-3 rounded-xl border-2 border-gray-200 focus:border-purple-500 focus:ring-0 block w-full text-gray-800 placeholder-gray-400 transition-all duration-200 hover:border-gray-300"
                            />
                          </div>
                        </div>

                        <Button
                          type="submit"
                          className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                          disabled={contactLoading}
                        >
                          {contactLoading ? (
                            <span className="flex items-center justify-center">
                              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              Enviando...
                            </span>
                          ) : (
                            <span className="flex items-center justify-center">
                              <Mail className="mr-2 h-5 w-5" />
                              Enviar Mensagem
                              <ArrowRight className="ml-2 h-5 w-5" />
                            </span>
                          )}
                        </Button>

                        <div className="text-center mt-6">
                          <button
                            type="button"
                            onClick={() => switchTab('login')}
                            disabled={isTransitioning}
                            className="text-sm text-blue-600 hover:text-blue-800 font-medium transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            Já tem uma conta? Faça login
                          </button>
                        </div>
                      </form>
                    )}
                  </CardContent>
                </Card>
              )}
            </div>
          </div>

          {/* Footer */}
          <div className="text-center mt-16 text-white/60">
            <p className="text-lg mb-4">
              Módulos disponíveis: Gestão Esportiva • Sistema Médico • Administrativo • Financeiro
            </p>
            <p className="text-sm">
              © 2024 Game Day Nexus. Transformando a gestão esportiva.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
