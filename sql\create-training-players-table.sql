-- Criar tabela training_players se ela não existir
-- Este arquivo deve ser executado manualmente no Supabase se a tabela não existir

-- 1. Criar tabela training_players
CREATE TABLE IF NOT EXISTS training_players (
  id SERIAL PRIMARY KEY,
  club_id INTEGER REFERENCES club_info(id) NOT NULL,
  training_id INTEGER REFERENCES trainings(id) NOT NULL,
  player_id UUID REFERENCES players(id) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Garan<PERSON>r que cada jogador só pode estar associado uma vez a cada treino
  UNIQUE(club_id, training_id, player_id)
);

-- 2. Criar índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_training_players_club_id ON training_players(club_id);
CREATE INDEX IF NOT EXISTS idx_training_players_training_id ON training_players(training_id);
CREATE INDEX IF NOT EXISTS idx_training_players_player_id ON training_players(player_id);

-- 3. Habilitar RLS (Row Level Security)
ALTER TABLE training_players ENABLE ROW LEVEL SECURITY;

-- 4. Criar políticas RLS mais flexíveis
-- Primeiro, remover políticas existentes se houver
DROP POLICY IF EXISTS "Club members can view their own training players" ON training_players;
DROP POLICY IF EXISTS "Club members can insert their own training players" ON training_players;
DROP POLICY IF EXISTS "Club members can update their own training players" ON training_players;
DROP POLICY IF EXISTS "Club members can delete their own training players" ON training_players;

-- Criar função para obter club_id do usuário atual (se não existir)
CREATE OR REPLACE FUNCTION get_current_club_id()
RETURNS INTEGER AS $$
BEGIN
  -- Tentar obter club_id do JWT primeiro
  IF auth.jwt() ->> 'club_id' IS NOT NULL THEN
    RETURN (auth.jwt() ->> 'club_id')::INTEGER;
  END IF;

  -- Tentar obter do header customizado (para chamadas API)
  IF current_setting('request.headers', true)::json ->> 'x-club-id' IS NOT NULL THEN
    RETURN (current_setting('request.headers', true)::json ->> 'x-club-id')::INTEGER;
  END IF;

  -- Fallback: buscar na tabela club_members
  RETURN (
    SELECT club_id
    FROM club_members
    WHERE user_id = auth.uid()
    AND status = 'ativo'
    LIMIT 1
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Políticas RLS mais robustas
CREATE POLICY "Club members can view their own training players"
  ON training_players
  FOR SELECT
  USING (
    club_id = get_current_club_id() OR
    club_id = (auth.jwt() ->> 'club_id')::INTEGER
  );

CREATE POLICY "Club members can insert their own training players"
  ON training_players
  FOR INSERT
  WITH CHECK (
    club_id = get_current_club_id() OR
    club_id = (auth.jwt() ->> 'club_id')::INTEGER
  );

CREATE POLICY "Club members can update their own training players"
  ON training_players
  FOR UPDATE
  USING (
    club_id = get_current_club_id() OR
    club_id = (auth.jwt() ->> 'club_id')::INTEGER
  );

CREATE POLICY "Club members can delete their own training players"
  ON training_players
  FOR DELETE
  USING (
    club_id = get_current_club_id() OR
    club_id = (auth.jwt() ->> 'club_id')::INTEGER
  );

-- 5. Comentários para documentação
COMMENT ON TABLE training_players IS 'Associação entre treinamentos e jogadores';
COMMENT ON COLUMN training_players.club_id IS 'ID do clube';
COMMENT ON COLUMN training_players.training_id IS 'ID do treinamento';
COMMENT ON COLUMN training_players.player_id IS 'ID do jogador';
COMMENT ON COLUMN training_players.created_at IS 'Data de criação da associação';
