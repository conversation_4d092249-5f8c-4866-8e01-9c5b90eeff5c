import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { ClubInfo } from '@/api/api';
import autoTable from 'jspdf-autotable';
import { generateClothingReport } from "./clothingReportGeneratorNew";
import { getClubPrimaryColorRgb } from '@/utils/themeUtils';
import { FieldMapping, MappingPosition } from "@/api/categoryMappings";
import { TECHNICAL_ROLES, STAFF_ROLES, EXECUTIVE_ROLES } from '@/utils/callupRoles';
import { formatCPF } from '@/utils/formatters';
import { formatDateUTC } from '@/lib/utils';

// Ordem das posições para os relatórios
const POSITION_ORDER = [
  "Goleiro",
  "Zagueiro",
  "Lateral",
  "Volante",
  "Meio-campista",
  "Meias", // Incluir variação encontrada no sistema
  "Extremo",
  "Atacante",
  "Centroavante", // Incluir variação encontrada no sistema
  "Outro"
];

/**
 * Gera um PDF a partir de um elemento HTML com suporte a múltiplas páginas
 * @param element Elemento HTML a ser convertido em PDF
 * @param filename Nome do arquivo PDF
 * @param options Opções adicionais
 * @param headerElement Elemento HTML opcional para ser usado como cabeçalho em todas as páginas
 */
export async function generatePDF(
  element: HTMLElement,
  filename: string,
  options: {
    format?: 'a4' | 'letter' | 'legal';
    orientation?: 'portrait' | 'landscape';
    margin?: number;
    headerHeight?: number; // Altura do cabeçalho em mm
  } = {},
  headerElement?: HTMLElement
): Promise<void> {
  try {
    const { format = 'a4', orientation = 'portrait', margin = 10, headerHeight = 0 } = options;

    // Criar o PDF
    const pdf = new jsPDF({
      format: format,
      orientation: orientation,
      unit: 'mm',
    });

    // Dimensões do PDF
    const pdfWidth = pdf.internal.pageSize.getWidth();
    const pdfHeight = pdf.internal.pageSize.getHeight();

    // Capturar o cabeçalho como uma imagem separada, se fornecido
    let headerCanvas;
    let headerHeightPx = 0;

    if (headerElement) {
      headerCanvas = await html2canvas(headerElement, {
        scale: 3, // Aumentar qualidade
        useCORS: true,
        logging: false,
        backgroundColor: 'white',
      });

      // Calcular a altura do cabeçalho no PDF
      const headerWidthRatio = (pdfWidth - margin * 2) / headerCanvas.width;
      headerHeightPx = headerCanvas.height * headerWidthRatio;
    }

    // Capturar o elemento principal como uma imagem
    // Remover o cabeçalho do elemento principal para evitar duplicação
    const headerInElement = element.querySelector('.report-header');
    let headerDisplay = '';
    if (headerInElement && headerElement) {
      headerDisplay = headerInElement.style.display;
      headerInElement.style.display = 'none';
    }

    const canvas = await html2canvas(element, {
      scale: 3, // Aumentar qualidade
      useCORS: true,
      logging: false,
      backgroundColor: 'white',
    });

    // Restaurar a exibição do cabeçalho no elemento principal
    if (headerInElement && headerElement) {
      headerInElement.style.display = headerDisplay;
    }

    // Dimensões da imagem capturada
    const imgWidth = canvas.width;
    const imgHeight = canvas.height;

    // Calcular a escala para ajustar a imagem à largura do PDF (menos margens)
    const widthRatio = (pdfWidth - margin * 2) / imgWidth;

    // Calcular as dimensões finais
    const finalWidth = imgWidth * widthRatio;
    const finalHeight = imgHeight * widthRatio;

    // Calcular a posição horizontal para centralizar
    const x = margin;

    // Se o conteúdo for maior que a altura da página, dividir em múltiplas páginas
    if (finalHeight > (pdfHeight - margin * 2 - headerHeightPx)) {
      // Altura disponível para conteúdo em cada página (menos margens e cabeçalho)
      const availableHeight = pdfHeight - margin * 2 - headerHeightPx;

      // Calcular quantas páginas serão necessárias
      const totalPages = Math.ceil(finalHeight / availableHeight);

      // Para cada página
      for (let i = 0; i < totalPages; i++) {
        // Adicionar nova página, exceto para a primeira
        if (i > 0) {
          pdf.addPage();
        }

        // Adicionar o cabeçalho, se fornecido
        if (headerCanvas) {
          const headerImgData = headerCanvas.toDataURL('image/png');
          pdf.addImage(
            headerImgData,
            'PNG',
            x,
            margin,
            finalWidth,
            headerHeightPx
          );
        }

        // Calcular a posição vertical de início para esta página
        const yStart = i * availableHeight / widthRatio;
        const yHeight = Math.min(availableHeight / widthRatio, imgHeight - yStart);

        // Adicionar a parte correspondente da imagem
        const imgData = canvas.toDataURL('image/png');
        pdf.addImage(
          imgData,
          'PNG',
          x,
          margin + headerHeightPx,
          finalWidth,
          yHeight * widthRatio,
          '',
          'FAST',
          0,
          yStart,
          imgWidth,
          yHeight
        );

        // Adicionar número da página
        pdf.setFontSize(8);
        pdf.setTextColor(100, 100, 100);
        pdf.text(
          `Página ${i + 1} de ${totalPages}`,
          pdfWidth / 2,
          pdfHeight - margin / 2,
          { align: 'center' }
        );
      }
    } else {
      // Se couber em uma única página, adicionar a imagem inteira
      // Adicionar o cabeçalho, se fornecido
      if (headerCanvas) {
        const headerImgData = headerCanvas.toDataURL('image/png');
        pdf.addImage(
          headerImgData,
          'PNG',
          x,
          margin,
          finalWidth,
          headerHeightPx
        );
      }

      // Adicionar o conteúdo principal
      const imgData = canvas.toDataURL('image/png');
      pdf.addImage(imgData, 'PNG', x, margin + headerHeightPx, finalWidth, finalHeight);
    }

    // Salvar o PDF
    pdf.save(filename);
  } catch (error) {
    console.error('Erro ao gerar PDF:', error);
    throw new Error('Não foi possível gerar o PDF');
  }
}

/**
 * Interface para jsPDF com suporte a autoTable
 */
interface jsPDFWithAutoTable extends jsPDF {
  autoTable: typeof autoTable;
  lastAutoTable: {
    finalY: number;
  };
  setFont(fontName?: string, fontStyle?: string): jsPDF;
  setLineDashPattern(pattern: number[], patternOffset: number): jsPDF;
}

/**
 * Gera um relatório de jogadores em PDF
 * @param players Lista de jogadores
 * @param clubInfo Informações do clube
 * @param filename Nome do arquivo PDF
 */
export async function generatePlayerReport(
  players: any[],
  clubInfo: ClubInfo,
  filename: string = 'relatorio-jogadores.pdf'
): Promise<void> {
  // Criar um novo documento PDF
  const doc = new jsPDF() as unknown as jsPDFWithAutoTable;

  // Configurações de página
  const pageWidth = doc.internal.pageSize.getWidth();
  const pageHeight = doc.internal.pageSize.getHeight();
  const margin = 10;

  // Adicionar título
  const title = 'Relatório de Jogadores';
  doc.setFontSize(18);
  doc.text(title, pageWidth / 2, 20, { align: 'center' });

  // Adicionar informações do clube
  doc.setFontSize(12);
  doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, margin, 30);

  if (clubInfo.address) {
    doc.text(`Endereço: ${clubInfo.address}`, margin, 35);
  }

  if (clubInfo.phone) {
    doc.text(`Telefone: ${clubInfo.phone}`, margin, 40);
  }

  if (clubInfo.zip_code) {
    doc.text(`CEP: ${clubInfo.zip_code}`, margin, 45);
  }

  // Tentar adicionar o logo do clube, se disponível
  if (clubInfo.logo_url) {
    try {
      const img = new Image();

      // Criar uma promessa para lidar com o carregamento da imagem
      const loadImage = new Promise<void>((resolve, reject) => {
        img.onload = () => {
          try {
            // Calcular proporção para manter as proporções
            const imgWidth = 30;
            const imgHeight = (img.height * imgWidth) / img.width;

            // Adicionar a imagem ao PDF (alinhada à direita)
            const imgX = pageWidth - margin - 41;  // Ajuste a posição X
            const imgY = -10 + imgWidth;  // Ajuste a posição Y
            doc.addImage(img, 'PNG', imgX, imgY, imgWidth, imgHeight, undefined, 'FAST', 0);
            resolve();
          } catch (error) {
            reject(error);
          }
        };
        img.onerror = reject;
      });

      // Iniciar o carregamento da imagem
      img.src = clubInfo.logo_url;

      // Aguardar o carregamento e processamento da imagem
      await loadImage;
    } catch (logoError) {
      console.error("Erro ao adicionar logo ao PDF:", logoError);
    }
  }

  // Adicionar data do relatório (após o logo para garantir que fique acima)
  const currentDate = new Date().toLocaleDateString('pt-BR');
  doc.setFontSize(10);
  doc.text(`Data do relatório: ${currentDate}`, margin, 50);

  // Posição Y inicial para o conteúdo
  let yPosition = 55;

  // Ordenar jogadores primeiro por posição, depois por nome
  const sortedPlayers = [...players].sort((a, b) => {
    const posA = POSITION_ORDER.indexOf(a.position);
    const posB = POSITION_ORDER.indexOf(b.position);

    // Se ambas as posições estão na lista, ordenar conforme a lista
    if (posA !== -1 && posB !== -1) {
      if (posA !== posB) {
        return posA - posB;
      }
    }

    // Se apenas uma posição está na lista, ela vem primeiro
    if (posA !== -1 && posB === -1) return -1;
    if (posA === -1 && posB !== -1) return 1;

    // Se nenhuma posição está na lista ou são da mesma posição, ordenar alfabeticamente por nome
    return a.name.localeCompare(b.name);
  });

  // Contar jogadores por posição
  const playersByPosition: Record<string, number> = {};
  sortedPlayers.forEach(player => {
    const position = player.position || 'Sem posição';
    playersByPosition[position] = (playersByPosition[position] || 0) + 1;
  });

  // Preparar dados para a tabela
  const tableData = sortedPlayers.map(player => [
    player.name || '-',
    player.position || '-',
    player.birthdate ? new Date(player.birthdate).toLocaleDateString('pt-BR') : '-',
    player.weight ? `${player.weight} kg` : '-',
    player.height ? `${player.height} cm` : '-',
    player.cpf_number || '-',
    player.registration_number || '-'
  ]);

  // Adicionar a tabela ao PDF
  autoTable(doc, {
    startY: yPosition,
    head: [['Nome', 'Posição', 'Data Nasc.', 'Peso', 'Altura', 'CPF', 'Cadastro']],
    body: tableData,
    theme: 'striped',
    headStyles: { fillColor: getClubPrimaryColorRgb(), textColor: [255, 255, 255] },
    margin: { left: margin, right: margin },
    columnStyles: {
      0: { cellWidth: 'auto' }, // Nome
      1: { cellWidth: 'auto' }, // Posição
      2: { cellWidth: 'auto' }, // Data Nasc.
      3: { cellWidth: 'auto' }, // Peso
      4: { cellWidth: 'auto' }, // Altura
      5: { cellWidth: 'auto' }, // CPF
      6: { cellWidth: 'auto' }  // Cadastro
    },
  });

  // Obter posição Y após a tabela
  const docWithTable = doc as jsPDFWithAutoTable;
  let currentY = docWithTable.lastAutoTable.finalY + 20;

  // Adicionar resumo de totais
  doc.setFontSize(14);
  doc.setTextColor(0, 0, 0);
  doc.text('Resumo por Posição', margin, currentY);
  currentY += 10;

  // Preparar dados do resumo ordenados pela ordem das posições
  const summaryData: string[][] = [];

  // Adicionar posições na ordem definida
  POSITION_ORDER.forEach(position => {
    if (playersByPosition[position]) {
      summaryData.push([position, playersByPosition[position].toString()]);
    }
  });

  // Adicionar outras posições que não estão na lista predefinida
  Object.keys(playersByPosition).forEach(position => {
    if (!POSITION_ORDER.includes(position)) {
      summaryData.push([position, playersByPosition[position].toString()]);
    }
  });

  // Adicionar linha de total
  summaryData.push(['TOTAL GERAL', sortedPlayers.length.toString()]);

  // Adicionar tabela de resumo
  autoTable(doc, {
    startY: currentY,
    head: [['Posição', 'Quantidade']],
    body: summaryData,
    theme: 'grid',
    headStyles: {
      fillColor: getClubPrimaryColorRgb(),
      textColor: [255, 255, 255],
      fontStyle: 'bold'
    },
    bodyStyles: {
      fontSize: 10
    },
    columnStyles: {
      0: { cellWidth: 80, fontStyle: 'normal' },
      1: { cellWidth: 30, halign: 'center' }
    },
    margin: { left: margin, right: margin },
    // Destacar a linha de total
    didParseCell: (data) => {
      if (data.row.index === summaryData.length - 1) {
        data.cell.styles.fontStyle = 'bold';
        data.cell.styles.fillColor = [240, 240, 240];
      }
    }
  });

  // Adicionar rodapé
  const pageCount = doc.internal.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(8);
    doc.setTextColor(100, 100, 100);
    doc.text(
      `© ${new Date().getFullYear()} ${clubInfo.name} - Todos os direitos reservados`,
      pageWidth / 2,
      pageHeight - 10,
      { align: 'center' }
    );
    doc.text(
      `Página ${i} de ${pageCount}`,
      pageWidth - margin,
      pageHeight - 10,
      { align: 'right' }
    );
  }

  // Salvar o PDF
  doc.save(filename);
}

/**
 * Gera um relatório de jogadores por categoria em PDF
 * @param playersByCategory Jogadores agrupados por categoria
 * @param clubInfo Informações do clube
 * @param filename Nome do arquivo PDF
 */
export async function generatePlayersByCategoryReport(
  playersByCategory: Record<string, any[]>,
  clubInfo: ClubInfo,
  filename: string = 'relatorio-jogadores-por-categoria.pdf'
): Promise<void> {
  // Criar um novo documento PDF
  const doc = new jsPDF() as unknown as jsPDFWithAutoTable;

  // Configurações de página
  const pageWidth = doc.internal.pageSize.getWidth();
  const pageHeight = doc.internal.pageSize.getHeight();
  const margin = 10;

  // Adicionar título
  const title = 'Relatório de Jogadores por Categoria';
  doc.setFontSize(18);
  doc.text(title, pageWidth / 2, 20, { align: 'center' });

  // Adicionar informações do clube
  doc.setFontSize(12);
  doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, margin, 30);

  if (clubInfo.address) {
    doc.text(`Endereço: ${clubInfo.address}`, margin, 35);
  }

  if (clubInfo.phone) {
    doc.text(`Telefone: ${clubInfo.phone}`, margin, 40);
  }

  if (clubInfo.zip_code) {
    doc.text(`CEP: ${clubInfo.zip_code}`, margin, 45);
  }

  // Tentar adicionar o logo do clube, se disponível
  if (clubInfo.logo_url) {
    try {
      const img = new Image();

      // Criar uma promessa para lidar com o carregamento da imagem
      const loadImage = new Promise<void>((resolve, reject) => {
        img.onload = () => {
          try {
            // Calcular proporção para manter as proporções
            const imgWidth = 30;
            const imgHeight = (img.height * imgWidth) / img.width;

            // Adicionar a imagem ao PDF (alinhada à direita)
            const imgX = pageWidth - margin - 41;  // Ajuste a posição X
            const imgY = -10 + imgWidth;  // Ajuste a posição Y
            doc.addImage(img, 'PNG', imgX, imgY, imgWidth, imgHeight, undefined, 'FAST', 0);
            resolve();
          } catch (error) {
            reject(error);
          }
        };
        img.onerror = reject;
      });

      // Iniciar o carregamento da imagem
      img.src = clubInfo.logo_url;

      // Aguardar o carregamento e processamento da imagem
      await loadImage;
    } catch (logoError) {
      console.error("Erro ao adicionar logo ao PDF:", logoError);
    }
  }

  // Adicionar data do relatório (após o logo para garantir que fique acima)
  const currentDate = new Date().toLocaleDateString('pt-BR');
  doc.setFontSize(10);
  doc.text(`Data do relatório: ${currentDate}`, margin, 50);

  // Posição Y inicial para o conteúdo
  let yPosition = 55;

  // Para cada categoria, mostrar os jogadores
  const categories = Object.keys(playersByCategory).sort();

  if (categories.length === 0) {
    doc.setFontSize(12);
    doc.setTextColor(100, 100, 100);
    doc.text('Nenhuma categoria encontrada.', margin, yPosition);
  } else {
    categories.forEach((category, index) => {
      const players = playersByCategory[category];

      if (players.length === 0) return;

      // Título da categoria
      doc.setFontSize(14);
      doc.setTextColor(0, 0, 0);
      doc.setFillColor(242, 242, 242);
      doc.rect(margin, yPosition, pageWidth - 2 * margin, 8, 'F');
      doc.text(`Categoria: ${category}`, margin + 2, yPosition + 5);
      yPosition += 12;

      // Ordenar jogadores primeiro por posição, depois por nome
      const sortedPlayers = [...players].sort((a, b) => {
        const posA = POSITION_ORDER.indexOf(a.position);
        const posB = POSITION_ORDER.indexOf(b.position);

        // Se ambas as posições estão na lista, ordenar conforme a lista
        if (posA !== -1 && posB !== -1) {
          if (posA !== posB) {
            return posA - posB;
          }
        }

        // Se apenas uma posição está na lista, ela vem primeiro
        if (posA !== -1 && posB === -1) return -1;
        if (posA === -1 && posB !== -1) return 1;

        // Se nenhuma posição está na lista ou são da mesma posição, ordenar alfabeticamente por nome
        return a.name.localeCompare(b.name);
      });

      // Contar jogadores por posição nesta categoria
      const categoryPlayersByPosition: Record<string, number> = {};
      sortedPlayers.forEach(player => {
        const position = player.position || 'Sem posição';
        categoryPlayersByPosition[position] = (categoryPlayersByPosition[position] || 0) + 1;
      });

      // Preparar dados para a tabela
      const tableData = sortedPlayers.map(player => [
        player.name || '-',
        player.position || '-',
        player.birthdate ? new Date(player.birthdate).toLocaleDateString('pt-BR') : '-',
        player.weight ? `${player.weight} kg` : '-',
        player.height ? `${player.height} cm` : '-',
        player.cpf_number || '-',
        player.registration_number || '-'
      ]);

      // Adicionar a tabela ao PDF
      autoTable(doc, {
        startY: yPosition,
        head: [['Nome', 'Posição', 'Data Nasc.', 'Peso', 'Altura', 'CPF', 'Cadastro']],
        body: tableData,
        theme: 'striped',
        headStyles: { fillColor: getClubPrimaryColorRgb(), textColor: [255, 255, 255] },
        margin: { left: margin, right: margin },
        columnStyles: {
          0: { cellWidth: 'auto' }, // Nome
          1: { cellWidth: 'auto' }, // Posição
          2: { cellWidth: 'auto' }, // Data Nasc.
          3: { cellWidth: 'auto' }, // Peso
          4: { cellWidth: 'auto' }, // Altura
          5: { cellWidth: 'auto' }, // CPF
          6: { cellWidth: 'auto' }  // Cadastro
        },
      });

      // Atualizar a posição Y para o próximo conteúdo
      const docWithTable = doc as jsPDFWithAutoTable;
      yPosition = docWithTable.lastAutoTable.finalY + 15;

      // Adicionar resumo de totais para esta categoria
      doc.setFontSize(12);
      doc.setTextColor(0, 0, 0);
      doc.text(`Resumo - ${category}`, margin, yPosition);
      yPosition += 8;

      // Preparar dados do resumo ordenados pela ordem das posições
      const categorySummaryData: string[][] = [];

      // Adicionar posições na ordem definida
      POSITION_ORDER.forEach(position => {
        if (categoryPlayersByPosition[position]) {
          categorySummaryData.push([position, categoryPlayersByPosition[position].toString()]);
        }
      });

      // Adicionar outras posições que não estão na lista predefinida
      Object.keys(categoryPlayersByPosition).forEach(position => {
        if (!POSITION_ORDER.includes(position)) {
          categorySummaryData.push([position, categoryPlayersByPosition[position].toString()]);
        }
      });

      // Adicionar linha de total da categoria
      categorySummaryData.push([`TOTAL - ${category}`, sortedPlayers.length.toString()]);

      // Adicionar tabela de resumo da categoria
      autoTable(doc, {
        startY: yPosition,
        head: [['Posição', 'Quantidade']],
        body: categorySummaryData,
        theme: 'grid',
        headStyles: {
          fillColor: getClubPrimaryColorRgb(),
          textColor: [255, 255, 255],
          fontStyle: 'bold',
          fontSize: 10
        },
        bodyStyles: {
          fontSize: 9
        },
        columnStyles: {
          0: { cellWidth: 60, fontStyle: 'normal' },
          1: { cellWidth: 25, halign: 'center' }
        },
        margin: { left: margin, right: margin },
        // Destacar a linha de total
        didParseCell: (data) => {
          if (data.row.index === categorySummaryData.length - 1) {
            data.cell.styles.fontStyle = 'bold';
            data.cell.styles.fillColor = [240, 240, 240];
          }
        }
      });

      // Atualizar posição Y após o resumo
      yPosition = (doc as jsPDFWithAutoTable).lastAutoTable.finalY + 15;

      // Adicionar linha divisória entre categorias (exceto a última)
      if (index < categories.length - 1) {
        doc.setDrawColor(200, 200, 200);
        doc.setLineDashPattern([3, 3], 0);
        doc.line(margin, yPosition, pageWidth - margin, yPosition);
        doc.setLineDashPattern([], 0); // Resetar para linha sólida
        yPosition += 10;
      }
    });

    // Adicionar resumo geral de todas as categorias
    if (categories.length > 1) {
      yPosition += 10;

      // Calcular totais gerais
      const allPlayersByPosition: Record<string, number> = {};
      let totalAllPlayers = 0;

      categories.forEach(category => {
        const players = playersByCategory[category];
        totalAllPlayers += players.length;

        players.forEach(player => {
          const position = player.position || 'Sem posição';
          allPlayersByPosition[position] = (allPlayersByPosition[position] || 0) + 1;
        });
      });

      doc.setFontSize(14);
      doc.setTextColor(0, 0, 0);
      doc.text('Resumo Geral - Todas as Categorias', margin, yPosition);
      yPosition += 10;

      // Preparar dados do resumo geral
      const generalSummaryData: string[][] = [];

      // Adicionar posições na ordem definida
      POSITION_ORDER.forEach(position => {
        if (allPlayersByPosition[position]) {
          generalSummaryData.push([position, allPlayersByPosition[position].toString()]);
        }
      });

      // Adicionar outras posições que não estão na lista predefinida
      Object.keys(allPlayersByPosition).forEach(position => {
        if (!POSITION_ORDER.includes(position)) {
          generalSummaryData.push([position, allPlayersByPosition[position].toString()]);
        }
      });

      // Adicionar linha de total geral
      generalSummaryData.push(['TOTAL GERAL', totalAllPlayers.toString()]);

      // Adicionar tabela de resumo geral
      autoTable(doc, {
        startY: yPosition,
        head: [['Posição', 'Quantidade']],
        body: generalSummaryData,
        theme: 'grid',
        headStyles: {
          fillColor: getClubPrimaryColorRgb(),
          textColor: [255, 255, 255],
          fontStyle: 'bold'
        },
        bodyStyles: {
          fontSize: 10
        },
        columnStyles: {
          0: { cellWidth: 80, fontStyle: 'normal' },
          1: { cellWidth: 30, halign: 'center' }
        },
        margin: { left: margin, right: margin },
        // Destacar a linha de total
        didParseCell: (data) => {
          if (data.row.index === generalSummaryData.length - 1) {
            data.cell.styles.fontStyle = 'bold';
            data.cell.styles.fillColor = [240, 240, 240];
          }
        }
      });
    }
  }

  // Adicionar rodapé
  const pageCount = doc.internal.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(8);
    doc.setTextColor(100, 100, 100);
    doc.text(
      `© ${new Date().getFullYear()} ${clubInfo.name} - Todos os direitos reservados`,
      pageWidth / 2,
      pageHeight - 10,
      { align: 'center' }
    );
    doc.text(
      `Página ${i} de ${pageCount}`,
      pageWidth - margin,
      pageHeight - 10,
      { align: 'right' }
    );
  }

  // Salvar o PDF
  doc.save(filename);
}

/**
 * Gera um relatório de Cardápio Semanal em PDF
 * @param weeklyMenu Dados do cardápio semanal
 * @param clubInfo Informações do clube
 * @param filename Nome do arquivo PDF
 */
export async function generateWeeklyMenuReport(
  weeklyMenu: {
    week: string;
    meals: {
      day: string;
      breakfast: string;
      breakfastStart?: string;
      breakfastEnd?: string;
      lunch: string;
      lunchStart?: string;
      lunchEnd?: string;
      snack: string;
      snackStart?: string;
      snackEnd?: string;
      dinner: string;
      dinnerStart?: string;
      dinnerEnd?: string;
    }[];
  },
  clubInfo: ClubInfo,
  filename: string = 'cardapio-semanal.pdf'
): Promise<void> {
  // Criar um novo documento PDF
  const doc = new jsPDF() as unknown as jsPDFWithAutoTable;

  // Configurações de página
  const pageWidth = doc.internal.pageSize.getWidth();
  const pageHeight = doc.internal.pageSize.getHeight();
  const margin = 10;

  // Adicionar título
  const title = 'Cardápio Semanal';
  doc.setFontSize(18);
  doc.text(title, pageWidth / 2, 20, { align: 'center' });

  // Adicionar informações do clube
  doc.setFontSize(12);
  doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, margin, 30);

  // Adicionar semana
  doc.setFontSize(12);
  doc.text(`Semana: ${weeklyMenu.week || 'Atual'}`, margin, 35);

  // Tentar adicionar o logo do clube, se disponível
  if (clubInfo.logo_url) {
    try {
      const img = new Image();

      // Criar uma promessa para lidar com o carregamento da imagem
      const loadImage = new Promise<void>((resolve, reject) => {
        img.onload = () => {
          try {
            // Calcular proporção para manter as proporções
            const imgWidth = 30;
            const imgHeight = (img.height * imgWidth) / img.width;

            // Adicionar a imagem ao PDF (alinhada à direita)
            const imgX = pageWidth - margin - 41;  // Ajuste a posição X
            const imgY = -10 + imgWidth;  // Ajuste a posição Y
            doc.addImage(img, 'PNG', imgX, imgY, imgWidth, imgHeight, undefined, 'FAST', 0);
            resolve();
          } catch (error) {
            reject(error);
          }
        };
        img.onerror = reject;
      });

      // Iniciar o carregamento da imagem
      img.src = clubInfo.logo_url;

      // Aguardar o carregamento e processamento da imagem
      await loadImage;
    } catch (logoError) {
      console.error("Erro ao adicionar logo ao PDF:", logoError);
    }
  }

  // Adicionar data do relatório (após o logo para garantir que fique acima)
  const currentDate = new Date().toLocaleDateString('pt-BR');
  doc.setFontSize(10);
  doc.text(`Data do relatório: ${currentDate}`, margin, 50);

  // Posição Y inicial para o conteúdo
  let yPosition = 60;

  const formatMeal = (desc: string, start?: string, end?: string) => {
    const time = start || end ? `${start || ''}${start && end ? ' - ' : ''}${end || ''}` : '';
    return time ? `${time}\n${desc}` : desc;
  };

  // Preparar dados para a tabela
  const tableData = weeklyMenu.meals.map(meal => [
    meal.day,
    formatMeal(meal.breakfast, meal.breakfastStart, meal.breakfastEnd),
    formatMeal(meal.lunch, meal.lunchStart, meal.lunchEnd),
    formatMeal(meal.snack, meal.snackStart, meal.snackEnd),
    formatMeal(meal.dinner, meal.dinnerStart, meal.dinnerEnd)
  ]);

  // Adicionar a tabela ao PDF
  autoTable(doc, {
    startY: yPosition,
    head: [['Dia', 'Café da Manhã', 'Almoço', 'Lanche', 'Jantar']],
    body: tableData,
    theme: 'grid',
    headStyles: {
      fillColor: getClubPrimaryColorRgb(),
      textColor: [255, 255, 255],
      halign: 'center'
    },
    styles: {
      overflow: 'linebreak',
      cellPadding: 5
    },
    columnStyles: {
      0: { cellWidth: 25, halign: 'center', fontStyle: 'bold' }, // Dia
      1: { cellWidth: 'auto' }, // Café da Manhã
      2: { cellWidth: 'auto' }, // Almoço
      3: { cellWidth: 'auto' }, // Lanche
      4: { cellWidth: 'auto' }  // Jantar
    },
  });

  // Adicionar rodapé
  const pageCount = doc.internal.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(8);
    doc.setTextColor(100, 100, 100);
    doc.text(
      `© ${new Date().getFullYear()} ${clubInfo.name} - Todos os direitos reservados`,
      pageWidth / 2,
      pageHeight - 10,
      { align: 'center' }
    );
    doc.text(
      `Página ${i} de ${pageCount}`,
      pageWidth - margin,
      pageHeight - 10,
      { align: 'right' }
    );
  }

  // Salvar o PDF
  doc.save(filename);
}

/**
 * @param clothingData Dados de rouparia por categoria
 * @param clubInfo Informações do clube
 * @param filename Nome do arquivo PDF
 */
export async function generateClothingControlReport(
  clothingData: {
    category: string;
    players: {
      id: string;
      name: string;
      birthdate?: string;
      shirt_size?: string;
      shorts_size?: string;
      sock_size?: string;
      shoes_size?: string;
      training_shirt_size?: string;
      training_shorts_size?: string;
      jacket_size?: string;
      pants_size?: string;
    }[];
  }[],
  clubInfo: ClubInfo,
  filename: string = 'controle-rouparia.pdf'
): Promise<void> {
  // Criar um novo documento PDF
  const doc = new jsPDF() as unknown as jsPDFWithAutoTable;

  // Configurações de página
  const pageWidth = doc.internal.pageSize.getWidth();
  const pageHeight = doc.internal.pageSize.getHeight();
  const margin = 10;

  // Adicionar título
  doc.setFontSize(18);
  doc.text(title, pageWidth / 2, 20, { align: 'center' });

  // Adicionar informações do clube
  doc.setFontSize(12);
  doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, margin, 30);

  // Tentar adicionar o logo do clube, se disponível
  if (clubInfo.logo_url) {
    try {
      const img = new Image();

      // Criar uma promessa para lidar com o carregamento da imagem
      const loadImage = new Promise<void>((resolve, reject) => {
        img.onload = () => {
          try {
            // Calcular proporção para manter as proporções
            const imgWidth = 30;
            const imgHeight = (img.height * imgWidth) / img.width;

            // Adicionar a imagem ao PDF (alinhada à direita)
            const imgX = pageWidth - margin - 41;  // Ajuste a posição X
            const imgY = -10 + imgWidth;  // Ajuste a posição Y
            doc.addImage(img, 'PNG', imgX, imgY, imgWidth, imgHeight, undefined, 'FAST', 0);
            resolve();
          } catch (error) {
            reject(error);
          }
        };
        img.onerror = reject;
      });

      // Iniciar o carregamento da imagem
      img.src = clubInfo.logo_url;

      // Aguardar o carregamento e processamento da imagem
      await loadImage;
    } catch (logoError) {
      console.error("Erro ao adicionar logo ao PDF:", logoError);
    }
  }

  // Adicionar data do relatório (após o logo para garantir que fique acima)
  const currentDate = new Date().toLocaleDateString('pt-BR');
  doc.setFontSize(10);
  doc.text(`Data do relatório: ${currentDate}`, margin, 50);

  // Posição Y inicial para o conteúdo
  let yPosition = 45;

  // Para cada categoria, mostrar os jogadores e seus tamanhos
  clothingData.forEach((categoryData, index) => {
    // Título da categoria
    doc.setFontSize(14);
    doc.setTextColor(0, 0, 0);
    doc.setFillColor(242, 242, 242);
    doc.rect(margin, yPosition, pageWidth - 2 * margin, 8, 'F');
    doc.text(`Categoria: ${categoryData.category}`, margin + 2, yPosition + 5);
    yPosition += 12;

    // Ordenar jogadores por nome
    const sortedPlayers = [...categoryData.players].sort((a, b) => a.name.localeCompare(b.name));

    // Preparar dados para a tabela
    const tableData = sortedPlayers.map(player => [
      player.name || '-',
      player.birthdate ? new Date(player.birthdate).toLocaleDateString('pt-BR') : '-',
      player.shirt_size || '-',
      player.shorts_size || '-',
      player.sock_size || '-',
      player.shoes_size || '-',
      player.training_shirt_size || '-',
      player.training_shorts_size || '-',
      player.jacket_size || '-',
      player.pants_size || '-'
    ]);

    // Adicionar a tabela ao PDF
    autoTable(doc, {
      startY: yPosition,
      head: [['Nome', 'Data Nasc.', 'Camisa', 'Shorts', 'Meias', 'Chuteira', 'Camisa Treino', 'Shorts Treino', 'Jaqueta', 'Calça']],
      body: tableData,
      theme: 'striped',
      headStyles: { fillColor: getClubPrimaryColorRgb(), textColor: [255, 255, 255] },
      margin: { left: margin, right: margin },
      styles: {
        fontSize: 8,
        cellPadding: 3
      },
      columnStyles: {
        0: { cellWidth: 'auto' }, // Nome
        1: { cellWidth: 'auto' }, // Data Nasc.
        2: { cellWidth: 15, halign: 'center' }, // Camisa
        3: { cellWidth: 15, halign: 'center' }, // Shorts
        4: { cellWidth: 15, halign: 'center' }, // Meias
        5: { cellWidth: 15, halign: 'center' }, // Chuteira
        6: { cellWidth: 15, halign: 'center' }, // Camisa Treino
        7: { cellWidth: 15, halign: 'center' }, // Shorts Treino
        8: { cellWidth: 15, halign: 'center' }, // Jaqueta
        9: { cellWidth: 15, halign: 'center' }  // Calça
      },
    });

    // Atualizar a posição Y para o próximo conteúdo
    const docWithTable = doc as jsPDFWithAutoTable;
    yPosition = docWithTable.lastAutoTable.finalY + 15;

    // Adicionar linha divisória entre categorias (exceto a última)
    if (index < clothingData.length - 1) {
      doc.setDrawColor(200, 200, 200);
      doc.setLineDashPattern([3, 3], 0);
      doc.line(margin, yPosition, pageWidth - margin, yPosition);
      doc.setLineDashPattern([], 0); // Resetar para linha sólida
      yPosition += 10;
    }
  });

  // Adicionar rodapé
  const pageCount = doc.internal.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(8);
    doc.setTextColor(100, 100, 100);
    doc.text(
      `© ${new Date().getFullYear()} ${clubInfo.name} - Todos os direitos reservados`,
      pageWidth / 2,
      pageHeight - 10,
      { align: 'center' }
    );
    doc.text(
      `Página ${i} de ${pageCount}`,
      pageWidth - margin,
      pageHeight - 10,
      { align: 'right' }
    );
  }

  // Salvar o PDF
  doc.save(filename);
}

/**
 * Gera um relatório de alojamentos em PDF
 * @param accommodations Lista de alojamentos
 * @param clubInfo Informações do clube
 * @param playersByCategory Jogadores sem alojamento agrupados por categoria
 * @param filename Nome do arquivo PDF
 */
export async function generateAccommodationReport(
  accommodations: any[],
  clubInfo: ClubInfo,
  playersByCategory: Record<string, any[]> = {},
  filename: string = 'relatorio-alojamentos.pdf'
): Promise<void> {
  // Criar um novo documento PDF
  const doc = new jsPDF() as unknown as jsPDFWithAutoTable;

  // Configurações de página
  const pageWidth = doc.internal.pageSize.getWidth();
  const pageHeight = doc.internal.pageSize.getHeight();
  const margin = 10;

  // Adicionar título
  const title = 'Relatório de Alojamentos';
  doc.setFontSize(18);
  doc.text(title, pageWidth / 2, 20, { align: 'center' });

  // Adicionar informações do clube
  doc.setFontSize(12);
  doc.text(`Clube: ${clubInfo.name || 'Seu Clube'}`, margin, 30);

  if (clubInfo.address) {
    doc.text(`Endereço: ${clubInfo.address}`, margin, 35);
  }

  if (clubInfo.phone) {
    doc.text(`Telefone: ${clubInfo.phone}`, margin, 40);
  }

  if (clubInfo.zip_code) {
    doc.text(`CEP: ${clubInfo.zip_code}`, margin, 45);
  }

  // Tentar adicionar o logo do clube, se disponível
  if (clubInfo.logo_url) {
    try {
      const img = new Image();

      // Criar uma promessa para lidar com o carregamento da imagem
      const loadImage = new Promise<void>((resolve, reject) => {
        img.onload = () => {
          try {
            // Calcular proporção para manter as proporções
            const imgWidth = 30;
            const imgHeight = (img.height * imgWidth) / img.width;

            // Adicionar a imagem ao PDF (alinhada à direita)
            const imgX = pageWidth - margin - 41;  // Ajuste a posição X
            const imgY = -10 + imgWidth;  // Ajuste a posição Y
            doc.addImage(img, 'PNG', imgX, imgY, imgWidth, imgHeight, undefined, 'FAST', 0);
            resolve();
          } catch (error) {
            reject(error);
          }
        };
        img.onerror = reject;
      });

      // Iniciar o carregamento da imagem
      img.src = clubInfo.logo_url;

      // Aguardar o carregamento e processamento da imagem
      await loadImage;
    } catch (logoError) {
      console.error("Erro ao adicionar logo ao PDF:", logoError);
    }
  }

  // Adicionar data do relatório (após o logo para garantir que fique acima)
  const currentDate = new Date().toLocaleDateString('pt-BR');
  doc.setFontSize(10);
  doc.text(`Data do relatório: ${currentDate}`, margin, 50);

  // Posição Y inicial para o conteúdo
  let yPosition = 55;

  // Seção de jogadores sem alojamento
  if (playersByCategory && Object.keys(playersByCategory).length > 0) {
    // Título da seção
    doc.setFontSize(14);
    doc.setTextColor(183, 28, 28); // Cor vermelha para destaque
    doc.text('Jogadores sem Alojamento', margin, yPosition);
    yPosition += 8;

    // Para cada categoria, mostrar os jogadores
    Object.entries(playersByCategory).forEach(([category, players]) => {
      if (players.length === 0) return;

      // Título da categoria
      doc.setFontSize(12);
      doc.setTextColor(0, 0, 0); // Voltar para cor preta
      doc.text(`Categoria: ${category}`, margin, yPosition);
      yPosition += 6;

      // Ordenar jogadores primeiro por posição, depois por nome
      const sortedCategoryPlayers = [...players].sort((a: any, b: any) => {
        const posA = POSITION_ORDER.indexOf(a.position);
        const posB = POSITION_ORDER.indexOf(b.position);

        // Se ambas as posições estão na lista, ordenar conforme a lista
        if (posA !== -1 && posB !== -1) {
          if (posA !== posB) {
            return posA - posB;
          }
        }

        // Se apenas uma posição está na lista, ela vem primeiro
        if (posA !== -1 && posB === -1) return -1;
        if (posA === -1 && posB !== -1) return 1;

        // Se nenhuma posição está na lista ou são da mesma posição, ordenar alfabeticamente por nome
        return a.name.localeCompare(b.name);
      });

      // Preparar dados para a tabela
      const tableData = sortedCategoryPlayers.map((player: any) => [
        player.name || 'Jogador sem nome',
        player.nickname || '-',
        player.birthdate ? new Date(player.birthdate).toLocaleDateString('pt-BR') : '-',
        player.category || '-'
      ]);

      // Adicionar a tabela ao PDF
      autoTable(doc, {
        startY: yPosition,
        head: [['Nome', 'Apelido', 'Data Nascimento', 'Categoria']],
        body: tableData,
        theme: 'striped',
        headStyles: { fillColor: getClubPrimaryColorRgb(), textColor: [255, 255, 255] },
        margin: { left: margin, right: margin },
      });

      // Atualizar a posição Y para o próximo conteúdo
      const docWithTable = doc as jsPDFWithAutoTable;
      yPosition = docWithTable.lastAutoTable.finalY + 10;
    });

    // Adicionar linha divisória
    doc.setDrawColor(200, 200, 200);
    doc.line(margin, yPosition, pageWidth - margin, yPosition);
    yPosition += 10;
  }

  // Conteúdo para cada alojamento
  accommodations.forEach((accommodation, index) => {
    // Título do alojamento
    doc.setFontSize(14);
    doc.setTextColor(0, 0, 0);
    doc.setFillColor(242, 242, 242);
    doc.rect(margin, yPosition, pageWidth - 2 * margin, 8, 'F');
    doc.text(accommodation.name, margin + 2, yPosition + 5);
    yPosition += 10;

    // Informações do alojamento
    doc.setFontSize(10);

    // Endereço
    if (accommodation.address) {
      doc.text(`Endereço: ${accommodation.address}`, margin, yPosition);
      yPosition += 5;
    }

    // Capacidade
    doc.text(`Capacidade: ${accommodation.capacity || '-'} pessoas`, margin, yPosition);
    yPosition += 5;

    // Responsável
    if (accommodation.manager) {
      doc.text(`Responsável: ${accommodation.manager}`, margin, yPosition);
      yPosition += 5;
    }

    // Contato
    if (accommodation.contact) {
      doc.text(`Contato: ${accommodation.contact}`, margin, yPosition);
      yPosition += 5;
    }

    yPosition += 5;

    // Tabela de hóspedes (jogadores e colaboradores)
    const hasPlayers = accommodation.players && accommodation.players.length > 0;
    const hasCollaborators = accommodation.collaborators && accommodation.collaborators.length > 0;

    if (hasPlayers || hasCollaborators) {
      // Título da seção de hóspedes
      doc.setFontSize(12);
      doc.text('Hóspedes Alojados', margin, yPosition);
      yPosition += 6;

      // Preparar dados para a tabela
      let tableData: any[] = [];

      // Agrupar hóspedes por quarto (para hotéis)
      if (accommodation.type === 'hotel') {
        // Criar um mapa de quartos e seus hóspedes
        const roomsMap = new Map();

        // Adicionar jogadores ao mapa
        if (accommodation.players) {
          accommodation.players.forEach((player: any) => {
            const roomKey = player.room || 'Sem quarto';
            if (!roomsMap.has(roomKey)) {
              roomsMap.set(roomKey, []);
            }
            roomsMap.get(roomKey).push({
              ...player,
              type: 'Jogador'
            });
          });
        }

        // Adicionar colaboradores ao mapa
        if (accommodation.collaborators) {
          accommodation.collaborators.forEach((collaborator: any) => {
            const roomKey = collaborator.room_number || 'Sem quarto';
            if (!roomsMap.has(roomKey)) {
              roomsMap.set(roomKey, []);
            }
            roomsMap.get(roomKey).push({
              name: collaborator.collaborator_name || collaborator.collaborators?.full_name,
              nickname: '-',
              birthdate: null,
              category: collaborator.collaborator_role || collaborator.collaborators?.role || '-',
              since: collaborator.check_in_date,
              type: 'Colaborador'
            });
          });
        }

        // Ordenar os quartos
        const sortedRooms = Array.from(roomsMap.keys()).sort();

        // Para cada quarto, adicionar os hóspedes
        sortedRooms.forEach(roomNumber => {
          const guestsInRoom = roomsMap.get(roomNumber);

          // Adicionar uma linha de cabeçalho para o quarto
          if (roomNumber !== 'Sem quarto') {
            tableData.push([
              {
                content: `Quarto ${roomNumber} (${guestsInRoom.length} hóspedes)`,
                colSpan: 6,
                styles: {
                  fillColor: [233, 236, 239],
                  fontStyle: 'bold',
                  fontSize: 10
                }
              }
            ]);
          }

          // Adicionar os hóspedes do quarto
          guestsInRoom.forEach((guest: any) => {
            tableData.push([
              guest.name || 'Hóspede sem nome',
              guest.nickname || '-',
              guest.birthdate ? new Date(guest.birthdate).toLocaleDateString('pt-BR') : '-',
              guest.category || '-',
              guest.since ? new Date(guest.since).toLocaleDateString('pt-BR') : '-',
              guest.type || 'Jogador'
            ]);
          });
        });
      } else {
        // Para apartamentos, listar todos os hóspedes normalmente
        const allGuests = [];

        // Adicionar jogadores
        if (accommodation.players) {
          accommodation.players.forEach((player: any) => {
            allGuests.push({
              name: player.name || 'Jogador sem nome',
              nickname: player.nickname || '-',
              birthdate: player.birthdate,
              category: player.category || '-',
              since: player.since,
              type: 'Jogador'
            });
          });
        }

        // Adicionar colaboradores
        if (accommodation.collaborators) {
          accommodation.collaborators.forEach((collaborator: any) => {
            allGuests.push({
              name: collaborator.collaborator_name || collaborator.collaborators?.full_name || 'Colaborador sem nome',
              nickname: '-',
              birthdate: null,
              category: collaborator.collaborator_role || collaborator.collaborators?.role || '-',
              since: collaborator.check_in_date,
              type: 'Colaborador'
            });
          });
        }

        // Ordenar por nome
        allGuests.sort((a, b) => a.name.localeCompare(b.name));

        tableData = allGuests.map((guest: any) => [
          guest.name,
          guest.nickname,
          guest.birthdate ? new Date(guest.birthdate).toLocaleDateString('pt-BR') : '-',
          guest.category,
          guest.since ? new Date(guest.since).toLocaleDateString('pt-BR') : '-',
          guest.type
        ]);
      }

      // Adicionar a tabela ao PDF
      autoTable(doc, {
        startY: yPosition,
        head: [['Nome', 'Apelido', 'Data Nasc.', 'Categoria/Função', 'Check-in', 'Tipo']],
        body: tableData,
        theme: 'striped',
        headStyles: { fillColor: getClubPrimaryColorRgb(), textColor: [255, 255, 255] },
        margin: { left: margin, right: margin },
        columnStyles: {
          0: { cellWidth: 'auto' },
          1: { cellWidth: 'auto' },
          2: { cellWidth: 'auto' },
          3: { cellWidth: 'auto' },
          4: { cellWidth: 'auto' },
          5: { cellWidth: 'auto' }
        },
        didParseCell: function(data) {
          // Aplicar estilos personalizados para células com conteúdo de objeto
          if (data.cell.raw && typeof data.cell.raw === 'object' && data.cell.raw.content) {
            // Aplicar estilos personalizados da célula
            if (data.cell.raw.styles) {
              Object.assign(data.cell.styles, data.cell.raw.styles);
            }

            // Aplicar mesclagem de células
            if (data.cell.raw.colSpan) {
              data.cell.colSpan = data.cell.raw.colSpan;
            }

            // Definir o conteúdo da célula
            data.cell.text = [data.cell.raw.content];
          }
        },
      });

      // Atualizar a posição Y para o próximo conteúdo
      const docWithTable = doc as jsPDFWithAutoTable;
      yPosition = docWithTable.lastAutoTable.finalY + 15;
    } else {
      // Mensagem de nenhum hóspede alojado
      doc.setFontSize(10);
      doc.setTextColor(100, 100, 100);
      // Use setFont with italic style instead of setFontStyle
      doc.setFont(undefined, 'italic');
      doc.text('Nenhum hóspede alojado.', margin, yPosition);
      yPosition += 10;
    }

    // Adicionar linha divisória entre alojamentos (exceto o último)
    if (index < accommodations.length - 1) {
      doc.setDrawColor(200, 200, 200);
      doc.setLineDashPattern([3, 3], 0);
      doc.line(margin, yPosition, pageWidth - margin, yPosition);
      doc.setLineDashPattern([], 0); // Resetar para linha sólida
      yPosition += 10;
    }
  });

  // Adicionar rodapé
  const pageCount = doc.internal.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(8);
    doc.setTextColor(100, 100, 100);
    doc.text(
      `© ${new Date().getFullYear()} ${clubInfo.name} - Todos os direitos reservados`,
      pageWidth / 2,
      pageHeight - 10,
      { align: 'center' }
    );
    doc.text(
      `Página ${i} de ${pageCount}`,
      pageWidth - margin,
      pageHeight - 10,
      { align: 'right' }
    );
  }

  // Salvar o PDF
  doc.save(filename);
}

export async function generateMatchAccommodationPDF(
  callup: any,
  players: any[],
  clubInfo: ClubInfo,
  filename: string = 'alojamento-partida.pdf'
): Promise<void> {
  const doc = new jsPDF() as jsPDFWithAutoTable;
  const pageWidth = doc.internal.pageSize.getWidth();
  const margin = 15;
  let y = margin;

  doc.setFontSize(18);
  doc.text('Relação de Alojamento', pageWidth / 2, y, { align: 'center' });
  y += 8;

  doc.setFontSize(14);
  doc.text(clubInfo.name || 'Clube', margin, y);

  if (clubInfo.logo_url) {
    try {
      const img = new Image();
      const load = new Promise<void>((res, rej) => {
        img.onload = () => {
          const w = 25;
          const h = (img.height * w) / img.width;
          doc.addImage(img, 'PNG', pageWidth - margin - w, y - 6, w, h);
          res();
        };
        img.onerror = rej;
      });
      img.src = clubInfo.logo_url;
      await load;
    } catch {}
  }

  y += 10;
  doc.setFontSize(10);
  doc.text(`Competição: ${callup.tournament_type}`, margin, y); y += 5;
  doc.text(`Data: ${new Date(callup.match_date).toLocaleDateString('pt-BR')}`, margin, y); y += 5;
  doc.text(`Local: ${callup.match_location}`, margin, y); y += 8;

  const section = (
    title: string,
    list: any[]
  ) => {
    if (list.length === 0) return;
    doc.setFontSize(12);
    doc.text(title, margin, y);
    y += 4;

    const sorted = [...list].sort((a, b) => {
      const n1 = (a.player_name || a.user_name || a.name || '').toLowerCase();
      const n2 = (b.player_name || b.user_name || b.name || '').toLowerCase();
      return n1.localeCompare(n2, 'pt-BR');
    });

    const tableData = sorted.map(p => [
      p.player_name || p.user_name || p.name || '-',
      formatDateUTC(p.player_birthdate || p.collaborator_birthdate),
      formatCPF(p.player_cpf || p.collaborator_cpf || '')
    ]);

    autoTable(doc, {
      startY: y,
      head: [['Nome', 'Data Nasc.', 'CPF']],
      body: tableData,
      theme: 'striped',
      headStyles: { fillColor: getClubPrimaryColorRgb(), textColor: [255,255,255] },
      margin: { left: margin, right: margin },
    });

    y = (doc as any).lastAutoTable.finalY + 8;
  };

  const playersOnly = players.filter(p => p.player_id);
  section('Jogadores Convocados', playersOnly);

  section('Comissão Técnica', players.filter(p => TECHNICAL_ROLES.includes(p.role)));
  section('Staff', players.filter(p => STAFF_ROLES.includes(p.role)));
  section('Diretoria Executiva', players.filter(p => EXECUTIVE_ROLES.includes(p.role)));

  doc.save(filename);
}

// Exportar funções de relatório
export { generateClothingReport };

// Definição das posições no campo para PDF
const PDF_FIELD_POSITIONS = {
  // Goleiros (4 posições)
  gk1: { x: 45, y: 87, label: "GOL", type: "goalkeeper" },
  gk2: { x: 30, y: 87, label: "GOL", type: "goalkeeper" },
  gk3: { x: 70, y: 87, label: "GOL", type: "goalkeeper" },
  gk4: { x: 55, y: 87, label: "GOL", type: "goalkeeper" },

  // Defensores (3 posições por lado)
  ld1: { x: 80, y: 70, label: "LD", type: "defender" },
  ld2: { x: 88, y: 70, label: "LD", type: "defender" },
  ld3: { x: 96, y: 70, label: "LD", type: "defender" },

  zag1: { x: 60, y: 70, label: "ZAG", type: "defender" },
  zag2: { x: 50, y: 70, label: "ZAG", type: "defender" },
  zag3: { x: 40, y: 70, label: "ZAG", type: "defender" },

  le1: { x: 21, y: 70, label: "LE", type: "defender" },
  le2: { x: 13, y: 70, label: "LE", type: "defender" },
  le3: { x: 5, y: 70, label: "LE", type: "defender" },

  // Meio-campistas (3 posições por lado)
  md1: { x: 80, y: 50, label: "MD", type: "midfielder" },
  md2: { x: 88, y: 50, label: "MD", type: "midfielder" },
  md3: { x: 96, y: 50, label: "MD", type: "midfielder" },

  mc1: { x: 60, y: 50, label: "MC", type: "midfielder" },
  mc2: { x: 50, y: 50, label: "MC", type: "midfielder" },
  mc3: { x: 40, y: 50, label: "MC", type: "midfielder" },

  me1: { x: 20, y: 50, label: "ME", type: "midfielder" },
  me2: { x: 12, y: 50, label: "ME", type: "midfielder" },
  me3: { x: 4, y: 50, label: "ME", type: "midfielder" },

  // Atacantes (3 posições por lado)
  ad1: { x: 80, y: 25, label: "AD", type: "attacker" },
  ad2: { x: 88, y: 25, label: "AD", type: "attacker" },
  ad3: { x: 96, y: 25, label: "AD", type: "attacker" },

  ac1: { x: 60, y: 25, label: "AC", type: "attacker" },
  ac2: { x: 50, y: 25, label: "AC", type: "attacker" },
  ac3: { x: 40, y: 25, label: "AC", type: "attacker" },

  ae1: { x: 20, y: 25, label: "AE", type: "attacker" },
  ae2: { x: 12, y: 25, label: "AE", type: "attacker" },
  ae3: { x: 4, y: 25, label: "AE", type: "attacker" },
};

// Layout das formações para o PDF de escalação
const LINEUP_FORMATION_POSITIONS: Record<string, Record<string, { x: number; y: number; label: string; type: string }>> = {
  "4-4-2": {
    GK: { x: 50, y: 10, label: "GOL", type: "goalkeeper" },
    RB: { x: 80, y: 25, label: "LD", type: "defender" },
    CB1: { x: 60, y: 25, label: "ZAG", type: "defender" },
    CB2: { x: 40, y: 25, label: "ZAG", type: "defender" },
    LB: { x: 20, y: 25, label: "LE", type: "defender" },
    RM: { x: 80, y: 50, label: "MD", type: "midfielder" },
    CM1: { x: 60, y: 50, label: "MC", type: "midfielder" },
    CM2: { x: 40, y: 50, label: "MC", type: "midfielder" },
    LM: { x: 20, y: 50, label: "ME", type: "midfielder" },
    ST1: { x: 60, y: 75, label: "ATA", type: "attacker" },
    ST2: { x: 40, y: 75, label: "ATA", type: "attacker" },
  },
  "4-3-3": {
    GK: { x: 50, y: 10, label: "GOL", type: "goalkeeper" },
    RB: { x: 80, y: 25, label: "LD", type: "defender" },
    CB1: { x: 60, y: 25, label: "ZAG", type: "defender" },
    CB2: { x: 40, y: 25, label: "ZAG", type: "defender" },
    LB: { x: 20, y: 25, label: "LE", type: "defender" },
    CDM: { x: 50, y: 40, label: "VOL", type: "midfielder" },
    CM1: { x: 65, y: 55, label: "MC", type: "midfielder" },
    CM2: { x: 35, y: 55, label: "MC", type: "midfielder" },
    RW: { x: 80, y: 75, label: "PD", type: "attacker" },
    ST: { x: 50, y: 75, label: "ATA", type: "attacker" },
    LW: { x: 20, y: 75, label: "PE", type: "attacker" },
  },
  "3-5-2": {
    GK: { x: 50, y: 10, label: "GOL", type: "goalkeeper" },
    CB1: { x: 70, y: 25, label: "ZAG", type: "defender" },
    CB2: { x: 50, y: 25, label: "ZAG", type: "defender" },
    CB3: { x: 30, y: 25, label: "ZAG", type: "defender" },
    RWB: { x: 85, y: 45, label: "ALA", type: "midfielder" },
    CM1: { x: 65, y: 50, label: "MC", type: "midfielder" },
    CM2: { x: 50, y: 50, label: "MC", type: "midfielder" },
    CM3: { x: 35, y: 50, label: "MC", type: "midfielder" },
    LWB: { x: 15, y: 45, label: "ALA", type: "midfielder" },
    ST1: { x: 60, y: 75, label: "ATA", type: "attacker" },
    ST2: { x: 40, y: 75, label: "ATA", type: "attacker" },
  },
  "4-2-3-1": {
    GK: { x: 50, y: 10, label: "GOL", type: "goalkeeper" },
    RB: { x: 80, y: 25, label: "LD", type: "defender" },
    CB1: { x: 60, y: 25, label: "ZAG", type: "defender" },
    CB2: { x: 40, y: 25, label: "ZAG", type: "defender" },
    LB: { x: 20, y: 25, label: "LE", type: "defender" },
    CDM1: { x: 60, y: 40, label: "VOL", type: "midfielder" },
    CDM2: { x: 40, y: 40, label: "VOL", type: "midfielder" },
    CAM1: { x: 70, y: 60, label: "MEI", type: "midfielder" },
    CAM2: { x: 50, y: 60, label: "MEI", type: "midfielder" },
    CAM3: { x: 30, y: 60, label: "MEI", type: "midfielder" },
    ST: { x: 50, y: 75, label: "ATA", type: "attacker" },
  },
  "5-3-2": {
    GK: { x: 50, y: 10, label: "GOL", type: "goalkeeper" },
    RB: { x: 85, y: 25, label: "LD", type: "defender" },
    CB1: { x: 65, y: 25, label: "ZAG", type: "defender" },
    CB2: { x: 50, y: 25, label: "ZAG", type: "defender" },
    CB3: { x: 35, y: 25, label: "ZAG", type: "defender" },
    LB: { x: 15, y: 25, label: "LE", type: "defender" },
    CM1: { x: 65, y: 50, label: "MC", type: "midfielder" },
    CM2: { x: 50, y: 50, label: "MC", type: "midfielder" },
    CM3: { x: 35, y: 50, label: "MC", type: "midfielder" },
    ST1: { x: 60, y: 75, label: "ATA", type: "attacker" },
    ST2: { x: 40, y: 75, label: "ATA", type: "attacker" },
  },
};

interface PDFGenerationOptions {
  categoryName: string;
  clubName: string;
  mapping: FieldMapping;
  date?: string;
}

interface LineupPDFOptions {
  formation: string;
  clubName: string;
  lineup: Record<string, MappingPosition | null>;
  reserves: MappingPosition[];
  coachName?: string;
  coachImage?: string;
  date?: string;
}


export async function generateMappingPDF(options: PDFGenerationOptions): Promise<void> {
  try {
    // Criar novo documento PDF em formato paisagem (A4)
    const doc = new jsPDF({
      orientation: 'landscape',
      unit: 'mm',
      format: 'a4'
    });

    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();

    // Configurações do campo otimizadas (sem legenda)
    const fieldWidth = pageWidth - 30; // Margem reduzida de 15mm de cada lado
    const fieldHeight = pageHeight - 40; // Margem reduzida (sem espaço para legenda)
    const fieldX = 15; // Margem esquerda reduzida
    const fieldY = 25; // Posição mais alta (cabeçalho mais próximo do topo)

    // Cabeçalho compacto
    doc.setFontSize(18); // Reduzido de 20 para 18
    doc.setFont('helvetica', 'bold');
    doc.text(`Mapeamento - ${options.categoryName}`, pageWidth / 2, 12, { align: 'center' }); // Subiu de 20 para 12

    doc.setFontSize(12); // Reduzido de 14 para 12
    doc.setFont('helvetica', 'normal');
    doc.text(options.clubName, pageWidth / 2, 20, { align: 'center' }); // Subiu de 30 para 20

    if (options.date) {
      doc.setFontSize(9); // Reduzido de 10 para 9
      doc.text(`Data: ${options.date}`, pageWidth - 15, 8, { align: 'right' }); // Subiu e ajustou margem
    }

    // Desenhar campo de futebol
    drawFootballField(doc, fieldX, fieldY, fieldWidth, fieldHeight);

    // Adicionar jogadores no campo (aguardar carregamento das imagens)
    await addPlayersToField(doc, options.mapping, fieldX, fieldY, fieldWidth, fieldHeight);

    // Adicionar legenda no canto superior esquerdo (igual à página)
    addFieldLegend(doc, fieldX, fieldY);

    // Salvar PDF
    const fileName = `mapeamento_${options.categoryName.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;
    doc.save(fileName);

  } catch (error) {
    console.error('Erro ao gerar PDF:', error);
    throw new Error('Erro ao gerar PDF. Verifique se as dependências estão instaladas.');
  }
}

export async function generateLineupPDF(options: LineupPDFOptions): Promise<void> {
  try {
    const doc = new jsPDF({ orientation: 'landscape', unit: 'mm', format: 'a4' });

    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();

    const fieldWidth = pageWidth - 30;
    const fieldHeight = pageHeight - 55;
    const fieldX = 15;
    const fieldY = 20;

    doc.setFontSize(18);
    doc.setFont('helvetica', 'bold');
    doc.text(`Escalação - ${options.formation}`, pageWidth / 2, 12, { align: 'center' });

    doc.setFontSize(12);
    doc.setFont('helvetica', 'normal');
    doc.text(options.clubName, pageWidth / 2, 18, { align: 'center' });

    if (options.date) {
      doc.setFontSize(9);
      doc.text(`Data: ${options.date}`, pageWidth - 15, 8, { align: 'right' });
    }

    drawFootballField(doc, fieldX, fieldY, fieldWidth, fieldHeight);
    await addLineupPlayersToField(doc, options.formation, options.lineup, fieldX, fieldY, fieldWidth, fieldHeight);

    await addReservesAndCoach(doc, options.reserves, options.coachName, options.coachImage, fieldX, fieldY + fieldHeight + 10, pageWidth);

    const filename = `escalacao_${new Date().toISOString().split('T')[0]}.pdf`;
    doc.save(filename);
  } catch (error) {
    console.error('Erro ao gerar PDF:', error);
    throw new Error('Erro ao gerar PDF');
  }
}

function drawFootballField(doc: any, x: number, y: number, width: number, height: number): void {
  // Campo principal com gradiente verde (simulando o gradiente da página)
  // Criar gradiente usando múltiplas camadas de verde
  const gradientSteps = 20;
  const startColor = [34, 197, 94]; // green-500
  const endColor = [21, 128, 61]; // green-700

  for (let i = 0; i < gradientSteps; i++) {
    const ratio = i / (gradientSteps - 1);
    const r = Math.round(startColor[0] + (endColor[0] - startColor[0]) * ratio);
    const g = Math.round(startColor[1] + (endColor[1] - startColor[1]) * ratio);
    const b = Math.round(startColor[2] + (endColor[2] - startColor[2]) * ratio);

    doc.setFillColor(r, g, b);
    const stripHeight = height / gradientSteps;
    doc.rect(x, y + i * stripHeight, width, stripHeight, 'F');
  }

  // Bordas arredondadas (simulando rounded-lg da página)
  doc.setDrawColor(255, 255, 255);
  doc.setLineWidth(1);

  // Margem interna para simular as bordas arredondadas
  const margin = 2;
  const fieldInnerX = x + margin;
  const fieldInnerY = y + margin;
  const fieldInnerWidth = width - (margin * 2);
  const fieldInnerHeight = height - (margin * 2);

  // Linhas do campo (ajustadas para a margem interna)
  doc.setDrawColor(255, 255, 255);
  doc.setLineWidth(0.8); // Linha um pouco mais espessa como na página

  // Linha lateral principal
  doc.rect(fieldInnerX, fieldInnerY, fieldInnerWidth, fieldInnerHeight);

  // Linha central
  doc.line(fieldInnerX, fieldInnerY + fieldInnerHeight / 2, fieldInnerX + fieldInnerWidth, fieldInnerY + fieldInnerHeight / 2);

  // Círculo central (proporcionalmente igual à página)
  const centerX = fieldInnerX + fieldInnerWidth / 2;
  const centerY = fieldInnerY + fieldInnerHeight / 2;
  const circleRadius = fieldInnerWidth * 0.08; // 8% da largura como na página
  doc.circle(centerX, centerY, circleRadius, 'S');

  // Áreas do goleiro (proporções iguais à página)
  const goalAreaWidth = fieldInnerWidth * 0.3; // 30% da largura
  const goalAreaHeight = fieldInnerHeight * 0.15; // 15% da altura
  const smallAreaWidth = fieldInnerWidth * 0.16; // 16% da largura
  const smallAreaHeight = fieldInnerHeight * 0.08; // 8% da altura

  // Área grande (superior)
  doc.rect(fieldInnerX + (fieldInnerWidth - goalAreaWidth) / 2, fieldInnerY, goalAreaWidth, goalAreaHeight);
  // Área pequena (superior)
  doc.rect(fieldInnerX + (fieldInnerWidth - smallAreaWidth) / 2, fieldInnerY, smallAreaWidth, smallAreaHeight);

  // Área grande (inferior)
  doc.rect(fieldInnerX + (fieldInnerWidth - goalAreaWidth) / 2, fieldInnerY + fieldInnerHeight - goalAreaHeight, goalAreaWidth, goalAreaHeight);
  // Área pequena (inferior)
  doc.rect(fieldInnerX + (fieldInnerWidth - smallAreaWidth) / 2, fieldInnerY + fieldInnerHeight - smallAreaHeight, smallAreaWidth, smallAreaHeight);
}

async function addPlayersToField(
  doc: any,
  mapping: FieldMapping,
  fieldX: number,
  fieldY: number,
  fieldWidth: number,
  fieldHeight: number
): Promise<void> {
  // Ajustar para a margem interna do campo
  const margin = 2;
  const adjustedFieldX = fieldX + margin;
  const adjustedFieldY = fieldY + margin;
  const adjustedFieldWidth = fieldWidth - (margin * 2);
  const adjustedFieldHeight = fieldHeight - (margin * 2);

  for (const [positionKey, player] of Object.entries(mapping)) {
    if (!player) continue;

    const position = PDF_FIELD_POSITIONS[positionKey as keyof typeof PDF_FIELD_POSITIONS];
    if (!position) continue;

    // Calcular posição no PDF (usando campo ajustado)
    const playerX = adjustedFieldX + (position.x / 100) * adjustedFieldWidth;
    const playerY = adjustedFieldY + (position.y / 100) * adjustedFieldHeight;

    // Tamanho igual ao da página (h-12 w-12 = 48px ≈ 12mm)
    let avatarRadius = 6; // 12mm de diâmetro = 6mm de raio

    try {
      // Tentar carregar e adicionar a foto do jogador
      if (player.player_image) {
        // Desenhar borda branca primeiro (border-2 border-white da página)
        doc.setFillColor(255, 255, 255);
        doc.circle(playerX, playerY, avatarRadius + 0.5, 'F'); // Borda branca

        // Adicionar shadow-lg (sombra)
        doc.setFillColor(0, 0, 0, 0.1); // Sombra sutil
        doc.circle(playerX + 0.5, playerY + 0.5, avatarRadius + 0.5, 'F');

        await addPlayerImageToCircle(doc, player.player_image, playerX, playerY, avatarRadius, position.type);
      } else {
        // Se não houver foto, usar fallback com cor da posição (igual à página)
        // Desenhar borda branca primeiro
        doc.setFillColor(255, 255, 255);
        doc.circle(playerX, playerY, avatarRadius + 0.5, 'F'); // Borda branca

        // Adicionar shadow-lg (sombra)
        doc.setFillColor(0, 0, 0, 0.1); // Sombra sutil
        doc.circle(playerX + 0.5, playerY + 0.5, avatarRadius + 0.5, 'F');

        // Círculo com cor da posição
        doc.setFillColor(...getPositionColor(position.type));
        doc.circle(playerX, playerY, avatarRadius, 'F');

        // Adicionar número do jogador no círculo (igual à página)
        doc.setTextColor(255, 255, 255);
        doc.setFontSize(8); // Fonte para o número
        doc.setFont('helvetica', 'bold');
        doc.text(player.player_number.toString(), playerX, playerY + 1.5, { align: 'center' });
      }
    } catch (error) {
      console.warn(`Erro ao carregar imagem do jogador ${player.player_name}:`, error);
      // Fallback igual ao caso sem foto
      // Desenhar borda branca primeiro
      doc.setFillColor(255, 255, 255);
      doc.circle(playerX, playerY, avatarRadius + 0.5, 'F'); // Borda branca

      // Adicionar shadow-lg (sombra)
      doc.setFillColor(0, 0, 0, 0.1); // Sombra sutil
      doc.circle(playerX + 0.5, playerY + 0.5, avatarRadius + 0.5, 'F');

      // Círculo com cor da posição
      doc.setFillColor(...getPositionColor(position.type));
      doc.circle(playerX, playerY, avatarRadius, 'F');

      // Adicionar número do jogador no círculo
      doc.setTextColor(255, 255, 255);
      doc.setFontSize(8);
      doc.setFont('helvetica', 'bold');
      doc.text(player.player_number.toString(), playerX, playerY + 1.5, { align: 'center' });
    }

    // Nome do jogador com background da cor da posição
    doc.setFontSize(8);
    doc.setFont('helvetica', 'bold');
    const playerName = player.player_nickname || player.player_name.split(' ')[0];

    // Posição com mais espaçamento do nome
    const nameY = playerY + avatarRadius + 6; // 6mm de distância do avatar (aumentado de 4mm)
    const positionY = nameY + 5; // 5mm abaixo do nome (mais espaçamento)

    // Calcular dimensões do texto para o background do nome
    const nameTextWidth = doc.getTextWidth(playerName);
    const textHeight = 2.5;
    const padding = 1;

    // Background com cor da posição para o nome (com bordas arredondadas simuladas)
    const positionColor = getPositionColor(position.type);
    doc.setFillColor(positionColor[0], positionColor[1], positionColor[2]); // Cor da posição

    // Simular bordas arredondadas com múltiplos retângulos pequenos
    const nameRectX = playerX - (nameTextWidth / 2) - padding;
    const nameRectY = nameY - textHeight - padding;
    const nameRectWidth = nameTextWidth + (padding * 2);
    const nameRectHeight = textHeight + (padding * 2);

    // Retângulo principal
    doc.rect(nameRectX + 0.5, nameRectY, nameRectWidth - 1, nameRectHeight, 'F');
    // Cantos arredondados
    doc.rect(nameRectX, nameRectY + 0.5, nameRectWidth, nameRectHeight - 1, 'F');

    // Texto do nome em branco
    doc.setTextColor(255, 255, 255);
    doc.text(playerName, playerX, nameY, { align: 'center' });

    // Background com cor da posição para a posição (com bordas arredondadas simuladas)
    doc.setFontSize(6); // text-[10px] da página
    const positionTextWidth = doc.getTextWidth(position.label);

    doc.setFillColor(positionColor[0], positionColor[1], positionColor[2]); // Mesma cor da posição

    const posRectX = playerX - (positionTextWidth / 2) - padding;
    const posRectY = positionY - textHeight - padding;
    const posRectWidth = positionTextWidth + (padding * 2);
    const posRectHeight = textHeight + (padding * 2);

    // Retângulo principal
    doc.rect(posRectX + 0.5, posRectY, posRectWidth - 1, posRectHeight, 'F');
    // Cantos arredondados
    doc.rect(posRectX, posRectY + 0.5, posRectWidth, posRectHeight - 1, 'F');

    // Texto da posição em branco
    doc.setTextColor(255, 255, 255);
    doc.text(position.label, playerX, positionY, { align: 'center' });
  }
}

function addFieldLegend(doc: any, fieldX: number, fieldY: number): void {
  // Posição da legenda no canto superior esquerdo (igual à página)
  const legendX = fieldX + 5; // 5mm da borda esquerda
  const legendY = fieldY + 8; // 8mm da borda superior
  const legendWidth = 35; // Largura da legenda
  const legendHeight = 25; // Altura da legenda

  // Background preto semi-transparente (bg-black bg-opacity-50 da página)
  // Usar setGState para transparência real no PDF
  doc.setGState(new doc.GState({opacity: 0.5}));
  doc.setFillColor(0, 0, 0);
  doc.rect(legendX, legendY, legendWidth, legendHeight, 'F');

  // Resetar opacidade para o resto do conteúdo
  doc.setGState(new doc.GState({opacity: 1.0}));

  // Título "Posições:" (font-bold mb-1 da página)
  doc.setTextColor(255, 255, 255); // text-white
  doc.setFontSize(8);
  doc.setFont('helvetica', 'bold');
  doc.text('Posições:', legendX + 2, legendY + 4);

  // Itens da legenda (flex items-center gap-1 mb-1 da página)
  const itemStartY = legendY + 8;
  const itemSpacing = 4;
  const circleSize = 1.5; // Tamanho dos círculos coloridos (w-3 h-3 da página)

  // Goleiros (bg-yellow-500)
  doc.setFillColor(255, 193, 7); // yellow-500
  doc.circle(legendX + 3, itemStartY, circleSize, 'F');
  doc.setTextColor(255, 255, 255);
  doc.setFontSize(7);
  doc.setFont('helvetica', 'normal');
  doc.text('Goleiros (4)', legendX + 6, itemStartY + 1);

  // Defensores (bg-blue-500)
  doc.setFillColor(0, 123, 255); // blue-500
  doc.circle(legendX + 3, itemStartY + itemSpacing, circleSize, 'F');
  doc.text('Defensores (9)', legendX + 6, itemStartY + itemSpacing + 1);

  // Meio-campistas (bg-green-500)
  doc.setFillColor(40, 167, 69); // green-500
  doc.circle(legendX + 3, itemStartY + itemSpacing * 2, circleSize, 'F');
  doc.text('Meio-campistas (9)', legendX + 6, itemStartY + itemSpacing * 2 + 1);

  // Atacantes (bg-red-500)
  doc.setFillColor(220, 53, 69); // red-500
  doc.circle(legendX + 3, itemStartY + itemSpacing * 3, circleSize, 'F');
  doc.text('Atacantes (9)', legendX + 6, itemStartY + itemSpacing * 3 + 1);
}

function getPositionColor(positionType: string): [number, number, number] {
  switch (positionType) {
    case 'goalkeeper':
      return [255, 193, 7]; // Amarelo
    case 'defender':
      return [0, 123, 255]; // Azul
    case 'midfielder':
      return [40, 167, 69]; // Verde
    case 'attacker':
      return [220, 53, 69]; // Vermelho
    default:
      return [108, 117, 125]; // Cinza
  }
}

function getPlayerInitials(playerName: string): string {
  const names = playerName.trim().split(' ');
  if (names.length >= 2) {
    return (names[0][0] + names[1][0]).toUpperCase();
  }
  return names[0].substring(0, 2).toUpperCase();
}

async function addLineupPlayersToField(
  doc: any,
  formation: string,
  lineup: Record<string, MappingPosition | null>,
  fieldX: number,
  fieldY: number,
  fieldWidth: number,
  fieldHeight: number
): Promise<void> {
  const layout = LINEUP_FORMATION_POSITIONS[formation];
  if (!layout) return;

  const margin = 2;
  const adjustedFieldX = fieldX + margin;
  const adjustedFieldY = fieldY + margin;
  const adjustedFieldWidth = fieldWidth - margin * 2;
  const adjustedFieldHeight = fieldHeight - margin * 2;

  for (const [posKey, posInfo] of Object.entries(layout)) {
    const player = lineup[posKey];
    const playerX = adjustedFieldX + (posInfo.x / 100) * adjustedFieldWidth;
    const playerY = adjustedFieldY + (posInfo.y / 100) * adjustedFieldHeight;

    const avatarRadius = 6;

    if (player) {
      try {
        if (player.player_image) {
          doc.setFillColor(255, 255, 255);
          doc.circle(playerX, playerY, avatarRadius + 0.5, 'F');
          doc.setFillColor(0, 0, 0, 0.1);
          doc.circle(playerX + 0.5, playerY + 0.5, avatarRadius + 0.5, 'F');
          await addPlayerImageToCircle(doc, player.player_image, playerX, playerY, avatarRadius, posInfo.type);
        } else {
          doc.setFillColor(255, 255, 255);
          doc.circle(playerX, playerY, avatarRadius + 0.5, 'F');
          doc.setFillColor(0, 0, 0, 0.1);
          doc.circle(playerX + 0.5, playerY + 0.5, avatarRadius + 0.5, 'F');
          doc.setFillColor(...getPositionColor(posInfo.type));
          doc.circle(playerX, playerY, avatarRadius, 'F');
          doc.setTextColor(255, 255, 255);
          doc.setFontSize(8);
          doc.setFont('helvetica', 'bold');
          doc.text(String(player.player_number), playerX, playerY + 1.5, { align: 'center' });
        }
      } catch {
        doc.setFillColor(255, 255, 255);
        doc.circle(playerX, playerY, avatarRadius + 0.5, 'F');
        doc.setFillColor(0, 0, 0, 0.1);
        doc.circle(playerX + 0.5, playerY + 0.5, avatarRadius + 0.5, 'F');
        doc.setFillColor(...getPositionColor(posInfo.type));
        doc.circle(playerX, playerY, avatarRadius, 'F');
        doc.setTextColor(255, 255, 255);
        doc.setFontSize(8);
        doc.setFont('helvetica', 'bold');
        doc.text(String(player.player_number), playerX, playerY + 1.5, { align: 'center' });
      }

      doc.setFontSize(8);
      doc.setFont('helvetica', 'bold');
      const baseName = player.player_nickname || player.player_name.split(' ')[0];
      const displayName = player.player_number ? `${baseName} #${player.player_number}` : baseName;
      const nameY = playerY + avatarRadius + 6;
      const positionY = nameY + 5;
      const nameWidth = doc.getTextWidth(displayName);
      const textHeight = 2.5;
      const padding = 1;
      const color = getPositionColor(posInfo.type);
      const rectX = playerX - nameWidth / 2 - padding;
      const rectY = nameY - textHeight - padding;
      const rectW = nameWidth + padding * 2;
      const rectH = textHeight + padding * 2;
      doc.setFillColor(...color);
      doc.rect(rectX + 0.5, rectY, rectW - 1, rectH, 'F');
      doc.rect(rectX, rectY + 0.5, rectW, rectH - 1, 'F');
      doc.setTextColor(255, 255, 255);
      doc.text(displayName, playerX, nameY, { align: 'center' });

      const posTextWidth = doc.getTextWidth(posInfo.label);
      const posRectX = playerX - posTextWidth / 2 - padding;
      const posRectY = positionY - textHeight - padding;
      const posRectW = posTextWidth + padding * 2;
      const posRectH = textHeight + padding * 2;
      doc.setFillColor(...color);
      doc.rect(posRectX + 0.5, posRectY, posRectW - 1, posRectH, 'F');
      doc.rect(posRectX, posRectY + 0.5, posRectW, posRectH - 1, 'F');
      doc.setTextColor(255, 255, 255);
      doc.setFontSize(6);
      doc.text(posInfo.label, playerX, positionY, { align: 'center' });
    }
  }
}

async function addReservesAndCoach(
  doc: any,
  reserves: MappingPosition[],
  coachName: string | undefined,
  coachImage: string | undefined,
  startX: number,
  startY: number,
  pageWidth: number
) {
  const avatarRadius = 6;
  const spacing = 20;

  doc.setFontSize(10);
  doc.setTextColor(0, 0, 0);
  doc.text('Reservas', startX, startY);

  const baseY = startY + 8;

  for (let i = 0; i < reserves.length; i++) {
    const r = reserves[i];
    const centerX = startX + i * spacing + avatarRadius;

    try {
      if (r.player_image) {
        doc.setFillColor(255, 255, 255);
        doc.circle(centerX, baseY, avatarRadius + 0.5, 'F');
        doc.setFillColor(0, 0, 0, 0.1);
        doc.circle(centerX + 0.5, baseY + 0.5, avatarRadius + 0.5, 'F');
        await addPlayerImageToCircle(doc, r.player_image, centerX, baseY, avatarRadius, 'reserve');
      } else {
        doc.setFillColor(255, 255, 255);
        doc.circle(centerX, baseY, avatarRadius + 0.5, 'F');
        doc.setFillColor(0, 0, 0, 0.1);
        doc.circle(centerX + 0.5, baseY + 0.5, avatarRadius + 0.5, 'F');
        doc.setFillColor(108, 117, 125);
        doc.circle(centerX, baseY, avatarRadius, 'F');
        doc.setTextColor(255, 255, 255);
        doc.setFontSize(8);
        doc.setFont('helvetica', 'bold');
        doc.text(String(r.player_number), centerX, baseY + 1.5, { align: 'center' });
      }
    } catch {
      doc.setFillColor(255, 255, 255);
      doc.circle(centerX, baseY, avatarRadius + 0.5, 'F');
      doc.setFillColor(0, 0, 0, 0.1);
      doc.circle(centerX + 0.5, baseY + 0.5, avatarRadius + 0.5, 'F');
      doc.setFillColor(108, 117, 125);
      doc.circle(centerX, baseY, avatarRadius, 'F');
      doc.setTextColor(255, 255, 255);
      doc.setFontSize(8);
      doc.setFont('helvetica', 'bold');
      doc.text(String(r.player_number), centerX, baseY + 1.5, { align: 'center' });
    }

    const baseName = r.player_nickname || r.player_name.split(' ')[0];
    const displayName = r.player_number ? `${baseName} #${r.player_number}` : baseName;
    doc.setFontSize(8);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(0, 0, 0);
    doc.text(displayName, centerX, baseY + avatarRadius + 6, { align: 'center' });
  }

  if (coachName) {
    const coachX = pageWidth - startX - avatarRadius;
    doc.setFontSize(10);
    doc.setTextColor(0, 0, 0);
    doc.text('Técnico', coachX, startY, { align: 'center' });

    const coachY = startY + 8;
    try {
      if (coachImage) {
        doc.setFillColor(255, 255, 255);
        doc.circle(coachX, coachY, avatarRadius + 0.5, 'F');
        doc.setFillColor(0, 0, 0, 0.1);
        doc.circle(coachX + 0.5, coachY + 0.5, avatarRadius + 0.5, 'F');
        await addPlayerImageToCircle(doc, coachImage, coachX, coachY, avatarRadius, 'coach');
      } else {
        doc.setFillColor(255, 255, 255);
        doc.circle(coachX, coachY, avatarRadius + 0.5, 'F');
        doc.setFillColor(0, 0, 0, 0.1);
        doc.circle(coachX + 0.5, coachY + 0.5, avatarRadius + 0.5, 'F');
        doc.setFillColor(108, 117, 125);
        doc.circle(coachX, coachY, avatarRadius, 'F');
      }
    } catch {
      doc.setFillColor(255, 255, 255);
      doc.circle(coachX, coachY, avatarRadius + 0.5, 'F');
      doc.setFillColor(0, 0, 0, 0.1);
      doc.circle(coachX + 0.5, coachY + 0.5, avatarRadius + 0.5, 'F');
      doc.setFillColor(108, 117, 125);
      doc.circle(coachX, coachY, avatarRadius, 'F');
    }

    doc.setFontSize(8);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(0, 0, 0);
    doc.text(coachName, coachX, coachY + avatarRadius + 5, { align: 'center' });
  }
}

async function addPlayerImageToCircle(
  doc: any,
  imageUrl: string,
  centerX: number,
  centerY: number,
  radius: number,
  positionType: string
): Promise<void> {
  try {
    // Carregar a imagem
    const img = new Image();
    img.crossOrigin = 'anonymous';

    await new Promise((resolve, reject) => {
      img.onload = resolve;
      img.onerror = reject;
      img.src = imageUrl;
    });

    // Criar canvas temporário para processar a imagem com alta resolução
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) throw new Error('Não foi possível criar contexto do canvas');

    // Usar resolução maior para melhor qualidade (4x o tamanho final)
    const scaleFactor = 4;
    const size = radius * 2 * scaleFactor;
    canvas.width = size;
    canvas.height = size;

    // Configurar contexto para melhor qualidade
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';

    // Criar clipping circular para deixar a imagem redonda (igual ao Avatar da página)
    ctx.save();
    ctx.beginPath();
    ctx.arc(size / 2, size / 2, size / 2, 0, Math.PI * 2);
    ctx.clip();

    // Desenhar a imagem redimensionada e centralizada com alta qualidade
    const aspectRatio = img.width / img.height;
    let drawWidth = size;
    let drawHeight = size;
    let drawX = 0;
    let drawY = 0;

    if (aspectRatio > 1) {
      drawWidth = size;
      drawHeight = size / aspectRatio;
      drawY = (size - drawHeight) / 2;
    } else {
      drawHeight = size;
      drawWidth = size * aspectRatio;
      drawX = (size - drawWidth) / 2;
    }

    // Usar drawImage com alta qualidade
    ctx.drawImage(img, drawX, drawY, drawWidth, drawHeight);
    ctx.restore();

    // Converter canvas para base64 com máxima qualidade (PNG para melhor qualidade)
    const imageData = canvas.toDataURL('image/png', 1.0);

    // Adicionar ao PDF com tamanho exato do avatar (h-12 w-12 = 48px ≈ 12mm)
    const imageSize = radius * 2; // Diâmetro completo
    doc.addImage(imageData, 'PNG', centerX - radius, centerY - radius, imageSize, imageSize);

  } catch (error) {
    console.warn('Erro ao processar imagem do jogador:', error);
    throw error;
  }
}