-- Create permissions table
CREATE TABLE IF NOT EXISTS permissions (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  description TEXT NOT NULL,
  category TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_permissions_name ON permissions(name);
CREATE INDEX IF NOT EXISTS idx_permissions_category ON permissions(category);

-- Insert default permissions
INSERT INTO permissions (name, description, category)
VALUES 
  -- Dashboard permissions
  ('dashboard.view', 'Ver dashboard', 'dashboard'),
  
  -- Player permissions
  ('players.view', 'Ver jogadores', 'players'),
  ('players.view_own', 'Ver próprio perfil', 'players'),
  ('players.edit_own', 'Editar próprio perfil', 'players'),
  ('players.create', 'Criar jogadores', 'players'),
  ('players.edit', 'Editar jogadores', 'players'),
  ('players.delete', 'Excluir jogadores', 'players'),
  ('players.documents.view', 'Ver documentos de jogadores', 'players'),
  ('players.documents.verify', 'Verificar documentos de jogadores', 'players'),
  ('players.finances.view', 'Ver finanças de jogadores', 'players'),
  ('players.finances.edit', 'Editar finanças de jogadores', 'players'),
  ('players.status.update', 'Alterar status dos atletas', 'players'),
  ('players.tabs.players', 'Acessar tab Atletas', 'players'),
  ('players.tabs.inactive', 'Acessar tab Inativos', 'players'),
  ('players.tabs.cards', 'Acessar tab Cards', 'players'),
  ('players.tabs.tactics', 'Acessar tab Tática', 'players'),
  ('players.tabs.members', 'Acessar tab Membros do Clube', 'players'),
  
  -- Match permissions
  ('matches.view', 'Ver partidas', 'matches'),
  ('matches.create', 'Criar partidas', 'matches'),
  ('matches.edit', 'Editar partidas', 'matches'),
  ('matches.delete', 'Excluir partidas', 'matches'),
  ('matches.lineup', 'Gerenciar escalação', 'matches'),
  ('matches.events', 'Gerenciar eventos de partida', 'matches'),
  
  -- Training permissions
  ('trainings.view', 'Ver treinamentos', 'trainings'),
  ('trainings.create', 'Criar treinamentos', 'trainings'),
  ('trainings.edit', 'Editar treinamentos', 'trainings'),
  ('trainings.delete', 'Excluir treinamentos', 'trainings'),
  ('trainings.locations', 'Gerenciar locais de treino', 'trainings'),
  
  -- Department permissions
  ('departments.view', 'Ver departamentos', 'departments'),
  ('departments.create', 'Criar departamentos', 'departments'),
  ('departments.edit', 'Editar departamentos', 'departments'),
  ('departments.delete', 'Excluir departamentos', 'departments'),
  
  -- User permissions
  ('users.view', 'Ver usuários', 'users'),
  ('users.create', 'Criar usuários', 'users'),
  ('users.edit', 'Editar usuários', 'users'),
  ('users.delete', 'Excluir usuários', 'users'),
  ('users.remove', 'Remover usuário do clube', 'users'),
  ('users.permissions', 'Gerenciar permissões de usuários', 'users'),
  ('users.invitations.create', 'Criar convites', 'users'),
  ('users.invitations.edit', 'Editar convites', 'users'),
  ('users.invitations.delete', 'Cancelar convites', 'users'),
  ('users.tabs.invitations', 'Acessar aba Convites', 'users'),
  ('users.tabs.documents', 'Acessar aba Documentos Pendentes', 'users'),
  
  -- Settings permissions
  ('settings.view', 'Ver configurações', 'settings'),
  ('settings.edit', 'Editar configurações', 'settings'),
  
  -- Finance permissions
  ('finances.view', 'Ver finanças', 'finances'),
  ('finances.create', 'Criar transações financeiras', 'finances'),
  ('finances.edit', 'Editar transações financeiras', 'finances'),
  ('finances.delete', 'Excluir transações financeiras', 'finances'),
  
  -- Medical permissions
  ('medical.view', 'Ver área médica', 'medical'),
  ('medical.create', 'Criar registros médicos', 'medical'),
  ('medical.edit', 'Editar registros médicos', 'medical'),
  ('medical.delete', 'Excluir registros médicos', 'medical'),
  
  -- Agenda permissions
  ('agenda.view', 'Ver agenda', 'agenda'),
  ('agenda.create', 'Criar eventos na agenda', 'agenda'),
  ('agenda.edit', 'Editar eventos na agenda', 'agenda'),
  ('agenda.delete', 'Excluir eventos na agenda', 'agenda'),
  
  -- Category permissions
  ('categories.view', 'Ver categorias', 'categories'),
  ('categories.create', 'Criar categorias', 'categories'),
  ('categories.edit', 'Editar categorias', 'categories'),
  ('categories.delete', 'Excluir categorias', 'categories'),
  
  -- Accommodation permissions
  ('accommodations.view', 'Ver alojamentos', 'accommodations'),
  ('accommodations.create', 'Criar alojamentos', 'accommodations'),
  ('accommodations.edit', 'Editar alojamentos', 'accommodations'),
  ('accommodations.delete', 'Excluir alojamentos', 'accommodations'),
  
  -- Callup permissions
  ('callups.view', 'Ver convocações', 'callups'),
  ('callups.create', 'Criar convocações', 'callups'),
  ('callups.edit', 'Editar convocações', 'callups'),
  ('callups.delete', 'Excluir convocações', 'callups'),
  
  -- Report permissions
  ('reports.view', 'Ver relatórios', 'reports'),
  ('reports.generate', 'Gerar relatórios', 'reports'),
  
  -- Statistics permissions
  ('statistics.view', 'Ver estatísticas', 'statistics'),
  
  -- Analytics permissions
  ('analytics.view', 'Ver analytics', 'analytics'),
  
  -- Communication permissions
  ('communication.view', 'Ver comunicações', 'communication'),
  ('communication.send', 'Enviar comunicações', 'communication'),
  
  -- Audit permissions
  ('audit_logs.view', 'Ver logs de auditoria', 'audit'),
  ('audit_logs.export', 'Exportar logs de auditoria', 'audit'),
  
  -- President permissions
  ('president.club_ownership', 'Propriedade do clube', 'president'),
  ('president.financial_approval', 'Aprovação financeira', 'president'),
  
  -- Medical professional permissions
  ('medical_professionals.view', 'Ver profissionais médicos', 'medical'),
  ('medical_professionals.create', 'Criar profissionais médicos', 'medical'),
  ('medical_professionals.edit', 'Editar profissionais médicos', 'medical'),
  ('medical_professionals.delete', 'Excluir profissionais médicos', 'medical'),
  ('medical_professionals.edit_own', 'Editar próprio perfil médico', 'medical'),
  -- Medical availability permissions
  ('medical.availability.view', 'Ver disponibilidade médica', 'medical'),
  ('medical.availability.create', 'Criar disponibilidade médica', 'medical'),
  ('medical.availability.edit', 'Editar disponibilidade médica', 'medical'),
  ('medical.availability.delete', 'Excluir disponibilidade médica', 'medical'),

  -- Evaluation permissions
  ('evaluation.tabs.players', 'Acessar aba Atletas em Pré Cadastro', 'evaluation'),
  ('evaluation.tabs.invitations', 'Acessar aba Convites', 'evaluation'),
  ('evaluation.tabs.new', 'Acessar aba Novo Convite', 'evaluation'),
  ('evaluation.tabs.dashboard', 'Acessar aba Dashboard', 'evaluation'),
  ('evaluation.invitations.create', 'Criar convites de pré cadastro', 'evaluation'),
  ('evaluation.invitations.copy', 'Copiar link de convite', 'evaluation'),
  ('evaluation.invitations.resend', 'Reenviar convite de pré cadastro', 'evaluation'),
  ('evaluation.invitations.delete', 'Excluir convites de pré cadastro', 'evaluation'),
  ('evaluation.players.edit', 'Editar atletas em pré cadastro', 'evaluation'),
  ('evaluation.players.delete', 'Excluir atletas em pré cadastro', 'evaluation'),
  ('evaluation.players.schedule', 'Agendar pré cadastro de atleta', 'evaluation'),
  ('evaluation.players.update_status', 'Atualizar status do pré cadastro', 'evaluation'),
  ('evaluation.players.verify_documents', 'Verificar documentos do pré cadastro', 'evaluation')
ON CONFLICT (name) DO NOTHING;
