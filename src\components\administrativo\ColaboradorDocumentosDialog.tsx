import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { DocumentUpload } from "@/components/ui/document-upload";
import { Badge } from "@/components/ui/badge";
import { toast } from "@/components/ui/use-toast";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Loader2, AlertCircle } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { Collaborator } from "@/api/api";
import { useUser } from "@/context/UserContext";
import { v4 as uuidv4 } from "uuid";
import {
  COLLABORATOR_DOCUMENT_TYPES,
  COLLABORATOR_DOCUMENT_LABELS,
} from "@/api/api";

// Tipo para documentos de colaborador
interface CollaboratorDocument {
  id: number;
  club_id: number;
  collaborator_id: number;
  document_type: string;
  file_url: string;
  status: string;
  uploaded_at: string;
  verified_at?: string;
  verified_by?: string;
  rejection_reason?: string;
  verifier_name?: string;
}

interface ColaboradorDocumentosDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clubId: number;
  collaborator: Collaborator;
}

export function ColaboradorDocumentosDialog({
  open,
  onOpenChange,
  clubId,
  collaborator
}: ColaboradorDocumentosDialogProps) {
  const { user } = useUser();
  const [documents, setDocuments] = useState<CollaboratorDocument[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);
  const [viewDocument, setViewDocument] = useState<string | null>(null);
  const [documentToDelete, setDocumentToDelete] = useState<CollaboratorDocument | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleting, setDeleting] = useState(false);

  // Carregar documentos quando o diálogo for aberto
  useEffect(() => {
    if (open && collaborator) {
      fetchDocuments();
    }
  }, [open, collaborator]);

  // Função para buscar documentos
  const fetchDocuments = async () => {
    try {
      setLoading(true);
      setError(null);

      // Buscar documentos diretamente do Supabase usando SQL
      const { data, error } = await supabase.rpc('get_collaborator_documents', {
        p_club_id: clubId,
        p_collaborator_id: collaborator.id
      });

      if (error) {
        throw new Error(`Erro ao buscar documentos: ${error.message}`);
      }

      // Converter os dados para o formato esperado
      const docs = Array.isArray(data) ? data.map(doc => ({
        ...doc,
        verifier_name: null // Simplificando para evitar problemas de tipagem
      })) : [];

      setDocuments(docs as CollaboratorDocument[]);
    } catch (err: any) {
      console.error("Erro ao buscar documentos:", err);
      setError(err.message || "Erro ao buscar documentos");
      toast({
        title: "Erro",
        description: err.message || "Erro ao buscar documentos",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Função para fazer upload de documento
  const handleUploadDocument = async (file: File, documentType: string) => {
    if (!user) return false;

    try {
      setUploading(true);

      // 1. Fazer upload do arquivo para o Storage
      const fileExt = file.name.split(".").pop();
      const fileName = `${documentType}-${uuidv4()}.${fileExt}`;
      const filePath = `${clubId}/collaborators/${collaborator.id}/${fileName}`;

      // Upload do arquivo
      const { error: uploadError } = await supabase.storage
        .from("playerdocuments") // Usando o mesmo bucket dos documentos de jogadores
        .upload(filePath, file, {
          cacheControl: "3600",
          upsert: true,
          contentType: file.type,
        });

      if (uploadError) {
        throw new Error(`Erro ao fazer upload do documento: ${uploadError.message}`);
      }

      // Obter URL pública
      const { data: urlData } = supabase.storage
        .from("playerdocuments")
        .getPublicUrl(filePath);

      // 2. Registrar o documento no banco de dados usando SQL direto para evitar problemas de tipagem
      const { error: insertError } = await supabase.rpc('insert_collaborator_document', {
        p_club_id: clubId,
        p_collaborator_id: collaborator.id,
        p_document_type: documentType,
        p_file_url: urlData.publicUrl,
        p_status: 'pending'
      });

      if (insertError) {
        throw new Error(`Erro ao registrar documento: ${insertError.message}`);
      }

      // Recarregar documentos
      await fetchDocuments();

      toast({
        title: "Sucesso",
        description: "Documento enviado com sucesso",
      });
    } catch (err: any) {
      console.error("Erro ao enviar documento:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao enviar documento",
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  // Função para iniciar o processo de exclusão de documento
  const handleDeleteDocument = (doc: CollaboratorDocument) => {
    setDocumentToDelete(doc);
    setDeleteDialogOpen(true);
  };

  // Função para confirmar a exclusão do documento
  const confirmDeleteDocument = async () => {
    if (!documentToDelete) return;

    try {
      setDeleting(true);

      // 1. Extrair o caminho do arquivo da URL
      const fileUrl = documentToDelete.file_url;
      const storageUrl = supabase.storage.from('playerdocuments').getPublicUrl('').data.publicUrl;
      const filePath = fileUrl.replace(storageUrl, '');

      // 2. Excluir o arquivo do Storage
      if (filePath) {
        const { error: storageError } = await supabase.storage
          .from('playerdocuments')
          .remove([filePath]);

        if (storageError) {
          console.error("Erro ao excluir arquivo do storage:", storageError);
          // Continuar mesmo com erro no storage, para pelo menos remover o registro do banco
        }
      }

      // 3. Excluir o registro do banco de dados usando SQL direto para evitar problemas de tipagem
      const { error: dbError } = await supabase.rpc('delete_collaborator_document', {
        p_document_id: documentToDelete.id
      });

      if (dbError) {
        throw new Error(`Erro ao excluir documento: ${dbError.message}`);
      }

      // 4. Atualizar a lista de documentos
      setDocuments(documents.filter(d => d.id !== documentToDelete.id));

      toast({
        title: "Sucesso",
        description: "Documento excluído com sucesso",
      });
    } catch (err: any) {
      console.error("Erro ao excluir documento:", err);
      toast({
        title: "Erro",
        description: err.message || "Erro ao excluir documento",
        variant: "destructive",
      });
    } finally {
      setDeleting(false);
      setDeleteDialogOpen(false);
      setDocumentToDelete(null);
    }
  };

  // Função para renderizar o status do documento
  const renderDocumentStatus = (status: string) => {
    switch (status) {
      case "verified":
        return <Badge className="bg-green-100 text-green-800">Verificado</Badge>;
      case "rejected":
        return <Badge variant="destructive">Rejeitado</Badge>;
      default:
        return <Badge variant="outline">Pendente</Badge>;
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Documentos de {collaborator.full_name}</DialogTitle>
          </DialogHeader>

          {error && (
            <div className="bg-red-50 text-red-700 p-3 rounded-md text-sm mb-4">
              {error}
            </div>
          )}

          <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {COLLABORATOR_DOCUMENT_TYPES.map((type) => {
              const doc = documents.find((d) => d.document_type === type);
              return (
                <DocumentUpload
                  key={type}
                  documentType={type}
                  documentLabel={COLLABORATOR_DOCUMENT_LABELS[type]}
                  value={doc?.file_url}
                  status={doc ? (doc.status as any) : "missing"}
                  onChange={async (value, file) => {
                    if (file) {
                      await handleUploadDocument(file, type);
                    } else if (value === null && doc) {
                      handleDeleteDocument(doc);
                    }
                  }}
                  onView={() => doc && setViewDocument(doc.file_url)}
                  uploading={uploading}
                  required
                />
              );
            })}
          </div>

      {loading && (
        <div className="text-center py-4">
          <Loader2 className="h-6 w-6 animate-spin mx-auto" />
        </div>
      )}
      </div>

      <Dialog open={!!viewDocument} onOpenChange={() => setViewDocument(null)}>
        <DialogContent className="max-w-4xl max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>Visualizar Documento</DialogTitle>
          </DialogHeader>
          {viewDocument && (
            <div className="mt-4 flex flex-col">
              {(() => {
                const currentDoc = documents.find(doc => doc.file_url === viewDocument);
                if (currentDoc && currentDoc.status === "rejected" && currentDoc.rejection_reason) {
                  return (
                    <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                      <p className="font-medium text-red-700">Documento Rejeitado</p>
                      <p className="text-red-600 mt-1">
                        <span className="font-medium">Motivo da rejeição:</span> {currentDoc.rejection_reason}
                      </p>
                    </div>
                  );
                }
                return null;
              })()}

              <div className="mb-4 w-full flex justify-end">
                <Button variant="outline" onClick={() => window.open(viewDocument!, '_blank')} className="mb-2">
                  Abrir no Navegador
                </Button>
              </div>

              <div className="w-full overflow-hidden" style={{ maxHeight: '60vh' }}>
                {viewDocument.endsWith('.pdf') ? (
                  <iframe
                    src={`https://docs.google.com/viewer?url=${encodeURIComponent(viewDocument)}&embedded=true`}
                    className="w-full h-[60vh] border-0"
                    title="Documento"
                    allowFullScreen
                  />
                ) : (
                  <img
                    src={viewDocument}
                    alt="Documento"
                    className="max-h-[60vh] object-contain mx-auto"
                    style={{ pointerEvents: 'none', display: 'block' }}
                  />
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

          <DialogFooter>
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Fechar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo de confirmação para exclusão de documento */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Excluir documento</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir este documento? Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={deleting}>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                confirmDeleteDocument();
              }}
              disabled={deleting}
              className="bg-red-600 hover:bg-red-700"
            >
              {deleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Excluindo...
                </>
              ) : (
                'Excluir'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}