-- <PERSON><PERSON><PERSON> para corrigir e criar tabelas do sistema de escalação
-- Execute este script no Supabase SQL Editor

-- 1. Verificar e criar tabela match_lineups
CREATE TABLE IF NOT EXISTS match_lineups (
  id SERIAL PRIMARY KEY,
  club_id INTEGER NOT NULL,
  match_id UUID NOT NULL,
  formation TEXT NOT NULL DEFAULT '4-4-2',
  lineup JSONB NOT NULL DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(club_id, match_id)
);

-- 2. Verificar e criar tabela match_squad
CREATE TABLE IF NOT EXISTS match_squad (
  id SERIAL PRIMARY KEY,
  club_id INTEGER NOT NULL,
  match_id UUID NOT NULL,
  player_id UUID,
  user_id UUID,
  role TEXT NOT NULL,
  position TEXT,
  jersey_number INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Garan<PERSON><PERSON> que cada pessoa só pode ter um papel por partida
  UNIQUE(club_id, match_id, player_id),
  UNIQUE(club_id, match_id, user_id)
);

-- 3. Verificar e criar tabela match_substitutions
CREATE TABLE IF NOT EXISTS match_substitutions (
  id SERIAL PRIMARY KEY,
  club_id INTEGER NOT NULL,
  match_id UUID NOT NULL,
  player_out_id UUID NOT NULL,
  player_in_id UUID NOT NULL,
  minute INTEGER NOT NULL,
  reason TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Verificar e criar tabela match_player_minutes
CREATE TABLE IF NOT EXISTS match_player_minutes (
  id SERIAL PRIMARY KEY,
  club_id INTEGER NOT NULL,
  match_id UUID NOT NULL,
  player_id UUID NOT NULL,
  minutes_played INTEGER NOT NULL DEFAULT 0,
  started BOOLEAN NOT NULL DEFAULT FALSE,
  substituted_in_minute INTEGER,
  substituted_out_minute INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(club_id, match_id, player_id)
);

-- 5. Criar índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_match_lineups_club_match ON match_lineups(club_id, match_id);
CREATE INDEX IF NOT EXISTS idx_match_squad_club_match ON match_squad(club_id, match_id);
CREATE INDEX IF NOT EXISTS idx_match_squad_role ON match_squad(club_id, match_id, role);
CREATE INDEX IF NOT EXISTS idx_match_substitutions_match ON match_substitutions(club_id, match_id);
CREATE INDEX IF NOT EXISTS idx_match_player_minutes_match ON match_player_minutes(club_id, match_id);

-- 6. Criar função para atualizar timestamp
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = NOW();
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 7. Criar triggers para atualizar timestamps
DROP TRIGGER IF EXISTS update_match_lineups_modtime ON match_lineups;
CREATE TRIGGER update_match_lineups_modtime 
BEFORE UPDATE ON match_lineups 
FOR EACH ROW EXECUTE FUNCTION update_modified_column();

DROP TRIGGER IF EXISTS update_match_player_minutes_modtime ON match_player_minutes;
CREATE TRIGGER update_match_player_minutes_modtime 
BEFORE UPDATE ON match_player_minutes 
FOR EACH ROW EXECUTE FUNCTION update_modified_column();

-- 8. Habilitar RLS (Row Level Security) nas tabelas
ALTER TABLE match_lineups ENABLE ROW LEVEL SECURITY;
ALTER TABLE match_squad ENABLE ROW LEVEL SECURITY;
ALTER TABLE match_substitutions ENABLE ROW LEVEL SECURITY;
ALTER TABLE match_player_minutes ENABLE ROW LEVEL SECURITY;

-- 9. Criar políticas RLS básicas
-- Política para match_lineups
DROP POLICY IF EXISTS "Club members can manage their own match lineups" ON match_lineups;
CREATE POLICY "Club members can manage their own match lineups"
  ON match_lineups
  FOR ALL
  USING (
    club_id IN (
      SELECT club_id FROM users WHERE id = auth.uid()
    )
  );

-- Política para match_squad
DROP POLICY IF EXISTS "Club members can manage their own match squad" ON match_squad;
CREATE POLICY "Club members can manage their own match squad"
  ON match_squad
  FOR ALL
  USING (
    club_id IN (
      SELECT club_id FROM users WHERE id = auth.uid()
    )
  );

-- Política para match_substitutions
DROP POLICY IF EXISTS "Club members can manage their own match substitutions" ON match_substitutions;
CREATE POLICY "Club members can manage their own match substitutions"
  ON match_substitutions
  FOR ALL
  USING (
    club_id IN (
      SELECT club_id FROM users WHERE id = auth.uid()
    )
  );

-- Política para match_player_minutes
DROP POLICY IF EXISTS "Club members can view their own match player minutes" ON match_player_minutes;
CREATE POLICY "Club members can view their own match player minutes"
  ON match_player_minutes
  FOR ALL
  USING (
    club_id IN (
      SELECT club_id FROM users WHERE id = auth.uid()
    )
  );

-- 10. Verificar se as tabelas foram criadas
SELECT 
  schemaname,
  tablename,
  tableowner
FROM pg_tables 
WHERE tablename IN ('match_lineups', 'match_squad', 'match_substitutions', 'match_player_minutes')
ORDER BY tablename;
